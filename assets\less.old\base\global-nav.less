/* This Source Code Form is subject to the terms of the Mozilla Public
* License, v. 2.0. If a copy of the MPL was not distributed with this
* file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import "../sandstone/lib.less";

/* -------------------------------------------------------------------------- */
// Horizontal top navigation

.moz-global-nav {
    background: #fff;
    margin: 0 auto;
    max-width: @widthMaxContent;
    overflow: hidden;
    position: relative;

    a:link,
    a:visited {
        color: #000;
        font-weight: bold;
        text-decoration: none;
    }

    a:hover,
    a:focus,
    a:active {
        color: #000;
        text-decoration: underline;
        transition: color 0.1s ease-in-out;
    }

    // Three-dotted nenu button
    .nav-button-menu {
        -moz-appearance: none;
        -webkit-appearance: none;
        appearance: none;
        background: transparent;
        border: none;
        cursor: pointer;
        float: left;
        font-size: 0;
        height: 25px;
        margin: 13px 0;
        padding: 0;
        width: 50px;

        .p1,
        .p2,
        .p3 {
            transition: transform .2s ease-in-out;
        }

        &:hover,
        &:active {
            .p1 {
                transform: translateX(4px);
            }

            .p2 {
                transform: translateX(-3px);
            }

            .p3 {
                transform: translateX(4px);
            }
        }

        &:focus {
            outline: none;

            .p1,
            .p2,
            .p3 {
                fill: #646464;
            }
        }

        &::-moz-focus-inner {
            border: none;
        }

        &.nav-hidden {
            display: none;
        }
    }

    // Mozilla wordmark logo
    .nav-logo {
        .font-size(15px);
        float: left;
        height: 25px;
        margin: 0 0 0 17px;
        padding: 13px 0;

        a {
            .image-replaced;
            background: url('/media/img/pebbles/moz-wordmark-dark-reverse.svg') top left no-repeat;
            display: block;
            width: 78px;
            height: 25px;
        }

        @media only screen and (max-width: @breakNavDesktop) {
            margin-left: 5px;

            a {
                .background-size(25px 25px);
                background-image: url('/media/img/favicon/favicon-196x196.png');
                width: 25px;
            }
        }
    }

    // Horizontal link menu container
    .nav-horizontal-menu {
        .clearfix;
        margin-left: 17px;
        padding: 25px 0 10px;
        position: relative;
        overflow: hidden;

        @media only screen and (max-width: @breakNavTablet) {
            padding-top: 10px;
        }

        @media only screen and (max-width: @breakNavDesktop) {
            margin-left: 5px;
        }
    }

    // Primary horizontal links
    .nav-primary-links {
        .font-size(16px);
        display: block;
        float: left;
        list-style-type: none;
        margin: 0 0 0 21px;
        padding: 13px 0;

        &> li {
            display: inline-block;
            padding: 0 21px;
            margin: 0;

            a:hover,
            a:active,
            a:focus {
                text-decoration: none;
            }

            a:focus {
                outline: none;
            }
        }

        @media only screen and (max-width: @breakNavDesktop) {
            margin-left: 10px;

            &> li {
                padding: 0 6px;
            }
        }

        @media only screen and (max-width: @breakNavTablet) {
            display: none;
        }
    }

    // Class for legacy browser support
    &.simple {
        .nav-primary-links {
            display: block;
        }
    }
}

// Menu button animation open state
.moz-nav-open .moz-global-nav .nav-button-menu {
    .p1 {
        transform: translateX(4px);
    }

    .p2 {
        transform: translateX(-3px);
    }

    .p3 {
        transform: translateX(4px);
    }
}

/* -------------------------------------------------------------------------- */
// Vertical side navigation drawer

.moz-global-nav-drawer {
    background: #000;
    bottom: 0;
    color: #fff;
    left: -320px;
    padding: 20px;
    position: absolute;
    top: 0;
    width: 280px;
    z-index: 1000;

    a:link,
    a:visited {
        transition: color 0.1s ease-in-out;
        color: #fff;
        text-decoration: none;
    }

    a:hover,
    a:active,
    a:focus {
        transition: color 0.1s ease-in-out;
    }

    a:focus {
        outline: none;
    }

    abbr[title] {
         text-decoration: none;
    }

    .nav-drawer-close-button {
        -moz-appearance: none;
        -webkit-appearance: none;
        .font-size(13px);
        background-color: #000;
        border: none;
        cursor: pointer;
        height: 22px;
        margin: 30px 0 0 22px;
        padding: 0;
        text-transform: uppercase;

        .rect {
            transform-origin: center center;
            transition: transform .12s ease-in-out;
            transform: translate(0, 0) rotate(45deg);
        }

        .center {
            transform: rotate(45deg);
        }

        &:hover,
        &:active,
        &:focus {
            .top-left {
                transform: translate(-7px, -7px) rotate(45deg);
            }

            .top-right {
                transform: translate(7px, -7px) rotate(45deg);
            }

            .bottom-left {
                transform: translate(-7px, 7px) rotate(45deg);
            }

            .bottom-right {
                transform: translate(7px, 7px) rotate(45deg);
            }
        }

        @media only screen and (max-width: @breakNavDesktop) {

            .rect {
                transition: none;
            }

            .top-left {
                transform: translate(-7px, -7px) rotate(45deg);
            }

            .top-right {
                transform: translate(7px, -7px) rotate(45deg);
            }

            .bottom-left {
                transform: translate(-7px, 7px) rotate(45deg);
            }

            .bottom-right {
                transform: translate(7px, 7px) rotate(45deg);
            }
        }

        &:focus {
            outline: none;
        }

        &::-moz-focus-inner {
            border: none;
        }
    }

    .nav-menu-inner-container {
        margin-top: 42px;
        position: relative;
    }

    .nav-menu-scroll-pane {
        padding: 0 22px;
        width: 234px;
    }

    .nav-menu-primary-links {
        padding-bottom: 40px;
        list-style-type: none;

        &> li {
            margin: 20px 0;
        }
    }

    .intro {
        .font-size(13px);
        margin: 0 0 20px;
    }

    .summary {
        .font-size(18px);
        padding-bottom: 20px;
        text-shadow: none;
        font-weight: bold;

        a:link,
        a:visited {
            position: relative;
            display: block;

            &:after {
                background-image: url('/media/img/nav/subnav-expand.svg');
                content: '';
                height: 12px;
                position: absolute;
                right: 0;
                top: 0;
                transition: transform .2s ease-in-out;
                width: 12px;
            }
        }

        &.selected {
            a:link:after,
            a:visited:after {
                transform: rotate(45deg);
            }
        }
    }

    .detail-container {
        border-top: 1px solid #fff;
        border-bottom: 1px solid #fff;
        padding-top: 20px;
    }

    .nav-menu-secondary-links > li {
        .font-size(16px);
        margin: 20px 0;

        a:link,
        a:visited {
            display: block;
        }
    }
}

/* -------------------------------------------------------------------------- */
// Common color styles for both primary and side navigation links & headings

.moz-global-nav,
.moz-global-nav-drawer {

    .item-firefox {
        a:hover,
        a:active,
        a:focus,
        a.selected {
            color: @colorFirefoxLightOrange;
        }
    }

    .item-internet-health {
        a:hover,
        a:active,
        a:focus,
        a.selected {
            color: @colorBrandCyan;
        }
    }

    .item-technology {
        a:hover,
        a:active,
        a:focus,
        a.selected {
            color: @colorBrandLilac;
        }
    }

    .item-about-us {
        a:hover,
        a:active,
        a:focus,
        a.selected {
            color: @colorBrandLime;
        }
    }

    .item-get-involved {
        a:hover,
        a:active,
        a:focus,
        a.selected {
            color: @colorBrandCoral;
        }
    }
}

body[data-global-nav-current-link="firefox"] .nav-primary-links .item-firefox {
    &> a:link,
    &> a:visited {
        color: @colorFirefoxLightOrange;
    }
}

body[data-global-nav-current-link="internet-health"] .item-internet-health {
    &> a:link,
    &> a:visited {
        color: @colorBrandCyan;
    }
}

body[data-global-nav-current-link="technology"] .item-technology {
    &> a:link,
    &> a:visited {
        color: @colorBrandLilac;
    }
}

body[data-global-nav-current-link="about-us"] .item-about-us {
    &> a:link,
    &> a:visited {
        color: @colorBrandLime;
    }
}

body[data-global-nav-current-link="get-involved"] .item-get-involved {
    &> a:link,
    &> a:visited {
        color: @colorBrandCoral;
    }
}

/* -------------------------------------------------------------------------- */
// Semi-opaque mask shown when side drawer is open.

.moz-global-nav-page-mask {
    background: #000;
    bottom: auto;
    left: 0;
    opacity: 0;
    position: absolute;
    right: auto;
    top: 0;
    transition: opacity 0.2s ease-in-out;
    z-index: 10000;
}

html.moz-nav-open .moz-global-nav-page-mask {
    bottom: 0;
    opacity: 0.3;
    right: 0;
    transition: opacity 0.2s ease-in-out;
}

/* -------------------------------------------------------------------------- */
// Customized Firefox download button for primary navigation.

#global-nav-download-firefox {
    float: right;
    margin: -2px 85px 0 0;

    .download-list {
        margin: 0;

        &> li {
            margin: 0;
        }
    }

    .download-link:link,
    .download-link:visited {
        .font-size(14px);
        background: inherit;
        border-radius: 0;
        border: none;
        color: #646464;
        display: inline-block;
        padding: 14px 10px;
        text-decoration: none;
        transition: color 0.1s ease-in-out, background-color 0.1s ease-in-out;

        .download-title {
            font-weight: bold;

            &:before {
                .font-size(16px);
                color: @colorFirefoxLightOrange;
                content: "\2193\00A0"; // downward-arrow+space
                transition: color 0.1s ease-in-out;
                white-space: nowrap;
            }
        }

        &:hover,
        &:focus,
        &:active {
            background-color: @colorFirefoxLightOrange;
            color: #fff;
            transition: color 0.1s ease-in-out, background-color 0.1s ease-in-out;

            .download-title:before {
                color: #fff;
                transition: color 0.1s ease-in-out;
            }
        }
    }

    @media only screen and (max-width: @breakNavDesktop) {
        margin-right: 60px;

        .download-link {
            &:link,
            &:visited {
                padding: 14px 10px;
            }
        }
    }

    @media only screen and (max-width: @breakNavTablet) {
        margin-right: 20px;
    }

    .fx-privacy-link {
        display: none;
    }
}

.other #global-nav-download-firefox,
.oldwin #global-nav-download-firefox,
.oldmac #global-nav-download-firefox {
    display: none;
}

/* -------------------------------------------------------------------------- */
// Side drawer state rules.

// Default (non-animated) accordion for old browsers.
.moz-global-nav-drawer {

    .detail {
        display: none;
    }

    .summary.selected + .detail {
        display: block;
    }
}

// Animated accordion for newer browsers.
@supports (transition: max-height .6s ease-in-out, visibility 0s 0s) {

    .moz-global-nav-drawer {

        .detail {
            display: block;
            max-height: 0;
            overflow: hidden;
            visibility: hidden;
            transition: max-height .3s ease-in-out, visibility 0s .8s;
        }

        .summary.selected + .detail {
            max-height: 1000px;
            visibility: visible;
            transition: max-height .6s ease-in-out, visibility 0s 0s;
        }
    }
}

// Default (non-animated) drawer for old browsers.
body {
    overflow-x: hidden;
}

html.moz-nav-open {
    body {
        left: 320px;
        position: relative;
    }
}

// Animated drawer for newer browsers.
@supports (transform: translateX(320px)) {
    body {
        position: static;
        transition: transform .25s ease-in-out;
    }

    html.moz-nav-open {
        height: 100%;

        body {
            height: 100%;
            left: 0;
            overflow: hidden;
            transform: translateX(320px);
            transition: transform .25s ease-in-out;
        }

        .moz-global-nav-drawer {
            transition: visibility 0s 0s;
            visibility: visible;
        }
    }

    .moz-global-nav-drawer {
        bottom: auto;
        height: 100%;
        left: 0;
        transform: translateX(-320px);
        transition: visibility 0s .4s;
        visibility: hidden;

        .nav-menu-inner-container {
            margin-top: 22px;
            height: 100%;
        }

        .nav-menu-scroll-pane {
            bottom: 100px;
            overflow-y: scroll;
            position: absolute;
            top: 0;
            width: 254px;

            &::-webkit-scrollbar {
                width: 8px;
                height: 100%;
            }

            &::-webkit-scrollbar-track {
                background: #000;
            }

            &::-webkit-scrollbar-thumb {
                width: 8px;
                height: 8px;
                background: #999;
                border-radius: 8px;
            }
        }

        .nav-menu-primary-links {
            padding-right: 20px;
        }
    }

    @media only screen and (max-width: @breakNavDesktop) {
        html.moz-nav-open {
            height: auto;

            body {
                height: auto;
                overflow-x: hidden;
                overflow-y: initial;
            }
        }

        .moz-global-nav-drawer {
            bottom: 0;
            height: auto;

            .nav-menu-inner-container {
                height: auto;
            }

            .nav-menu-scroll-pane {
                position: static;
                overflow-y: visible;
                width: 234px;
            }

            .nav-menu-primary-links {
                padding-right: 0;
            }
        }
    }
}

/* -------------------------------------------------------------------------- */
// Side drawer promo thumbnails.

.moz-global-nav-drawer {

    .nav-promo {
        text-align: center;
        width: 216px;

        .thumbnail {
            border: 1px solid #fff;
            display: block;
            height: 146px;
            margin-bottom: 20px;
            padding: 5px 0 20px;
            position: relative;
            width: 214px;
        }

        img {
            display: inline;
            height: auto;
            max-width: 100%;
        }

        figcaption {
            .font-size(14px);
            background-color: #000;
            bottom: 0;
            color: inherit;
            left: 0;
            line-height: 1.3;
            margin: 0;
            padding: 5px;
            position: absolute;
            text-align: left;
        }

        .meta {
            .font-size(12px);
            color: #fff;
            display: block;
            padding: 0 0 5px 5px;
            text-align: left;
        }
    }

    // only show promos on taller desktop viewports
    @media only screen and (max-width: @breakNavTablet) {
        .nav-promo {
            display: none;
        }
    }
}

/* -------------------------------------------------------------------------- */
// Styles for JavaScript disabled.

.no-js {
    #global-nav-download-firefox {
        display: none;
    }

    .nav-primary-links {
        display: block;
    }

    .moz-global-nav-drawer {
        display: none;
    }
}
