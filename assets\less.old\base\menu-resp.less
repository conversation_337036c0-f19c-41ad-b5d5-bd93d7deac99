// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

@import "../sandstone/lib.less";

/* Top Level Menu Items */

#nav-main-menu {
    .inline-block;
    margin: 0;

    li {
        position: relative;

        &.hover a {
            outline: 0;

            &[aria-haspopup="true"] {
                background-image: url("/media/img/sandstone/menu-point.png");
                background-repeat: no-repeat;
                background-position: center 2.75em;
            }
        }
    }

}

/* Second-level Menu Items */

// Get rid of uppercasing (specified in sandstone-resp.less)
#masthead nav .sublink {
    text-transform: none;
}

.js #nav-main li.hover .submenu {
    left: 0;
    opacity: 1;
    -moz-transition: opacity 0.2s ease-in-out;
    -webkit-transition: opacity 0.2s ease-in-out;
    transition: opacity 0.2s ease-in-out;
}

#nav-main .submenu,
#nav-main .submenu li {
    height: auto;
    border: 0;
    float: none;
    display: block;
}

#nav-main .submenu {
    position: absolute;
    z-index: 100;
    left: -999em;
    top: 43px;
    opacity: 0;
    width: 190px;
    text-shadow: 1px 1px 0 #fff, 0 0 5px #fff;
    background: #fff;
    padding: 1px 0;
    border-bottom: 1px solid #ddd;

    li,
    li.hover {
        background-image: none;

        a,
        a:link,
        a:visited,
        span {
            overflow: hidden;
            padding: 7px 10px;
            margin: 0;
            height: auto;
            float: none;
            display: block;
            .font-size(11px);
            border: 0;
            background: none;
            position: relative;
            -moz-transition: background 0.1s ease-in;
            -webkit-transition: background 0.1s ease-in;
            transition: background 0.1s ease-in;
        }

        a,
        a:hover,
        a:focus,
        a:active {
            background: rgb(227,235,244);
            background: rgba(152,178,201,0.2);
            -moz-transition: background 0.1s ease-out;
            -webkit-transition: background 0.1s ease-out;
            transition: background 0.1s ease-out;
        }
    }

    hr {
        height: 5px;
        margin: 0 auto 5px;
        width: 170px;
    }
}

/* Bug 925995 */
html[dir="rtl"] #nav-main .submenu {
    left: 999em;
}

/* Currently active menu items */

#nav-mail ul li.current {
    span,
    a,
    a:link,
    a:visited {
        background: #fbfdff;
        padding-right: 30px;
        padding-bottom: 15px;
    }

    ul span,
    ul a,
    ul a:link,
    ul a:visited {
        background: none;
        height: auto;
        padding: 8px 10px;
    }
}

/* {{{ Dark Background Header */

.darkbg #nav-main ul li {

    a,
    a:link,
    a:visited,
    span {
        color: #fff;
        border-color: #555;
    }

    li a,
    li a:link,
    li a:visited,
    li span {
        color: #484848;
    }

    a:hover,
    &:hover a,
    .hover a,
    .hover a:link,
    .hover a:visited {
        background: #ccc;
        color: #484848;
    }

}

/* }}} */

/* Mobile Layout: 320px */
@media only screen and (max-width: @breakTablet) {

    #nav-main-menu {
        margin: 15px 0 0 -10px;

        li.hover a[aria-haspopup="true"] {
            background-image: none;
        }

    }

    #nav-main-menu .submenu,
    .js #nav-main li.hover .submenu {
        left: -999em;
        top: auto;
        background: #fff;
        .border-radius(10px);
        .box-shadow(0 2px 3px rgba(0,0,0,.35));
        opacity: 1;
        padding: 0;
    }

    #nav-main-menu .submenu a,
    #nav-main-menu .submenu a:link,
    #nav-main-menu .submenu a:visited {
        -moz-transition: none 0s;
        -webkit-transition: none 0s;
        transition: none 0s;
    }

}
