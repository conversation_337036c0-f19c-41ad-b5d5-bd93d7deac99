// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

@import "../sandstone/lib.less";

@font-face {
    font-family: 'Font Awesome';
    src: url('/media/fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'),
         url('/media/fonts/fontawesome-webfont.woff?v=4.7.0') format('woff');
    font-weight: normal;
    font-style: normal;
}

// Share button
.mozilla-share-cta {
    position: relative;
    width: 296px;
    background: #0c99d5;
    color: #fff;
    border: 2px solid #9ed6ee;
    border-radius: 8px;
    overflow: hidden;
    .transition(background .2s ease-in-out);

    &:hover,
    &:focus {
        background: lighten(#0c99d5, 2%);
    }

    h3 {
        display: none;
        width: 256px;
        padding: 1em 20px;
        margin: 0;
        color: #fff;
        .font-size(18px);
        text-transform: uppercase;
        border: none;
        background: none;
        text-align: center;
        visibility: visible;
        text-shadow: none;
        .open-sans;
        opacity: 1;
        .transition(opacity .2s ease-in-out);

        &:before {
            font-family: 'Font Awesome';
            content: '\f1e0\00A0\00A0';
            text-shadow: none;
        }
    }

    ul {
        display: table;
        position: relative;
        list-style-type: none;
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        visibility: visible;

        li {
            display: table-cell;
            width: 33.3%;
            margin: 0;
            padding: 0;
            text-align: center;
            vertical-align: middle;
            opacity: 1;
            .transition(opacity .2s ease-in-out);

            a {
                color: #fff;
                display: block;
                margin: 0 10px;
                .font-size(0);

                &:before {
                    font-family: 'Font Awesome';
                    .font-size(28px);
                    line-height: 56px;
                    opacity: 0.8;
                    padding-top: 1px;
                    text-shadow: none;
                    -webkit-font-smoothing: antialiased;
                    .transition(opacity .2s ease-in-out);
                }

                &.twitter:before {
                    content: '\f099\00A0';
                }

                &.facebook:before {
                    content: '\f09a\00A0';
                }

                &.g-plus:before {
                    content: '\f0d5\00A0';
                }

                &:hover,
                &:focus {
                    text-decoration: none;
                    color: #fff;

                    &:before {
                        text-shadow: none;
                        opacity: 1;
                    }
                }
            }
        }
    }

    /* Sky theme: blue buttons & transparent background */
    &.sky {
        &,
        &:hover,
        &:focus {
            background: transparent;
        }

        h3,
        ul li a,
        ul li a:hover,
        ul li a:focus {
            color: @linkBlue;
        }
    }

    /* Mini widget */
    &.mini {
        border: 0;
        border-radius: 0;
        width: 140px;

        h3 {
            padding: 15px 10px;
            width: 120px;
            height: 13px;
            .font-size(@smallFontSize);
        }

        ul li a:before {
            .font-size(18px);
            line-height: 43px;
        }
    }
}

.js .mozilla-share-cta {
    h3 {
        display: block;
        position: absolute;
        top: 0;
        left: 0;

        &.out {
            opacity: 0;
            .transition(opacity .2s ease-in-out);
        }
    }

    ul {
        visibility: hidden;

        li {
            opacity: 0;
        }

        &.in li {
            opacity: 1;
        }
    }
}

/* For the standard #masthead header, e.g. /firefox/all/ */
#masthead .mozilla-share-cta {
    position: absolute;
    top: 60px;
    right: 24px;
}

html[dir="rtl"] #masthead .mozilla-share-cta {
    right: auto;
    left: 24px;
}

/* Wide Mobile Layout: 480px */
@media only screen and (max-width: @breakTablet) {
    #masthead .mozilla-share-cta {
        right: 14px;
    }

    html[dir="rtl"] #masthead .mozilla-share-cta {
        right: auto;
        left: 14px;
    }
}

/* Mobile Layout: 320px */
@media only screen and (max-width: @breakMobileLandscape) {
    #masthead .mozilla-share-cta {
        display: none;
    }
}
