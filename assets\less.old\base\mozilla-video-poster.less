// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

@import "../sandstone/lib.less";

.moz-video-button {
    display: none;
}

.moz-video-container {
    position: relative;
    width: 100%;
    margin-bottom: 25px;

    &:after {
        position: absolute;
        left: 0;
        bottom: -15px;
        width: 100%;
        height: 15px;
        content: '';
        background-image: url('/media/img/sandstone/video/shadow.png');
        background-repeat: no-repeat;
        background-position: 50% 100%;
        .background-size(100% 15px);
    }
}

.js {
    .moz-video-container video {
        z-index: 0;
    }

    .moz-video-button {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        border: none;
        color: #fff;
        z-index: 1;
        cursor: pointer;
        .font-size(0);
        background-color: transparent;
        background-position: top left;
        background-repeat: no-repeat;
        -webkit-background-size: cover;
        background-size: cover;

        &:after {
            position: absolute;
            top: 50%;
            left: 50%;
            content: '';
            width: 100px;
            height: 100px;
            margin: -50px 0 0 -50px;
            background: url("/media/img/sandstone/video/play.png") top left no-repeat;
            .transition(opacity .3s);
            opacity: 0.7;
            z-index: 2;
        }

        &:hover:after,
        &:focus:after {
            opacity: 1;
        }
    }

    .supports-video {
        .moz-video-button {
            display: block;
        }
        .moz-video-container video {
            visibility: hidden;
        }
    }
}
