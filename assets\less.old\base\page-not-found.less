// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

@import "../sandstone/lib.less";

.error500#main-content {
    .span-all();
}

.moz-global-nav-drawer + #outer-wrapper {
    padding-top: @baseLine * 2;
}

#wrapper {
    .clearfix;
}

#tabzilla:before {
    background-color: #bbb;
}

#error-content {
    .span(6);
    .clearfix;
}

.download-button {
    .span-all();
    float: right;
}

.fx-privacy-link {
    a:link,
    a:visited {
        color: @linkBlue;
    }

    a:hover,
    a:active,
    a:focus {
        color: @linkBlueHover;
    }
}

/* {{{ Tablet  Layout: 768px */

@media only screen and (min-width: @widthTablet) and (max-width: @widthDesktop) {
    #error-content {
        .span_narrow(6);
    }
}

/* }}} */
/* {{{ Wide Mobile Layout: 480px */

@media only screen and (max-width: @widthTablet) {
    #error-content {
        .span-all();
    }
    .download-button {
        .span-all();
    }
}

/* }}} */
