/* This Source Code Form is subject to the terms of the Mozilla Public
* License, v. 2.0. If a copy of the MPL was not distributed with this
* file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import "../sandstone/lib.less";

/*
 * Embedded "micro" version of Send to Device form.
 * For single platform use only (e.g. either 'platform=android' or 'platform=ios')
 */
#send-to-device {
    background: #005189;
    border-radius: 6px;
    color: #fff;
    text-align: center;

    h2 {
        .font-size(24px);
        color: #fff;
        padding: 0 @baseLine @baseLine;
        text-shadow: none;
    }

    ul {
        list-style-type: none;
        margin: 0;
        padding: 0;

        li {
            margin: 0;
            padding: 0;
        }
    }

    a:link,
    a:visited {
        color: #fff;
        text-decoration: underline;
    }

    a:hover,
    a:active,
    a:focus {
        color: darken(#fff, 5%);
        text-decoration: underline;
    }

    .form-container {
        padding-top: (@baseLine * 2);
    }

    footer {
        .clearfix();
        background: #005189;
        border-radius: 0 0 6px 6px;
        padding: @baseLine (@baseLine / 2);

        ul {
            position: relative;

            li {
                display: inline-block;

                a {
                    .font-size(14px);
                    display: inline-block;
                    line-height: 1.5;
                    padding: (@baseLine / 2) (@baseLine * 2);
                    text-align: center;
                }
            }

            &.ios .google-play {
                display: none;
            }

            &.android .app-store {
                display: none;
            }
        }
    }
}

#send-to-device-form {
    padding: 0 @gridGutterWidth;
    position: relative;

    label {
        .font-size(@largeFontSize);
        display: block;
        margin-bottom: @baseLine / 2;
        text-align: center;
    }

    .inline-field {
        .clearfix();
        position: relative;
    }

    #id-input {
        .border-box();
        .font-size(@baseFontSize);
        .transition(none);
        display: block;
        height: 32px;
        line-height: 2;
        padding: 5px 10px;
        width: 100%;

        /*
         * Override CSS input invalid default styling, since the form can be re-submitted
         * after the browser performs input validation on first submission.
         */
        &:-moz-ui-invalid:not(output) {
            border-color: #D1D2D3;
            box-shadow: none;
        }

        &:-moz-ui-invalid:not(output):focus {
            border-color: #42A4E0;
            box-shadow: 0 0 0 2px rgba(73, 173, 227, 0.4);
        }
    }

    button[type="submit"] {
        .transition(background-color .1s ease-in-out);
        background-color: #0c99d5;
        display: block;
        margin: @baseLine 0 0;
        padding: 12px 5px;
        width: 100%;

        &:hover,
        &:active,
        &:focus,
        &:active {
            background-color: lighten(#0c99d5, 5%);
        }
    }

    ::-moz-placeholder {
        .open-sans;
        color: rgba(86, 86, 90, 0.8);
    }

    ::-webkit-input-placeholder {
        .open-sans;
        color: rgba(86, 86, 90, 0.8);
    }

    :-ms-input-placeholder {
        .open-sans;
        color: rgba(86, 86, 90, 0.8);
    }

    .legal {
        .font-size(13px);
        clear: both;
        margin: (@baseLine * 2) auto @baseLine;
        max-width: 38em;
    }

    .error-list {
        margin-bottom: @baseLine;
        text-align: left;

        li {
            background: #c33b32;
            border-radius: 3px;
            color: #fff;
            margin: @baseLine auto;
            padding: 10px;
        }
    }

    .email {
        display: block;
    }

    .sms {
        display: none;
    }

    &.us {
        .email {
            display: none;
        }

        .sms {
            display: block;
        }
    }

    .loading-spinner {
        bottom: 0;
        display: none;
        left: 0;
        position: absolute;
        right: 0;
        top: 0;
    }

    &.loading {
        .transition(opacity .2s ease-in-out);
        opacity: 0.2;
    }

    .thank-you {
        margin: 0 0 (@baseLine * 2);

        p {
            .font-size(20px);
            margin-bottom: @baseLine;
            padding-top: 92px;
            position: relative;

            &:before {
                .at2x('/media/img/send-to-device/device-icon.png', 72px, 72px);
                content: '';
                height: 72px;
                left: 50%;
                margin-left: -36px;
                position: absolute;
                top: 0;
                width: 72px;
            }
        }

        a {
            .font-size(@largeFontSize);
        }
    }
}
