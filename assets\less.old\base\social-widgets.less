@import "../sandstone/lib.less";

@font-face {
  font-family: 'Font Awesome';
  src: url('/media/fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'),
       url('/media/fonts/fontawesome-webfont.woff?v=4.7.0') format('woff');
  font-weight: normal;
  font-style: normal;
}

// Twitter Follow button

a.twitter-follow-button {
  .inline-block;
  border: 1px solid #CCC;
  border-radius: 3px;
  padding: 0 5px;
  color: #333;
  #gradient > .vertical(#FFFFFF, #DEDEDE);
  .font-size(@smallFontSize);
  line-height: 1.4;
  font-weight: bold;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  white-space: nowrap;

  &:hover, &:focus, &:active {
    color: #333;
    #gradient > .vertical(#F8F8F8, #D9D9D9);
    text-decoration: none;
  }

  &:focus {
    border-color: #0089CB;
    outline: none;
  }

  &:active {
    background-color: #EFEFEF;
    .box-shadow(inset 0 3px 5px rgba(0, 0, 0, .1));
  }

  &:before {
    .inline-block;
    color: #55ACEE;
    .font-size(@baseFontSize);
    font-family: 'Font Awesome', sans-serif;
    vertical-align: middle;
    content: '\0F099\00A0';
  }
}

// Twitter timeline widget

#twitter-timeline-widget {
  header {
    a.twitter-follow-button {
      float: right;
      margin: -32px 0 0;
    }
  }

  article {
    overflow: hidden;
    padding: 10px 10px 10px 68px;
    line-height: @baseLine;

    header {
      .timestamp {
        float: right;
        margin: 0 0 0 10px;
        .font-size(@smallFontSize);
        line-height: @baseLine;
        letter-spacing: 0;

        a {
          color: @textColorTertiary;
        }

        .full {
          .visually-hidden();
        }
      }
    }

    [itemprop="author"] {
      a {
        color: inherit;

        &:hover, &:focus, &:active {
          text-decoration: none;

          [itemprop="name"] {
            text-decoration: underline;
          }
        }
      }

      img {
        float: left;
        margin: 0 0 0 -58px;
        border-radius: 5px;
        width: 48px;
        height: 48px;
      }

      [itemprop="name"] {
        font-weight: bold;
      }

      [itemprop="alternateName"] {
        .font-size(@smallFontSize);
        color: @textColorTertiary;
      }
    }

    div {
      p {
        margin: 0;
        line-height: @baseLine;
      }

      img {
        margin: 5px 0;
        width: 100%;
        vertical-align: top;
      }

      .retweet-credit {
        .font-size(@smallFontSize);
        color: @textColorLight;

        &:before {
          .inline-block;
          font-family: 'Font Awesome', sans-serif;
          content: '\0F079\00A0';
        }

        a {
          color: inherit;
        }
      }
    }

    footer {
      overflow: hidden;

      .actions {
        float: right;
        margin: 0;

        li {
          .inline-block;
          margin: 0 0 0 8px;
          padding: 0;
          .font-size(@smallFontSize);

          &:first-child {
            margin-left: 0;
          }
        }

        a {
          color: @textColorTertiary;

          &:before {
            .inline-block;
            font-family: 'Font Awesome', sans-serif;
          }
        }

        .reply:before {
          content: '\0F112\00A0';
        }

        .retweet:before {
          content: '\0F079\00A0';
        }

        .favorite:before {
          content: '\0F005\00A0';
        }
      }
    }
  }
}

.html-rtl {
  #twitter-timeline-widget {
    header a.twitter-follow-button,
    article header .timestamp,
    footer .actions {
      float: left;
    }

    article {
      padding: 10px 68px 10px 10px;

      header .timestamp {
        margin: 0 10px 0 0;
      }

      [itemprop="author"] img {
        float: right;
        margin: 0 -58px 0 0;
      }
    }

    footer .actions li {
      margin: 0 8px 0 0;
    }
  }
}

@media only screen and (max-width: @breakMobileLandscape) {
  #twitter-timeline-widget {
    header a.twitter-follow-button {
      float: none;
      margin: 0;
    }
  }

  .html-rtl {
    #twitter-timeline-widget {
      header a.twitter-follow-button {
        float: none;
      }
    }
  }
}
