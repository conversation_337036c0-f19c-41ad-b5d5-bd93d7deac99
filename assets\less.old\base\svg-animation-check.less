// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

#svg-test {
    visibility: hidden;
    height: 0;
    width: 0;
    overflow: hidden;
}

#svg-test-circle {
    stroke-dasharray: 117;
    stroke-dashoffset: 117;
    -webkit-animation: svg-dash-test 0.1s linear forwards;
    animation: svg-dash-test 0.1s linear forwards;
}

@-webkit-keyframes svg-dash-test {
    0% {
        stroke-dashoffset: 0;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes svg-dash-test {
    0% {
        stroke-dashoffset: 0;
    }
    100% {
        stroke-dashoffset: 0;
    }
}
