// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

@import "../sandstone/lib.less";

.space #wrapper {
    background-repeat: no-repeat;
    background-position: 0 180px;
    background-image: url('/media/img/firefox/releasenotes/aurora_bg_800.png');
}

.notes-head {
    .clearfix;
    margin-bottom: @baseLine * 2;

    h1,
    h2,
    p {
        .span(8);
    }

    h1 {
        margin-bottom: .15em;
    }

    h2 {
        .font-size(1.428em);
        letter-spacing: normal;
        margin-bottom: .75em;
    }

    p {
        margin-bottom: 1em;
    }
}

.space .notes-head {
    background-repeat: no-repeat;
    background-position: top left;
    background-image: url('/media/img/firefox/releasenotes/aurora_small.png');
    background-size: 150px auto;
    padding: 10px 0 0 150px;
}

.blueprint .notes-head {
    h1,
    h2,
    p {
        .span(8);
    }
    h1 {
        margin-bottom: .15em;
    }
    h2 {
        margin-bottom: .75em;
    }
}

#platform {
    float: right;
    overflow: hidden;

    a {
        color: #fff;
    }

    h4 {
        .font-size(15px);
        margin: 0 0 6px 0;
        padding: 0;
    }

    ul {
        margin: 0;
        padding: 3px;
        float: left;
        box-shadow: 0 3px 3px rgba(126, 137, 147, .5) inset,
                    0 1px rgba(185, 197, 204, .5);
        border-radius: 9px;
        background: #b1becc;
        background: rgba(207, 218, 230, .25);
    }

    li {
        margin: 0;
        padding: 0;
        list-style-type: none;
        float: left;
    }

    a {
        border-bottom: 1px solid rgba(43, 58, 153, .5);
        box-shadow: 0 -2px rgba(0, 0, 0, .2) inset;
        #gradient > .vertical-three-colors(#7ca9f4, #4b79c6, 3%, #2e4e99);
        text-shadow: 0 1px 0 rgba(0, 0, 0, .35);
    }

    span {
        color: #506aa1;
        border-bottom: 1px solid #22366d;
        .box-shadow(0 2px 1 px #0b1b41 inset);
        #gradient > .vertical(#1a2f5a 14%, #254d90 95%);
        border-radius: 0 6px 6px 0;
        text-shadow: 0 1px rgba(28, 54, 112, .35);
    }

    li:first-child {
        padding-right: 1px;

        a,
        span {
            border-radius: 6px 0 0 6px;
        }
    }

    a,
    span {
        display: block;
        padding: 12px 16px;
        .open-sans;
        .font-size(@smallFontSize);
        font-weight: 600;
        text-transform: uppercase;
        border-radius: 0 6px 6px 0;
    }
}

.space #platform {
    ul {
        box-shadow: 0 0 10px rgba(72, 23, 95, .35) inset,
                    0 -1px 0 rgba(134, 80, 168, .5) inset;
    }

    a {
        #gradient > .vertical-three-colors(#f2a279, #f28047, 3%, #c63);
    }

    span {
        color: #d79676;
        #gradient > .vertical(#7b3816 14%, #ac5022 95%);
    }

}

a.go:after,
.sidebar a:after {
    content: " »";
    white-space: nowrap;
}

article {
    .clearfix;
}

.notes-section {
    .box-shadow(@shadow);
    @shadow: 0 0 0 1px #fff inset;
    background: #fff;
    border-bottom: 1px solid #ddd;
    margin: 0 -@gridGutterWidth @baseLine;
    padding: @baseLine @gridGutterWidth;

    > h4 {
        margin-top: 30px;
    }
}

.blueprint .notes-section {
    background: #111;
    background: rgba(9, 68, 121, .1);
    border: 1px solid rgba(25, 93, 153, .5);
    box-shadow: 0 0 10px rgba(9, 68, 121, .36) inset;
}

.space .notes-section {
    background: #303;
    background: rgba(40, 6, 57, .25);
    border: 1px solid rgba(80, 27, 91, .5);
    box-shadow: 0 0 10px rgba(72, 23, 95, .36) inset,
                0 -1px 0 rgba(134, 80, 168, .5) inset;
}

.section-items {
    list-style: none;
    margin: 0;

    > li {
        border-top: 1px solid @borderColor;
        margin: 0;
        padding: .714rem 0 .5rem;
        position: relative;
    }

    h4 {
        .font-size(18px);
        margin: 0 0 .25em;
    }

    p {
        margin: 0 0 .25em;
    }

    .note {
        .font-size(@smallFontSize);
        color: @textColorSecondary;
        margin: 0 0 .25em;
    }
}

.space .section-items > li {
    border-top-color: #522853;
}

.blueprint .section-items > li {
    border-top-color: #173e62;
}

.section-items.tagged {
    > li {
        padding-left: 160px;

        &.untagged {
            padding-left: 0;
        }
    }

    .tag {
        background: #f3f3f3 url('/media/img/firefox/releasenotes/tags.png') 0 0 no-repeat;
        border-right: 3px solid #ccc;
        box-shadow: 0 1px 1px rgba(0,0,0,0.05), 0 -1px 0 rgba(0,0,0,0.05) inset;
        color: @textColorPrimary;
        display: inline-block;
        .font-size(11px);
        font-style: normal;
        line-height: 14px;
        padding: 4px 2px 4px 33px;
        position: absolute;
        top: .714rem;
        left: 0;
        text-shadow: 1px 1px 0 #fff;
        text-transform: uppercase;
        width: 102px;
    }

    .tag-new {
        background-color: #ffedcf;
        background-position: 0 -50px;
        border-color: #f90;
    }

    .tag-changed {
        background-color: #e9cfff;
        background-position: 0 -100px;
        border-color: #8106e0;
    }

    .tag-fixed {
        background-color: #f0ffe1;
        background-position: 0 -150px;
        border-color: #5ba622;
    }

    .tag-html5 {
        background-color: #f8dfd8;
        background-position: 0 -200px;
        border-color: #d85027;
    }

    .tag-resolved {
        background-color: #dcf6ff;
        background-position: 0 -250px;
        border-color: #038fc0;
    }

    .tag-unresolved {
        background-color: #ffc8c8;
        background-position: 0 -300px;
        border-color: #c00;
    }

    .tag-developer {
        background-color: #e7e7e7;
        background-position: 0 -350px;
        border-color: #999;
    }
}

.sidebar section {
    border-bottom: 1px dotted darken(@borderColor, 10%);
    margin-bottom: @baseLine;

    h3 {
        .font-size(22px);
    }
}

.space .sidebar section {
    border-bottom-color: #522853;
}

.blueprint .sidebar section {
    border-bottom-color: #173e62;
}

#try {
    .box-shadow(@shadow);
    @shadow: 0 0 0 1px #fff inset;
    background: #fff;
    border-bottom: 1px solid #ddd;
    margin: 0 -@gridGutterWidth @baseLine;
    padding: @baseLine @gridGutterWidth;

    ol {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    li {
        .font-size(1.125rem);
        margin: 0;
        padding: .5em 0 .5em 40px;
        position: relative;

        &:before {
            display: block;
            width: 1.25em;
            height: 1.5em;
            padding: 0 .15em;
            background: #09c;
            color: #fff;
            text-shadow: 0 1px 1px rgba(0, 0, 0, .2);
            box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
            position: absolute;
            left: 0;
            top: 50%;
            margin-top: -.75em;
            text-align: center;
            line-height: 1.5em;
            vertical-align: middle;
            border-radius: 50%;
        }

        &.try1:before {
            content: '1';
        }
        &.try2:before {
            content: '2';
        }
        &.try3:before {
            content: '3';
        }
        &.try4:before {
            content: '4';
        }
    }
}

.space #try {
    background: #003;
    background: rgba(165, 165, 165, .08);
    border: 1px solid rgba(80, 80, 80, .25);
    box-shadow: 0 0 10px rgba(100, 100, 100, .36) inset,
                0 -1px 0 rgba(153, 153, 153, .25) inset;

    li:before {
        background: #c63;
    }
}

#problems li {
    margin: 0 0 .5em 20px;
}

#other p + p {
    margin-top: -.5em;
}


/* Tablet Layout: 760px */
@media only screen and (min-width: @breakTablet) and (max-width: @breakDesktop) {

    .space #wrapper {
        background-size: 650px;
    }

    .notes-head {
        h1,
        h2,
        p {
            .span_narrow(7);
        }
    }

    .blueprint .notes-head {
        h1,
        h2,
        p {
            .span-all();
        }
        h1 {
            margin-bottom: .15em;
        }
        h2 {
            margin-bottom: .75em;
        }
    }

    .space .notes-head {
        p {
            .span_narrow(6);
        }
    }

}


/* Mobile Layout: 320px */
@media only screen and (max-width: @breakTablet) {

    .space #wrapper {
        background-size: 320px;
    }

    .notes-head,
    .blueprint .notes-head {
        h1,
        h2,
        p {
            .span-all();
        }
        h1 {
            margin-bottom: .15em;
        }
        h2 {
            margin-bottom: .75em;
        }
    }

    .space .notes-head {
        background-image: none;
        padding: 0;
    }

    #platform {
        float: none;
        margin: @baseLine 10px;
    }

    .section-items.tagged {
        > li {
            padding-left: 0;
            display: block;
            word-wrap: break-word;
            width: auto;
            span {
                word-wrap: break-word;
            }
        }

        .tag {
            position: static;
            display: block;
            width: auto;
            margin: 0 0 .75em;
        }
    }
}
