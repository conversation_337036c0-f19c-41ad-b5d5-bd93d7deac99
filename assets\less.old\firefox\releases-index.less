// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

@import "../sandstone/lib.less";

#main-feature {
    h1,
    p {
        .span-all();
        margin-bottom: @baseLine / 2;
    }
}

#main-content {
    ol {
        margin: 0;
        list-style-type: none;
        li {
            overflow: hidden;
            margin: 0;
            border-bottom: 1px dotted #d6d6d6;
            padding: @baseLine / 2;
            line-height: @baseLine * 1.5;
            &:last-child {
                border: 0 none;
            }
            strong {
                float: left;
                width: 6em;
            }
            ol {
                margin-left: 6em;
                li {
                    float: left;
                    overflow: visible;
                    border: 0 none;
                    padding: 0;
                    width: 6em;
                    white-space: nowrap;
                }
            }
        }
    }
}
