// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

@import "../sandstone/lib.less";

@light: #fff;
@lightHover: #f4f4f4;

@mozGreen: #4caf50;
@mozGreenHover: #66bb6a;
@mozGreenBorder: #4aa84a;

@mozBlue: #0095dd;
@mozBlueHover: #33aae4;
@mozBlueBorder: #0092cc;

@mozDark: #00539F;
@mozDarkHover: #0060a8;
@mozDarkBorder: #005493;

@mozRed: #C13832;
@mozRedHover: #EA3B28;
@mozRedBorder: #C13832;

@mozOrange: #f26c23;
@mozOrangeHover: #f5894f;
@mozOrangeBorder: #e55525;

// the basis for all buttons on bedrock
.base-button() {
    .font-size(20px);
    .inline-block;
    .transition(background-color .1s ease-in-out);
    background: none;
    border-radius: 4px;
    cursor: pointer;
    padding: (@baseLine / 2) 36px;
    position: relative;
    text-align: center;
    text-shadow: none;

    @media only screen and (max-width: @breakMobileLandscape) {
        .font-size(18px);
    }

    &:active {
        position: relative;
        top: 1px;
    }

    &:hover,
    &:focus {
        text-decoration: none;
    }
}

// buttons used for forms or where a button element makes semantic sense
input[type="submit"].form-button,
button.form-button {
    .base-button;
    color: @mozBlue;
    border: 2px solid @mozBlue;
    width: 100%;

    &:hover,
    &:focus {
        color: @mozBlueHover;
        border-color: @mozBlueHover;
    }

    &.button-dark {
        color: @mozDark;
        border-color: @mozDark;

        &:hover,
        &:focus {
            color: @mozDarkHover;
            border-color: @mozDarkHover;
        }
    }

    &.button-light {
        color: @light;
        border-color: @light;

        &:hover,
        &:focus {
            color: @lightHover;
            border-color: @lightHover;
        }
    }

    &.button-general {
        .font-size(@largeFontSize);
    }

    &.button-red {
        background-color: @mozRed;
        color: @light;
        border: 2px solid @mozRedBorder;

        &:hover,
        &:focus {
            background-color: @mozRedHover;
        }
    }
}

// All buttons not in a form context such as CTA and download buttons
.button,
button.button,
.blueprint .button,
a.button:link,
a.button:visited {
    .base-button;
    background-color: @mozBlue;
    color: @light;
    border: 2px solid @mozBlueBorder;

    &:hover,
    &:focus {
        background-color: @mozBlueHover;
        color: @light;
    }

    &.button-dark {
        background-color: @mozDark;
        color: @light;
        border-color: @mozDarkBorder;

        &:hover,
        &:focus {
            background-color: @mozDarkHover;
        }
    }

    &.button-green {
        background-color: @mozGreen;
        color: @light;
        border: 2px solid @mozGreenBorder;

        &:hover,
        &:focus {
            background-color: @mozGreenHover;
        }
    }

    &.button-red {
        background-color: @mozRed;
        color: @light;
        border: 2px solid @mozRedBorder;

        &:hover,
        &:focus {
            background-color: @mozRedHover;
        }
    }

    &.button-orange {
        background-color: @mozOrange;
        color: @light;
        border: 2px solid @mozOrangeBorder;

        &:hover,
        &:focus {
            background-color: @mozOrangeHover;
        }
    }

    &.button-transparent-light {
        background-color: transparent;
        color: @light;
        border: 2px solid @light;

        &:hover,
        &:focus {
            border-color: @lightHover;
        }
    }
}

// privacy policy link that accompany most download buttons
.fx-privacy-link {
    display: block;
    .font-size(@largeFontSize);
    text-align: center;
    text-shadow: none;

    a:link,
    a:visited {
        color: @light;
    }

    a:hover,
    a:focus {
        text-decoration: underline;
    }
}

.sand .fx-privacy-link {
    a:link,
    a:visited {
        color: @linkBlue;
    }
}

.sky .fx-privacy-link {
    a:link,
    a:visited {
        color: @linkSkyBlue;
    }
}

.space .fx-privacy-link {
    a:link,
    a:visited {
        color: @linkBlue;
    }
}

.blueprint .fx-privacy-link {
    a:link,
    a:visited {
        color: @linkBlue;
    }
}

// fxfamilynav overrides
.fxfamilynav-cta-wrapper {
    .button,
    a.button:link,
    a.button:visited {
        padding: 10px 18px;
        .font-size(@baseFontSize);
    }
}

// download button and related containers specific
ul.download-list {
    list-style-type: none;
    margin-bottom: 10px;

    li {
        margin-left: 0;
    }

    strong {
        font-weight: normal;
    }
}

.infobar {
    a.button {
        margin-left: @baseLine;
        padding: 5px 18px;
        .font-size(@largeFontSize);
    }
}

.download-dumb {
    ul {
        list-style: none;

        li {
            .inline-block();
            margin: @baseLine 0 0;

            .button {
                padding: 6px;
                margin-left: 3px;
                .font-size(14px);
            }
        }
    }
}

.download-button {
    display: inline-block;
    vertical-align: top;
}

.download-other {
    color: @textColorLight;
    .font-size(11px);

    a:link,
    a:visited {
        color: #999;
    }

    a:hover,
    a:focus,
    a:active {
        color: #999;
        text-decoration: underline;
    }
}

/* !important used for strict download link enforcement */
/* stylelint-disable declaration-no-important  */

// Product download buttons
.download-button {
    .ios-download,
    .linux-arm-download,
    .unrecognized-download,
    .unsupported-download,
    .unsupported-download-osx,
    .nojs-download {
        display: none;
    }
}

// OS detection
.download-button .os_msi,
.download-button .os_winsha1,
.download-button .os_win64,
.download-button .os_linux,
.download-button .os_linux64,
.win7up.x86.x64 .download-button .os_win,
.android .download-button-desktop,
.windows.arm .download-button .os_win,
.linux.arm .download-button .os_linux,
.linux.x86.x64 .download-list .os_linux,
.download-button .os_win,
.download-button .os_osx,
.download-button .os_android,
.download-button .os_ios,
.no-js .download-list,
.other .download-list {
    display: none !important;
}

.win7up.x86.x64 .download-button .os_win64,
.linux .download-button .os_linux,
.linux.x86.x64 .download-button .os_linux64,
.windows .download-button .os_win,
.osx .download-button .os_osx,
.android .download-button .os_android,
.download-button-android .os_android,
.android .download-button-desktop .download-list,
.android .download-button-desktop small.os_win,
.download-button-ios .os_ios,
.ios .download-button .os_ios,
.ios .download-button .ios-download,
.ios .download-button-desktop .download-list,
.other .download-button-android .download-list,
.other .download-button small.os_win {
    display: block !important;
}

.windows.arm .download-button .unsupported-download,
.linux.arm .download-button .linux-arm-download,
.oldwin .download-button .unsupported-download,
.oldmac .download-button .unsupported-download {
    display: block;
    max-width: 250px;
}

// Hide the privacy link if platform is unsupported.
.windows.arm .download-button .fx-privacy-link,
.linux.arm .download-button .fx-privacy-link,
.oldwin .download-button .fx-privacy-link,
.oldmac .download-button .fx-privacy-link {
    display: none;
}

.android .download-button-desktop,
.ios .download-button-desktop,
.no-js .download-button {
    .nojs-download {
        display: block;
    }
}

.other .download-button {
    .unrecognized-download {
        display: block;
    }
}

// Android architecture detection
.download-button .download-list .os_android.x86,
.download-button .download-other.os_android .api-15,
.android.x86 .download-button .download-list .os_android.armv7up,
.android.x86 .download-button .download-other.os_android .x86 {
    display: none !important;
}

.android.x86 .download-button .download-list .os_android.x86 {
    display: block !important;
}

.android.x86 .download-button .download-other.os_android .armv7up {
    display: inline !important;
}

// IE on Windows XP, Server 2003, Vista need sha-1 button
.windows.sha-1 .download-button .os_win {
    display: none !important;
}

.windows.sha-1 .download-button .os_winsha1 {
    display: block !important;
}

/* stylelint-enable */

// l10n download button tweaks
html[lang="ml"] {
    .button {
        padding: 10px 16px;
    }
}
