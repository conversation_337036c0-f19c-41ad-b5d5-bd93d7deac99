// THIS FILE SHOULD OUTPUT NOTHING!
//
// As this file is included in many places (for variable/mixin access),
// it must output nothing for performance reasons.
//
// IF IT'S NOT A MIXIN OR VARIABLE, IT DOESN'T GO HERE!


// Fonts and color

// The custom X-LocaleSpecific font family is defined with a @font-face rule in
// locale-specific stylesheets which can be found at /media/css/l10n/. See
// /docs/l10n.rst for details.
@baseFontFamily:    'Open Sans', X-LocaleSpecific, sans-serif;

@baseFontSize:      14px;
@baseLine:          20px;
@smallFontSize:     12px;
@largeFontSize:     16px;

// Use rem (root em) units for font-size.
// Assumes default 16px to match sizes but will respect user preferences.
// Pass through any units that aren't px or rem
.font-size(@value) {
    font-size: @value;
}

// When unit is pixels, pass it through and convert to rem
.font-size(@value) when (ispixel(@value)) {
    font-size: @value;
    .font-size(unit(@value / 16, rem));
}

// When unit is rem, convert it to pixels and pass it through
.font-size(@value) when (isunit(@value, rem)) {
    font-size: unit(@value * 16, px);
    font-size: @value;
}

@textColorPrimary:   #333;
@textColorSecondary: #484848;
@textColorTertiary:  #666;
@textColorLight:     #bbb;

@textColorSpaceYellow: #ffbb38;
@blueprintTextColorSecondary: #a2b4c7;

@blueprintTextColorSecondary: #a2b4c7;

@linkRed:            rgb(175,50,50);
@linkBlue:           rgb(0,150,221);
@linkBlueHover:      rgb(0,83,159);
@linkSkyBlue:        rgb(103,167,208);

@mozillaRed: #c13832;

@buttonGreen: #81bc2e;
@buttonGreenDark: darken(@buttonGreen, 10%);
@buttonTextColor: #fff;
@buttonBlue: #43a6e2;
@buttonBlueDark: #277ac1;

// new mozID visual style
@mozIDBlue: #0c99d5;
@mozIDGrey: #56565a;

@colorBrandCyan:      #4eb5e6;
@colorBrandCoral:     #ff8385;
@colorBrandYellow:    #ffed00;
@colorBrandLilac:     #b0b2e9;
@colorBrandOrange:    #faba00;
@colorBrandLime:      #b6d806;
@colorBrandNeonblue:  #0ff;
@colorBrandNeonpink:  #fa8cff;
@colorBrandNeongreen: #abff10;

// product brand colors
@colorFirefoxLightOrange: #ff9500;

.open-sans() {
    font-family: @baseFontFamily;
}

.open-sans-italic() {
    font-family: @baseFontFamily;
    font-style: italic;
}

.open-sans-light() {
    font-family: 'Open Sans Light', X-LocaleSpecific-Light, @baseFontFamily;
    font-weight: normal;
}

.open-sans-extrabold() {
    font-family: 'Open Sans Extrabold', X-LocaleSpecific-Extrabold, @baseFontFamily;
    font-weight: 800;
}

// Button shadows

@shadowPartBottom:      0 1px 0 0 rgba(0, 0, 0, 0.2);
@shadowPartBottomInset: inset 0 -1px 0 0 rgba(0, 0, 0, 0.3);
@shadowPartHover:       inset 0 12px 24px 2px saturate(@buttonBlue, 10%);
@shadowPartFocusRing:   0 0 0 2px rgba(73,173,227,0.4);

@buttonShadow:          @shadowPartBottom, @shadowPartBottomInset;
@buttonShadowHover:     @shadowPartBottom, @shadowPartBottomInset, @shadowPartHover;
@buttonShadowFocus:     @shadowPartBottom, @shadowPartBottomInset, @shadowPartHover, @shadowPartFocusRing;

@buttonShadowActive:     inset 0 2px 0 0 rgba(0, 0, 0, 0.2),
                         inset 0 12px 24px 6px rgba(0,0,0,0.2),
                         inset 0 0 2px 2px rgba(0,0,0,0.2);

@borderColor: #d6d6d6;

// Gutter width remains the same for all grids
@gridGutterWidth:         20px;

// Default grid: 12 columns at 60px
@gridColumns:             12;
@gridColumnWidth:         60px;
@gridRowWidth:            (@gridColumns * @gridColumnWidth) + (@gridGutterWidth * (@gridColumns - 1));

// Tablet grid: 12 columns at 40px
@gridColumnWidthNarrow:   40px;
@gridRowWidthTablet:      (@gridColumns * @gridColumnWidthNarrow) + (@gridGutterWidth * (@gridColumns - 1));

// Mobile grid: 5 columns at 40px
@gridColumnsMobile:       5;
@gridRowWidthMobile:      (@gridColumnsMobile * @gridColumnWidthNarrow) + (@gridGutterWidth * (@gridColumnsMobile - 1));

// Mobile landscape grid: 7 columns at 40px
@gridColumnsMobileWide:   7;
@gridRowWidthMobileWide:  (@gridColumnsMobileWide * @gridColumnWidthNarrow) + (@gridGutterWidth * (@gridColumnsMobileWide - 1));

// Breakpoints for common devices
@breakDesktopWide:        1400px;
@breakDesktop:            1000px;
@breakTablet:             760px;
@breakMobileLandscape:    480px;
@breakMobile:             320px;

// Breakpoints for global navigation
@breakNavTablet:  860px;
@breakNavDesktop: 1140px;

// Page widths for common breakpoints

// Desktop:
//           (12cols ✕ 60px) =  720
// +     (11 gutters ✕ 20px) =  220
// + (2 page gutters ✕ 30px) =   60
// --------------------------------
//                           = 1000px
//
// Tablet:
//           (12cols ✕ 40px) =  480
// +     (11 gutters ✕ 20px) =  220
// + (2 page gutters ✕ 30px) =   60
// --------------------------------
//                           =  760px
//
// Phone (landscape):
//            (7cols ✕ 40px) =  280
// +      (6 gutters ✕ 20px) =  120
// + (2 page gutters ✕ 20px) =   40
// --------------------------------
//                           =  440px
//
// Phone (portrait):
//            (5cols ✕ 40px) =  200
// +      (4 gutters ✕ 20px) =   80
// + (2 page gutters ✕ 20px) =   40
// --------------------------------
//                           =  320px

@widthDesktopWide:        1400px;
@widthDesktop:            1000px;
@widthTablet:             760px;
@widthMobileLandscape:    440px;
@widthMobile:             320px;

// for fluid width hub pages bug 1363708.
@widthMaxContent: 1420px; // 85px left padding + 1250px content + 85px right padding

.clearfix() {
    zoom: 1;
    &:after {
        display: block;
        visibility: hidden;
        height: 0;
        clear: both;
        content: ".";
    }
}

.inset() {
    background-color: rgb(233,233,233);
    background-color: rgba(0,0,0,0.02);
    @shadow: 0 0 3px rgba(0,0,0,0.1) inset;
    .box-shadow(@shadow);
    border-bottom: 1px solid #fff;
}

// Some mixins from http://markdotto.com/bootstrap
// http://creativecommons.org/licenses/by/3.0/

.border-radius(@radius: 0.25em) {
  border-radius: @radius;
}

.inline-block() {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}

.box-shadow(@shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.1)) {
  box-shadow: @shadow;
}

.transition(@transition: all linear .25s) {
  -webkit-transition: @transition;
  transition: @transition;
}

.transform(@transform: inherit) {
  -webkit-transform: @transform;
  -ms-transform: @transform;
  transform: @transform;
}

.transform-origin(@transform-origin: left top) {
  -webkit-transform-origin: @transform-origin;
  -ms-transform-origin: @transform-origin;
  transform-origin: @transform-origin;
}

.transform-style(@transform-style: inherit) {
  -webkit-transform-style: @transform-style;
  -ms-transform-style: @transform-style;
  transform-style: @transform-style;
}

// Extra mixins for fine-tuned transitions
.transition-property(@transition-property: all) {
  -webkit-transition-property: @transition-property;
  transition-property: @transition-property;
}

.transition-duration(@transition-duration: 1s) {
  -webkit-transition-duration: @transition-duration;
  transition-duration: @transition-duration;
}

.transition-timing-function(@transition-timing-function: linear) {
  -webkit-transition-timing-function: @transition-timing-function;
  transition-timing-function: @transition-timing-function;
}

.transition-delay(@transition-delay: 0s) {
  -webkit-transition-delay: @transition-delay;
  transition-delay: @transition-delay;
}

.animation(@parameters) {
  -webkit-animation: @parameters;
  animation: @parameters;
}

.border-box() {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.flexbox() {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
}

.flex(@values) {
    -webkit-box-flex: @values;
    -moz-box-flex: @values;
    -webkit-flex: @values;
    -ms-flex: @values;
    flex: @values;
}

.flex-wrap(@value: wrap) {
    -webkit-flex-wrap: wrap;
    -moz-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.align-items(@align: stretch) {
    -webkit-align-items: @align;
       -moz-align-items: @align;
        -ms-align-items: @align;
            align-items: @align;
}

.justify-content(@justify: flex-start) {
    -webkit-justify-content: @justify;
       -moz-justify-content: @justify;
        -ms-justify-content: @justify;
            justify-content: @justify;
}

// White content box, often used with .title-shadow-box
// See /about/powered-by for example.
.callout-content() {
    .clearfix;
    display: block;
    @shadow: 0 0 0 1px #fff inset;
    .box-shadow(@shadow);
    background: #fff;
    border-bottom: 1px solid #ddd;
    margin: 0 auto @baseLine;
    padding-left: @gridGutterWidth;
    padding-right: @gridGutterWidth;
}

// Gradients
#gradient {
  .horizontal (@startColor: #555, @endColor: #333) {
    background-color: @endColor;
    background-repeat: repeat-x;
    background-image: -webkit-linear-gradient(left, @startColor, @endColor); /* Safari 5.1+, Chrome 10+ */
    -ms-filter: %("progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=1)",@startColor,@endColor); /* IE8+ */
    background-image: linear-gradient(to right, @startColor, @endColor); /* the standard */
  }
  .vertical (@startColor: #555, @endColor: #333) {
    background-color: @endColor;
    background-repeat: repeat-x;
    background-image: -webkit-linear-gradient(top, @startColor, @endColor); /* Safari 5.1+, Chrome 10+ */
    -ms-filter: %("progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=0)",@startColor,@endColor); /* IE8+ */
    background-image: linear-gradient(to bottom, @startColor, @endColor); /* the standard */
  }
  .directional (@startColor: #555, @endColor: #333, @deg: 45deg) {
    background-color: @endColor;
    background-repeat: repeat-x;
    background-image: -webkit-linear-gradient(@deg, @startColor, @endColor); /* Safari 5.1+, Chrome 10+ */
    background-image: linear-gradient(@deg, @startColor, @endColor); /* the standard */
  }
  .vertical-three-colors(@startColor: #00b3ee, @midColor: #7a43b6, @colorStop: 50%, @endColor: #c3325f) {
    background-color: @endColor;
    background-repeat: no-repeat;
    background-image: -webkit-linear-gradient(top, @startColor 0%, @midColor @colorStop, @endColor 100%); /* Chrome10+,Safari5.1+ */
    background-image: linear-gradient(to bottom, @startColor 0%, @midColor @colorStop, @endColor 100%); /* the standard */
  }
  .radial(@posX:center, @posY:center, @shape:circle, @size:closest-side, @startColor:white, @endColor:black){
    background-image: -webkit-radial-gradient(@posX @posY, @shape @size, @startColor, @endColor);
    background-image: radial-gradient(@shape @size at @posX @posY, @startColor, @endColor);
  }
}


// Column spans for standard column widths
.span(@columns) {
    float: left;
    width: (@gridColumnWidth * @columns) + (@gridGutterWidth * (@columns - 1));
    margin: 0 (@gridGutterWidth / 2);
}

.span-all() {
    float: none;
    width: auto;
    margin: 0 (@gridGutterWidth / 2);
}

.offset(@columns) {
    margin-left: ((@gridColumnWidth + @gridGutterWidth) * @columns) + (@gridGutterWidth * 0.5);
}

// Column spans for narrow column widths
.span_narrow(@columns) {
    float: left;
    width: (@gridColumnWidthNarrow * @columns) + (@gridGutterWidth * (@columns - 1));
    margin: 0 (@gridGutterWidth / 2);
}

.offset_narrow(@columns) {
    margin-left: ((@gridColumnWidthNarrow + @gridGutterWidth) * @columns) + (@gridGutterWidth * 0.5);
}

.button-hover-box-shadow(@color: #ffffff) {
    @shadow:  @shadowPartBottom,
              @shadowPartBottomInset,
              inset 0 12px 24px 2px saturate(@color, 10%);
    .box-shadow(@shadow);
}

.button-focus-box-shadow(@color: #ffffff) {
    @shadow:  @shadowPartBottom,
              @shadowPartBottomInset,
              0 0 0 2px fade(@color, 40%);
    .box-shadow(@shadow);
}

@formFieldErrorShadow:       0 0 0 2px rgba(175, 50, 50, .4);
@formFieldSelectErrorShadow: 0 0 0 3px rgba(175, 50, 50, .4);

.form-field-error() {
    border-color: rgb(175,50,50);
    .box-shadow(@formFieldErrorShadow);
}

.required-star() {
    content: " *";
    .font-size(@smallFontSize);
    font-weight: bold;
    color: @linkRed;
}

/****************************************************************************/
// Classes used to hide content from users. Choose wisely!

// Hide visually, but not to screen readers
.visually-hidden() {
    /* !important required to ensure element is hidden when mixin is applied */
    position: absolute !important; /* stylelint-disable-line declaration-no-important */
    height: 1px;
    width: 1px;
    margin: -1px;
    overflow: hidden;
    clip: rect(0 0 0 0);
    padding: 0;
    border: 0;
}

// Hide visually & from screen readers
.hidden() {
    display: none;
}

/****************************************************************************/

// not the most up to date method, but works in IE7
.image-replaced() {
    text-indent: 120%; // extra 20% to account for fancy fonts that may overhang
    white-space: nowrap;
    overflow: hidden;
}

// Flip an element horizontally for RTL locales such as Arabic. Usually applied
// to :before or :after pseudo-elements with a background-image
.flip-horizontally() {
    .transform(scale(-1, 1));
}

.trailing-arrow() {
    &:after {
        content: "\00A0\00BB"; /* nbsp raquo */
        white-space: nowrap;
    }
}

.leading-arrow() {
    &:before {
        content: "« "; /* laquo nbsp */
        white-space: nowrap;
    }
}

// Make a button look like an anchor
.link-button() {
    border: none;
    background: transparent;
    color: @linkBlue;
    .font-size(@baseFontSize);
    padding: 0;
    margin: 0;
    line-height: 1.5;
    .open-sans();
    cursor: pointer;

    &:hover {
        text-decoration: underline;
        color: darken(@linkBlue, 10%);
    }

    &:focus {
        outline: 0;
    }
}

.multi-column(@width: auto, @count: auto, @gap: normal) {
  -webkit-column-width: @width;
     -moz-column-width: @width;
       -o-column-width: @width;
          column-width: @width;
  -webkit-column-count: @count;
     -moz-column-count: @count;
       -o-column-count: @count;
          column-count: @count;
    -webkit-column-gap: @gap;
       -moz-column-gap: @gap;
         -o-column-gap: @gap;
            column-gap: @gap;
}

.multi-column-clear() {
  .multi-column(auto, auto, normal);
}

.multi-column-avoid-break() {
            page-break-inside: avoid;
  -webkit-column-break-inside: avoid;
     -moz-column-break-inside: avoid;
       -o-column-break-inside: avoid;
                 break-inside: avoid;
}

// A helper mixin for applying high-resolution background images (http://www.retinajs.com)
// File-names should follow:
//  example.png
//  example-high-res.png
@highdpi: ~"(-webkit-min-device-pixel-ratio: 1.5), (min--moz-device-pixel-ratio: 1.5), (-o-min-device-pixel-ratio: 3/2), (min-resolution: 1.5dppx)";
.at2x(@path, @w: auto, @h: auto) {
    background-image: url(@path);
    @at2x_path: ~`@{path}.replace(/\.\w+$/, function(match) { return "-high-res" + match; })`;
    @media @highdpi {
        background-image: url("@{at2x_path}");
        -webkit-background-size: @w @h;
                background-size: @w @h;
    }
}

.background-size(...) {
    -webkit-background-size: @arguments;
            background-size: @arguments;
}

// Will usually be added to #outer-wrapper
.aurora-bars(@pageBgColor: @mozIDBlue, @auroraStartColor: #00a7e0) {
    background-color: @pageBgColor;
    background-image: none;
    background-image: linear-gradient(to bottom, fade(@pageBgColor, 0%) 0, fade(@pageBgColor, 0%) 100px, @pageBgColor 450px, @pageBgColor),
        repeating-linear-gradient(118deg,
            fade(@pageBgColor, 0%) 0, @auroraStartColor 550px,
            fade(@pageBgColor, 0%) 550px, @auroraStartColor 800px,
            fade(@pageBgColor, 0%) 800px, @auroraStartColor 950px);

    @media (max-width: @breakDesktop) {
        background-image: linear-gradient(to bottom, fade(@pageBgColor, 0%) 0, fade(@pageBgColor, 0%) 100px, @pageBgColor 350px, @pageBgColor),
            repeating-linear-gradient(118deg,
                fade(@pageBgColor, 0%) 0, @auroraStartColor 410px,
                fade(@pageBgColor, 0%) 410px, @auroraStartColor 600px,
                fade(@pageBgColor, 0%) 600px, @auroraStartColor 720px);
    }

    @media (max-width: @breakTablet) {
        background-image: linear-gradient(to bottom, fade(@pageBgColor, 0%) 0, fade(@pageBgColor, 0%) 100px, @pageBgColor 300px, @pageBgColor),
            repeating-linear-gradient(118deg,
                fade(@pageBgColor, 0%) 0, @auroraStartColor 320px,
                fade(@pageBgColor, 0%) 320px, @auroraStartColor 480px,
                fade(@pageBgColor, 0%) 480px, @auroraStartColor 570px);
    }

    @media (max-width: @breakMobileLandscape) {
        background-image: linear-gradient(to bottom, fade(@pageBgColor, 0%) 0, fade(@pageBgColor, 0%) 100px, @pageBgColor 250px, @pageBgColor),
            repeating-linear-gradient(118deg,
                fade(@pageBgColor, 0%) 0, @auroraStartColor 200px,
                fade(@pageBgColor, 0%) 200px, @auroraStartColor 320px,
                fade(@pageBgColor, 0%) 320px, @auroraStartColor 380px);
    }
}
