@import "lib.less";
@import "reset.less";
@import "fonts.less";
@import "buttons.less";
@import "animations.less";

/* {{{ Basic Colors, Text, Links */

html {
    background: #fff;
    .font-size(100%);
}

body {
    .font-size(@baseFontSize);
    line-height: 1.5;
    font-family: @baseFontFamily;
    color: @textColorPrimary;
    background: #fff;
}

#outer-wrapper {
    position: relative;
    border-top: 2px solid #fff;
    background: #f9f9f9 url("/media/img/sandstone/bg-stone.png") 0 0 repeat-x;
}

#wrapper {
    padding-bottom: @baseLine * 2;
    width: @widthDesktop;
    margin: 0 auto;
    position: relative;
}

#strings { /* An arbitrary container for translatable strings to use in scripts */
    display: none;
}

a {
    color: @linkBlue;
    text-decoration: none;

    &:hover,
    &:focus,
    &:active {
        color: darken(@linkBlue, 10%);
        text-decoration: underline;
    }

    &:visited {
        color: darken(@linkBlue, 10%);
    }
}

// Add a double-arrow after links
a.more {
    .trailing-arrow();
}

.sand #outer-wrapper {
    background: #f5f1e8 url("/media/img/sandstone/bg-gradient-sand.png") repeat-x;
    background: url("/media/img/sandstone/bg-gradient-sand.png") repeat-x,
                url("/media/img/sandstone/bg-sand.png") repeat,
                #f5f1e8;
}

.sky {
    #outer-wrapper {
        background: #eee url("/media/img/sandstone/bg-gradient-sky.png") repeat-x;
        background: url("/media/img/sandstone/bg-gradient-sky.png") repeat-x,
                    url("/media/img/sandstone/grain.png") repeat,
                    #eee;
    }

    a {
        color: @linkSkyBlue;
        &:hover,
        &:focus,
        &:active {
            color: darken(@linkSkyBlue, 10%);
        }
    }
}

// Daily visual style

.space {
    color: #fff;
    background: #000;

    #outer-wrapper {
        background: url('/media/img/firefox/horizon/stars.svg') center 60px no-repeat,
                    linear-gradient(to bottom, #000, #002048 1000px, #000 2000px);
    }

    a {
        color: @linkBlue;
        &:hover,
        &:focus,
        &:active {
            color: lighten(@linkBlue, 10%);
        }
    }

    h1, h2, h3, h4, h5, h6, .huge, .large {
        color: #fff;
        text-shadow: none;
    }

    #masthead nav li {
        a,
        a:link,
        a:visited {
            color: @linkBlue;
        }
        li {
            a,
            a:link,
            a:visited {
                color: @textColorSecondary;
            }
        }
    }

    #masthead h2 img {
        height: auto;
    }
}

// Firefox Developer Edition visual style
.blueprint {
    color: #fff;

    #outer-wrapper {
        background: #1e1e21;
        #gradient > .radial(top, left, ellipse, farthest-side, #00549e 0%, rgba(0, 0, 0, 0) 100%);
        background-size: 100% 500px;
        background-repeat: no-repeat;
        background-position: top center;
    }

    a {
        color: @linkBlue;
        &:hover,
        &:focus,
        &:active {
            color: lighten(@linkBlue, 10%);
        }
    }

    h1, h2, h3, h4, h5, h6, .huge, .large {
        color: #fff;
        text-shadow: none;
    }

    #masthead nav li {
        a,
        a:link,
        a:visited {
            color: #fff;
        }
        li {
            a,
            a:link,
            a:visited {
                color: @textColorSecondary;
            }
        }
    }
}

// mozID visual style
// full width rows w/differing bg colors
// TODO: needs responsive styles
.mozID {
    a:link,
    a:active,
    a:focus,
    a:visited {
        color: #fff;

        &:hover,
        &:focus {
            text-decoration: underline;
        }
    }

    h1, h2, h3, h4, h5, h6, .huge, .large {
        color: #fff;
        text-shadow: none;
    }

    #wrapper {
        width: 100%;

        .container {
            width: @widthDesktop - (@baseLine * 2);
            padding-left: @baseLine;
            padding-right: @baseLine;
        }
    }

    #masthead {
        z-index: 10;
        padding-left: 0;
        padding-right: 0;
    }
}

h1, h2, h3, h4, h5, h6, legend, .huge, .large, legend {
    .open-sans-light;
    display: block;
    margin: 0 0 12px 0;
    line-height: 1;
    text-shadow: 0 1px 0 rgba(255, 255, 255, .75);
    color: @textColorSecondary;
    letter-spacing: -.035em;
}

.huge,
.huge h1 {
    .font-size(108px);
}

.large,
.large h1 {
    .font-size(72px);
}

h1,
.huge h2,
.large h2,
.billboard h2 {
    .font-size(48px);
}

h2 {
    .font-size(32px);
}

h3 {
    .font-size(28px);
}

h4, legend {
    .font-size(24px);
}

h5 {
    .font-size(@largeFontSize);
}

h6 {
    .font-size(@baseFontSize);
}

.small,
small {
    .font-size(@smallFontSize);
    line-height: 1.3;
}

hgroup {
    h1, h2, h3, h4, h5, h6 {
        margin-bottom: 0;
    }
}

p,
ul,
ol,
dl,
hgroup {
    margin: 0 0 @baseLine 0;
}

ul.unstyled,
ol.unstyled {
    li {
        list-style-type: none;
        margin-left: 0;
    }
    li li {
        list-style-type: disc;
        margin-left: 20px;
    }
}

li > ul {
    margin-bottom: 0;
}

li {
    margin-left: 20px;
}

dl dt {
    .open-sans-light;
    .font-size(32px);
    line-height: 1;
    letter-spacing: -.035em;
    margin-bottom: @baseLine / 2;
}

dl dd {
    margin-bottom: @baseLine * 2;
}

dl.faq dt {
    .font-size(18px);
    letter-spacing: normal;
}

dl.faq dd {
    margin-bottom: 1.5em;
}

dl.simple dt {
    .font-size(18px);
    font-weight: bold;
}

dl.simple dd {
    margin-bottom: @baseLine;
}

pre,
code {
    color: @textColorTertiary;
    .font-size(@baseFontSize);
}

.center {
    text-align: center;
}

hr {
    margin: @baseLine 0;
    border-bottom: 1px dotted @borderColor;
}

img {
    max-width: 100%;
    -ms-interpolation-mode: bicubic;
}

img[data-high-res] {
    display: none;
}

.js img[data-high-res] {
    display: inline;
}

/* }}} */
/* {{{ Forms */

textarea,
input[type=email],
input[type=url],
input[type=tel],
input[type=password],
input[type=search],
input[type=text],
input[type=number],
input[type=date],
input[type=time] {
    background: #fff;
    border-color: #b2b2b2;
    border-style: solid;
    border-width: 1px;
    @shadow: 0 1px rgba(255,255,255,0.5);
    .font-size(@smallFontSize);
    padding: (@baseLine / 5)  (@baseLine / 2);
    .border-radius(3px);
    .box-shadow(@shadow);
    .transition(all linear .1s);
    line-height: 1.1;
}

button,
input,
select,
textarea {
    font-family: inherit; // must inherit or falls back to UA stylesheet/system font
}

textarea { height: auto; }

textarea:focus,
input[type=email]:focus,
input[type=password]:focus,
input[type=search]:focus,
input[type=text]:focus,
input[type=tel]:focus,
input.focus {
    border-color: #42a4e0;
    @shadow: 0 0 0 2px rgba(73,173,227,0.4);
    .box-shadow(@shadow);
    .transition(all linear .1s);
}

select:focus {
    @shadow: 0 0 0 2px rgba(73,173,227,0.4);
    .box-shadow(@shadow);
}

textarea:-moz-ui-invalid:not(output),
input[type=email]:-moz-ui-invalid:not(output),
input[type=password]:-moz-ui-invalid:not(output),
input[type=search]:-moz-ui-invalid:not(output),
input[type=text]:-moz-ui-invalid:not(output),
input[type=tel]:-moz-ui-invalid:not(output),
input.invalid {
    border-color: #a91300;
    @shadow: 0 0 0 2px rgba(255,80,80,0.4);
    .box-shadow(@shadow);
    .transition(all linear .1s);
}

.fill-width {
    display: block;
    width: 100%;
    .border-box();
}

.field {
    margin-bottom: @baseLine / 2;
    label {
        display: block;
        margin-bottom: 0.1em;
        .required {
            .font-size(@smallFontSize);
            color: @linkRed;
            text-transform: uppercase;
        }
        &.checkbox,
        &.radio {
            display: block;
            padding-left: 1.5em;
        }
        &.checkbox input[type="checkbox"],
        &.radio input[type="radio"] {
            float: left;
            margin-left: -1.5em;
        }
    }
    &.required label:after {
        .required-star();
    }
    .field-note {
        .font-size(@smallFontSize);
        color: @textColorTertiary;
        margin-top: 0.1em;
    }
}

.form-minimal-label {
    label {
        .font-size(@smallFontSize);
        color: #999;
    }
}

.in-form-fieldset {
    margin-top: @baseLine / 2;
    margin-bottom: @baseLine / 2;
    legend {
        margin-bottom: @baseLine / 2;
        .font-size(18px);
        letter-spacing: normal;
        &.required:after {
            .required-star();
        }
    }
}

.messagelist {
    margin: 0 0 24px 0;
    .open-sans;
    li {
        list-style-type: none;
    }
    .error { color: #c00; }
    .warning { color: #0c0; }
    .info { color: #000; }
}

.errorlist {
    margin: 0;
    .open-sans;
    color: #c00;

    li {
        list-style-type: none;
        margin: 0;
    }
}

.field-error {
    input[type=email],
    input[type=password],
    input[type=text],
    input[type=tel] {
        .form-field-error();
    }
    select {
        .box-shadow(@formFieldSelectErrorShadow);
    }
    label {
        color: @linkRed;
    }
}

input[type=email], input[type=password], input[type=text], input[type=tel], textarea {
    &.error {
        .form-field-error();
    }
}

select.error {
    .box-shadow(@formFieldSelectErrorShadow);
}

.tooltip {
    color: #fff;
    font-weight: bold;
    text-shadow: 0 1px #333;
    text-align: center;
    line-height: 16px;
    background-color: #aa1401;
    background-image: -webkit-linear-gradient(top, rgba(218, 81, 50, 0.9), rgba(169, 19, 0, 1));
    background-image:         linear-gradient(top, rgba(218, 81, 50, 0.9), rgba(169, 19, 0, 1));
    border-radius: 4px;
    padding: 10px 15px;
    box-shadow: 0 1px #666;
    max-width: 275px;
    position: relative;
    &:after {
        content: "";
        position: absolute;
        top: 100%;
        left: 15px;
        border-style: solid;
        border-width: 10px 10px 0;
        border-color: #a91300 transparent;
    }
    &.arrow-top:after {
        top: -10px;
        border-width: 0 10px 10px;
        border-color: #d54a2c transparent;
    }
    &.arrow-left:after {
        top: 50%;
        margin-top: -10px;
        left: -10px;
        border-width: 10px 10px 10px 0;
        border-color: transparent #c13018;
    }
}

label.error {
    .open-sans;
    color: rgb(175,50,50);
}

.super-priority-field {
    display: none;
    visibility: hidden;
}

/* }}} */
/* {{{ Layout */

#main-content,
#main-feature {
    padding-bottom: 48px;
}

.main-column {
    .span(7);
}

.sidebar {
    .span(3);
    .offset(2);
}

.divider.container,
.divider {
    border-bottom: 1px dotted @borderColor;
    padding-bottom: @baseLine * 2;
    margin-bottom: @baseLine *2;
}

.divider-last.container,
.divider-last {
    border-bottom: 0;
    padding-bottom: @baseLine * 2;
}

/* }}} */
/* {{{ Grid containers */
#masthead,
#main-feature,
#main-content,
#colophon,
.billboard,
.container {
    display: block;
    margin: 0 auto;
    padding-left: @gridGutterWidth;
    padding-right: @gridGutterWidth;
    position: relative;
    .clearfix;
}

/* }}} */
/* {{{ Header Nav */
#masthead {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    // Fix for IE9 - fall back to stacked elements
    text-align: center;

    h2 {
        padding: (@baseLine * 1.5) 0 @baseLine;
        margin: 0 (@gridGutterWidth / 2);
        flex: 1;

        img {
            height: auto;
        }
    }

    nav {
        margin: 0 16px;
        text-transform: uppercase;
        .font-size(13px);
        .open-sans;

        #nav-main-donate a {
            .open-sans-extrabold();
            .font-size(15px);
        }

        ul {
            margin-bottom: 0;
        }

        li {
            .inline-block;
            list-style-type: none;
            margin: 0;

            a,
            b {
                display: inline-block;
                padding: 12px;
                font-weight: normal;
            }

            b,
            .current {
                background-position: 50% 0;
                background-repeat: no-repeat;
                background-image: url("/media/img/sandstone/menu-current.png");
            }


            a,
            a:link,
            a:visited {
                color: @textColorSecondary;
            }

        }

    }

}

.html-rtl #tabzilla {
    float: left;
}

.html-rtl #masthead {
    flex-direction: row-reverse;
}

#masthead .toggle {
    display: none; /* Hidden in non-mobile views */
}


/* }}} */
/* {{{ Header Breadcrumbs */

#masthead {

    nav.breadcrumbs {
        padding: 0 10px (@baseLine / 2) 10px;
        width: 100%;
        text-align: left;
        order: 1;

        a,
        span {
            margin-right: .5em;
            margin-left: .5em;
        }

        a:first-child,
        span:first-child {
            margin-left: 0;
        }

    }

}

/* }}} */
/* {{{ Headers/Titles */

.title-shadow-box {
    .open-sans-light;
    width: 420px;
    padding: 20px;
    .font-size(48px);
    letter-spacing: -.05em;
    color: #fff;
    text-shadow: none;
    background: #b30406;
    background: rgba(179,4,6,.95);
    margin: -60px 0 40px;
    position: relative;
    float: left;
}

.title-shadow-box:after {
    content: "";
    display: block;
    width: 100%;
    padding: 0;
    height: 40px;
    position: absolute;
    bottom: -40px;
    left: 0;
    background: url("/media/img/mission/title-banner-shadow.png") no-repeat;
}

html[dir="rtl"] {
    .title-shadow-box {
        float: right;

        &:after {
            .flip-horizontally;
        }
    }
}

/* }}} */
/* {{{ Menu Bars */

.billboard {
    padding-top: @baseLine * 2;
    padding-bottom: @baseLine * 2;
    margin-bottom: @baseLine * 2;
    @shadow: 0 0 0 1px #fff inset;
    .box-shadow(@shadow);
    background: #fff;
    border-bottom: 1px solid #ddd;
    .clearfix;
    h1, h2, h3, h4, h5, h6, .huge, .large {
        color: @textColorSecondary;
    }
}

nav.menu-bar {
    text-align: center;
    .open-sans-light;
    margin-bottom: @baseLine * 2;
    padding-top: 0;
    padding-bottom: 0;

    ol,
    ul {
        margin: 0;
        li {
            .inline-block;
            margin: 0;
        padding-top: (@baseLine / 2);
        padding-bottom: (@baseLine / 2);
            a {
                .inline-block;
                border-left: 1px dotted @borderColor;
                padding: @baseLine / 3 @baseLine;
                span {
                    display: block;
                }
            }
            &:first-child a {
                border-left: 0;
            }
        }
    }
}

/* }}} */
/* {{{ Tables */

.table {
    border-collapse: collapse;
    border-spacing: 0;
    margin-bottom: @baseLine;
    caption {
        text-align:left;
        padding-bottom:1px; /* fix to get it to look nice in many browsers */
        margin-bottom:-1px;
        width:100%;
    }
    th,
    td {
        border-top: 1px solid rgba(0,0,0,0.2);
        padding: 10px @gridGutterWidth 10px 0;
        text-align: left;
    }
    thead {
        th,
        td {
            border-top: 0;
            padding-top: 0;
            .font-size(@largeFontSize);
            .open-sans-light;
        }
    }
}

.html-rtl .table caption,
.html-rtl .table th,
.html-rtl .table td {
    text-align: right;
}

/* }}} */
/* {{{ Sidebar */

.sidebar {

    nav,
    .nav {
        .open-sans-light;
        .font-size(@largeFontSize);
        color: @textColorSecondary;

        li {
            list-style-type: none;
            border-bottom: 1px dotted #ccc;
            margin: 0;
            line-height: 1.1;

            a,
            b {
                display: block;
                padding: 8px 0;
            }

            li b {
                font-weight: bold;
            }

        }

        li:first-child {
            .font-size(24px);
        }

    }

    .reference {
        margin: @baseLine * 2  auto;

        .more {
            display: block;
            padding: (@baseLine / 2) 0;
            border-bottom: 1px dotted @borderColor;
            .open-sans-light;
        }

        p {
            margin: 0;
        }
    }
}

/* }}} */
/* {{{ Footer Email Form */

.newsletter-form {
    margin-bottom: 0;

    .form-title {
        .span(4);
    }

    .form-contents {
        .span(4);
    }

    .form-submit {
        .span(3);
        input {
            // fix for IE 6/7 treating min-width as width
            // for submit input
            overflow: visible;
        }
    }

    select {
        max-width: 80%;
    }

    input[type=email] {
        .border-box();
        width: 100%;
        padding: 19px 10px;
    }

    .field-privacy {
        .font-size(@smallFontSize);
        input {
            float: left;
        }
        .title {
            display: block;
            padding: 0 0 0 25px;
        }
    }

    &.loading {
        .form-title,
        .form-contents,
        .form-submit {
            .transition(opacity .2s ease-in-out);
            opacity: 0.2;
        }
    }
}

.html-rtl {
    .newsletter-form {
        .form-title,
        .form-contents,
        .form-submit,
        .field-privacy input {
            float: right;
        }
        .field-privacy .title {
            padding: 0 25px 0 0;
        }
    }
}

.newsletter-form.thank {
    display: none;
    overflow: hidden;
    .form-title {
        width: auto;
        margin: auto;
        padding: 0;
        float: none;
        .animation(sand-bounce-down 0.7s ease-in 0.2s 1 normal both);
    }
    p {
        margin-top: @baseLine;
        .animation(sand-fade-in 0.5s ease-in 0.8s 1 normal both);
    }
}
.js {
    #form-details,
    .form-details { display: none; }
    .has-errors #form-details,
    .has-errors .form-details { display: block; }
    p.form-details {
        margin-top: 8px;
        line-height: 1;
    }
}

#footer-email-errors .errorlist,
#newsletter-errors .errorlist {
    .container;
    background: #AF3232;
    color: #fff;
    padding-top: @baseLine / 2;
    padding-bottom: @baseLine / 2;
}

#newsletter-spinner {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* }}} */
/* {{{ Footer */

#colophon {
    .font-size(14px);
    background: #fff;
    color: @textColorTertiary;
    line-height: 1.5;
    margin: 0;
    padding: 40px 0;
    width: 100%;

    p {
        color: inherit;
    }

    a:link,
    a:visited {
        color: @linkBlue;
        text-decoration: none;
    }

    a:hover,
    a:focus,
    a:active {
        color: @linkBlueHover;
        text-decoration: underline;
    }

    .logo {
        margin-bottom: .5em;

        a {
            .image-replaced();
            display: inline-block;
            background: url('/media/img/pebbles/moz-wordmark-dark-reverse.svg') no-repeat;
            .background-size(100px, 32px);
            height: 32px;
            width: 100px;
        }
    }

    li {
        margin: 0;
        list-style-type: none;
    }

    .twitter-button {
        font: normal normal normal 13px/26px 'Helvetica Neue',Arial,sans-serif;
        height: 28px;
        border-radius: 4px;
        max-width: 100%;
        box-sizing: border-box;
        position: relative;
        background-color: #1DA1F2;
        border: #CCC solid 1px;
        color: #333;
        font-weight: bold;
        text-shadow: 0 1px 0 rgba(255, 255, 255, .5);
        -webkit-user-select: none;
        -moz-user-select: none;
        -o-user-select: none;
        user-select: none;
        cursor: pointer;
        overflow: hidden;
        display: inline-block;
        vertical-align: top;
        zoom: 1;
        text-decoration: none;

        li:before {
            content: none;
        }

        &:hover {
            text-decoration: none;
        }

        i {
            width: 32px;
            height: 24px;
            margin-top: -16px;
            position: absolute;
            top: 50%;
            background: url(/media/img/thunderbird/twitter.png) 0 0 / ~"32px auto" no-repeat;
        }

        div {
            padding: 0px 7px 0 29px;
            white-space: nowrap;
            color: white;
        }
    }

    .facebook {
        position: relative;
        display: inline-block;

        i {
            width: 24px;
            height: 24px;
            margin-top: -8px;
            margin-left: 1px;
            position: absolute;
            top: 50%;
            background: url(/media/img/thunderbird/facebook.png) 0 0 / ~"24px auto" no-repeat;
        }
        div {
            padding: 10px 7px 0 29px;
            white-space: nowrap;
            color: #3b5998;
        }

        &:hover {
            text-decoration: none;
        }
    }

    &.dark {
        background: #000;
        color: #fff;

        a:link,
        a:visited {
            color: #23c7db;
            text-decoration: none;
        }

        a:hover,
        a:focus,
        a:active {
            color: #23c7db;
            text-decoration: underline;
        }
    }

    &.blue {
        background-color: #005189;
        color: #fff;

        a:link,
        a:visited {
            color: #bee1f5;
            text-decoration: none;
        }

        a:hover,
        a:focus,
        a:active {
            color: #fff;
            text-decoration: underline;
        }
    }

    &.dark,
    &.blue {
        .logo a {
            background-image: url('/media/img/pebbles/moz-wordmark-light-reverse.svg');
        }
    }
}

#colophon.universal {

    h5 {
        color: #000;
        font-weight: bold;
    }

    .row {
        .border-box;
        .clearfix;
        margin: 0 auto;
        padding: 0 10px;
        width: @widthDesktop;
    }

    .col {
        .span(4);
    }

    .primary {
        border-bottom: 1px solid @textColorTertiary;

        .col-1 {
            .span(2);
        }
        .col-2, .col-3 {
            .span(5);
        }
    }

    &.dark,
    &.blue {
        h5 {
            color: #fff;
            text-shadow: none;
        }

        .primary {
            border-bottom: 1px solid #fff;
        }
    }

    .secondary {
        padding-top: 20px;

        .col-1 {
            .span(4);
        }
        .col-2 {
            .span(8);
        }
    }

    .license {
        margin-bottom: 0;
    }

    .social-links {
        display: inline;
        margin: 0;

        li {
            display: inline;

            &:before {
                content: "\00B7\00A0"; /* &middot;&nbsp; */
            }

            &:first-child:before {
                content: "";
            }

            span {
                .visually-hidden; /* for a11y */
            }
        }
    }

    .legal-links {
        li {
            display: inline;
            &:before {
                content: " | ";
            }
            &:first-child:before {
                content: "";
            }
        }
    }
}

.html-rtl #colophon.universal {
    .col {
        float: right;
    }
}

#colophon.old {

    p, ul {
        margin-bottom: @baseLine / 2;
        line-height: 1.5;
    }

    li {
        margin: 0;
        list-style-type: none;
    }

    .row {
        width: @widthDesktop - (@gridGutterWidth * 2);
        margin: 0 auto;
        .clearfix;
    }

    .col {
        .span(4);

        & > *:last-child {
            margin-bottom: 0;
        }
    }

    .col-2 ul {
        overflow: hidden;

        &.fx-footer-links {
            li {
                float: none;
            }
        }

        li {
            float: left;

            &.wrap:before {
                content: "\00A0\00B7\00A0"; /* &nbsp;&middot;&nbsp; */
            }

            &.clear {
                clear: both;
            }
        }
    }

    .links-social li {
        ul {
            display: inline;
            margin: 0;
        }

        li {
            display: inline;

            &:before {
                content: "\00B7\00A0"; /* &middot;&nbsp; */
            }

            &:first-child:before {
                content: "";
            }

            span {
                .visually-hidden(); /* for a11y */
            }
        }
    }
}

.html-rtl #colophon.old {
    .col, .col-2 ul li {
        float: right;
    }
}

.visually-hidden {
    .visually-hidden();
}

.hidden {
    .hidden();
}

.invert {
    transform: rotateY(-180deg);
    transition: transform 1s ease-in-out;
}

/* }}} */
/* {{{ Responsive Grid */

/* Tablet Layout: 760px */
@media only screen and (min-width: @breakTablet) and (max-width: @breakDesktop) {

    #wrapper {
      width: @widthTablet;
    }

    .mozID #wrapper .container {
        width: @widthTablet;
    }

    #masthead,
    #main-feature,
    #main-content,
    .billboard,
    .container {
        width: @widthTablet - (@gridGutterWidth * 2);
    }

    .main-column {
        .span_narrow(8);
    }

    nav.menu-bar {
        .font-size(13px);
    }

    .sidebar {
        .span_narrow(3);
        .offset_narrow(1);
    }

    .footer-newsletter-form,
    #newsletter-form {
        .form-title {
            .span_narrow(3);
            padding-top: 4px;
        }

        .form-contents {
            .span_narrow(5);
        }

        .form-submit {
            .span_narrow(4);
        }
    }

    #colophon.universal {
        .row {
            width: @widthTablet - (@gridGutterWidth * 2);
            padding: 0;
        }

        .primary {
            .col-1 {
                .span_narrow(2);
            }
            .col-2, .col-3 {
                .span_narrow(5);
            }
        }

        .secondary {
            padding-top: 20px;
            .col-1 {
                .span(4);
            }
            .col-2 {
                .span(5);
            }
        }

        .license {
            margin-bottom: @baseLine / 2;
        }
    }

    .html-rtl #colophon.universal .col {
        float: none;
    }

    #colophon.old {
        .row {
            width: @widthTablet - (@gridGutterWidth * 2);
        }

        .col-1 {
            .span-all();
            overflow: hidden;
            margin-bottom: @baseLine;
        }

        .col-2,
        .col-3 {
            .span_narrow(6);
        }

        .logo {
            float: left;
            margin-bottom: 0;
            width: 120px;
        }

        .license {
            margin: 0 0 0 120px;
        }
    }

    .html-rtl #colophon.old {
        .col-2,
        .col-3,
        .logo {
            float: right;
        }

        .license {
            margin: 0 120px 0 0;
        }
    }
}


/* Mobile Layout: 320px */
@media only screen and (max-width: @breakTablet) {

    #wrapper {
      width: @widthMobile;
    }

    .mozID #wrapper .container {
        width: @widthMobile;
    }

    #masthead,
    #main-feature,
    #main-content,
    #colophon,
    .billboard,
    .container,
    .main-column,
    .sidebar {
        width: auto;
        padding-left: @gridGutterWidth / 2;
        padding-right: @gridGutterWidth / 2;
        padding-bottom: @gridGutterWidth;
    }

    .main-column,
    .sidebar {
        padding-left: 0;
        padding-right: 0;
        .span-all();
    }

    .row {
        margin-left: auto;
    }

    #masthead {
        flex-direction: column-reverse;
        align-items: start;

        .toggle {
            display: block;
            width: 32px;
            height: 32px;
            /*margin: -3px 0 0 -3px;*/
            background: no-repeat center top url("/media/img/sandstone/icn-menu.png");
            text-indent: -999em;
            overflow: hidden;
            cursor: pointer;
            position:relative;
            top:-3px;
        }
        .toggle.open {
            background-position: center -100px;
        }

        h2 {
            padding-top: @baseLine / 2;
            padding-bottom: @baseLine / 2;
            order: 2;
        }

        nav {
            padding-top: 10px;
            margin-left: 10px;
            order: 3;

            ul li {
                display: block;
                a,
                b {
                    display: block;
                    padding: 0;
                }

                b,
                .current {
                    background-image: none;
                }
            }
        }

        nav.breadcrumbs {
            margin-left: 0;
            a,
            span {
                margin-right: .3em;
                margin-left: .3em;
            }
        }
    }

    .html-rtl #masthead {
        flex-direction: column-reverse;
        align-items: end;
    }

    .space #masthead nav li,
    .blueprint #masthead nav li {
        a,
        a:link,
        a:visited {
            color: @textColorSecondary;
        }
    }

    .huge,
    .huge h1,
    .large,
    .large h1 {
        .font-size(48px);
    }

    h1,
    .large h2 {
        .font-size(32px);
    }

    h2,
    .billboard h2 {
        .font-size(28px);
    }

    h3 {
        .font-size(24px);
    }

    h4 {
        .font-size(@largeFontSize);
        letter-spacing: 0;
    }

    h5 {
        .font-size(@baseFontSize);
        letter-spacing: 0;
    }

    dl dt {
        .font-size(24px);
    }

    nav.menu-bar {
        text-align: inherit;
        line-height: 1;

        ul {
            padding-top: @baseLine / 2;
            padding-bottom: @baseLine / 2;
            li {
                display: block;
                padding: 0;
                a {
                    padding: @baseLine / 2;
                    border: 0;
                    display: block;
                    border-top: 1px dotted @borderColor;
                    border-left: 0;
                }
                &:first-child a {
                    border-top: 0;
                }
            }
        }
    }

    .title-shadow-box {
        width: 340px;
        .font-size(28px);
        margin: -25px 0 @baseLine;
    }

    .footer-newsletter-form,
    #newsletter-form {
        .form-title,
        .form-contents,
        .form-submit {
            width: auto;
            float: none;
            margin-bottom: 10px;
        }

        .form-submit {
            margin-top: @baseLine / 2;
        }
    }

    .html-rtl {
        .footer-newsletter-form,
        #newsletter-form {
            .form-title,
            .form-contents,
            .form-submit {
                float: none;
            }
        }
    }

    #colophon.universal {
        .row {
            width: @widthMobileLandscape - @gridGutterWidth;
        }

        .primary {
            border-bottom: none;
        }

        &.dark,
        &.blue {
            .primary {
                border-bottom: none;
            }
        }

        .primary,
        .secondary {
            .col {
                width: auto;
                float: none;
                margin-bottom: @baseLine / 2;
            }
        }

        .license {
            margin-bottom: @baseLine / 2;
        }
    }

    #colophon.old {
        .row {
            width: @widthMobile - @gridGutterWidth;
        }

        .col {
            width: auto;
            float: none;
            margin-bottom: @baseLine / 2;
        }
    }

    .html-rtl #colophon .col {
        float: none;
    }

    #nav-main {

        #nav-main-menu li {
            display: block;
            float: none;
            margin: 0 10px;
            border-bottom: 1px solid #f2f2f2;
        }

        #nav-main-menu li:last-child {
            border: 0;
        }

        .submenu hr {
            display: none;
        }

        #nav-main-menu a,
        #nav-main-menu b {
            cursor: pointer;
            display: block;
            text-transform: none;
            padding: 12px 35px 12px 10px;
            margin: 0 -10px;
        }

        #nav-main-menu a.submenu-item {
            background: 94% 50% no-repeat url("/media/img/sandstone/arrow-go.png");
        }

        #nav-main-menu a:hover,
        #nav-main-menu a:focus,
        #nav-main-menu a:active {
            color: #fff;
            text-decoration: none;
            text-shadow: 1px 1px 0 rgba(0,0,0,.25);
            background-color: #247ac1;
            background-position: 94% 50%;
            background-repeat: no-repeat;
            background-image: -webkit-linear-gradient(top, #43a6e2, #247ac1);
            background-image: linear-gradient(to bottom, #43a6e2, #247ac1);
        }

        #nav-main-menu a.submenu-item:hover,
        #nav-main-menu a.submenu-item:focus,
        #nav-main-menu a.submenu-item:active {
            background-image: url("/media/img/sandstone/arrow-go.png"), -webkit-linear-gradient(top, #43a6e2, #247ac1);
            background-image: url("/media/img/sandstone/arrow-go.png"), linear-gradient(to bottom, #43a6e2, #247ac1);
        }

        #nav-main-menu li.first > a {
            border-radius: 10px 10px 0 0;
        }

        #nav-main-menu li.last > a {
            border-radius: 0 0 10px 10px;
        }
    }

    #nav-main-menu {
        background: #fff;
        position: absolute;
        z-index: 99;
        width: 200px;
        margin: 20px 0 0 -20px;
        left: -999em;
        top: 30px;
        overflow: visible;
        .border-radius(10px);
        .box-shadow(0 1px 3px 0 rgba(0,0,0,.5));
    }

    #nav-main:hover #nav-main-menu,
    #nav-main-menu:target {
        left: auto;
    }

    .js #nav-main-menu {
        left: auto;
        display: none;
    }

    #nav-main-menu:before {
        content: "";
        display: block;
        width: 28px;
        height: 10px;
        background: no-repeat url("/media/img/sandstone/menu-point.png");
        position: absolute;
        left: 22px;
        top: -10px;
    }

    .html-rtl #masthead nav {
        float: right;
    }

    .html-rtl #masthead .toggle {
        left: auto;
        right: 10px;
    }

    .html-rtl #nav-main-menu {
        margin-left: 0;
        margin-right: 0;
        right: 60px;
    }

    .html-rtl #nav-main-menu:before {
        left: auto;
        right: 12px;
    }
}


/* Wide Mobile Layout: 480px */
@media only screen and (min-width: @breakMobileLandscape) and (max-width: @breakTablet) {

    #wrapper {
        width: @widthMobileLandscape;
    }

    #masthead,
    #main-feature,
    #main-content,
    .billboard,
    .container,
    .main-column,
    .sidebar {
        width: auto;
    }

    .main-column,
    .sidebar {
        padding-left: 0;
        padding-right: 0;
    }

    .mozID #wrapper .container {
        width: @widthMobileLandscape;
    }

    #colophon.universal,
    #colophon.old {
        .row {
            width: @widthMobileLandscape;
        }
    }
}
/* }}} */

@media only screen and (max-width: @breakMobileLandscape) {
    .title-shadow-box {
        width: 240px;
    }

    .title-shadow-box:after {
        display: none;
    }

    #colophon.universal,
    #colophon.old {
        .row {
            width: @widthMobile - @gridGutterWidth;
        }
    }
}

// Don't display the dynamic platform images when js is disabled
// because the ones in the noscript tag will be shown
.no-js .platform-img.js {
    display: none;
}

/* }}} */
