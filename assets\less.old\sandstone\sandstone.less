@import "lib.less";
@import "reset.less";
@import "fonts.less";
@import "buttons.less";
@import "animations.less";

/* {{{ Basic Colors, Text, Links */

html {
    background: #fff;
    .font-size(100%);
}

body {
    .font-size(@baseFontSize);
    line-height: 1.5;
    font-family: @baseFontFamily;
    color: @textColorPrimary;
    background: #fff;
}

#outer-wrapper {
    position: relative;
    border-top: 2px solid #fff;
    min-width: 992px;
    background: #f9f9f9 url("/media/img/sandstone/bg-stone.png") 0 0 repeat-x;
}

#wrapper {
    padding-bottom: @baseLine * 2;
}

a {
    color: @linkBlue;
    text-decoration: none;

    &:hover,
    &:focus,
    &:active {
        color: darken(@linkBlue, 10%);
        text-decoration: underline;
    }

    &:visited {
        color: darken(@linkBlue, 10%);
    }
}

// Add a double-arrow after links
a.more {
    .trailing-arrow();
}

.sand #outer-wrapper {
    background: #f5f1e8 url("/media/img/sandstone/bg-gradient-sand.png") repeat-x;
    background: url("/media/img/sandstone/bg-gradient-sand.png") repeat-x,
                url("/media/img/sandstone/bg-sand.png") repeat,
                #f5f1e8;
}

.sky {
    #outer-wrapper {
        background: #eee url("/media/img/sandstone/bg-gradient-sky.png") repeat-x;
        background: url("/media/img/sandstone/bg-gradient-sky.png") repeat-x,
                    url("/media/img/sandstone/grain.png") repeat,
                    #eee;
    }

    a {
        color: @linkSkyBlue;
        &:hover,
        &:focus,
        &:active {
            color: darken(@linkSkyBlue, 10%);
        }
    }
}

// Daily visual style
.space {
    color: #fff;
    background: #000;

    #outer-wrapper {
        background: url('/media/img/firefox/horizon/stars.svg') center 60px no-repeat,
                    linear-gradient(to bottom, #000, #002048 1000px, #000 2000px);
    }

    a {
        color: @linkBlue;
        &:hover,
        &:focus,
        &:active {
            color: lighten(@linkBlue, 10%);
        }
    }

    h1, h2, h3, h4, h5, h6, .huge, .large {
        color: #fff;
        text-shadow: none;
    }

    #masthead nav li {
        a,
        a:link,
        a:visited {
            color: @linkBlue;
        }
        li {
            a,
            a:link,
            a:visited {
                color: @textColorSecondary;
            }
        }
    }

}

// Firefox Developer Edition visual style
.blueprint {
    color: #fff;

    #outer-wrapper {
        background: #1e1e21;
        #gradient > .radial(top, left, ellipse, farthest-side, #00549e 0%, rgba(0, 0, 0, 0) 100%);
        background-size: 100% 500px;
        background-repeat: no-repeat;
        background-position: top center;
    }

    a {
        color: @linkBlue;
        &:hover,
        &:focus,
        &:active {
            color: lighten(@linkBlue, 10%);
        }
    }

    h1, h2, h3, h4, h5, h6, .huge, .large {
        color: #fff;
        text-shadow: none;
    }

    #masthead nav li {
        a,
        a:link,
        a:visited {
            color: #fff;
        }
        li {
            a,
            a:link,
            a:visited {
                color: @textColorSecondary;
            }
        }
    }

}

// mozID visual style
// full width rows w/differing bg colors
.mozID {
    a:link,
    a:active,
    a:focus,
    a:visited {
        color: #fff;

        &:hover,
        &:focus {
            text-decoration: underline;
        }
    }

    h1, h2, h3, h4, h5, h6, .huge, .large {
        color: #fff;
        text-shadow: none;
    }

    #wrapper {
        width: 100%;

        .container {
            width: @widthDesktop - (@baseLine * 2);
            padding-left: @baseLine;
            padding-right: @baseLine;
        }
    }

    #masthead {
        z-index: 10;
        padding-left: 0;
        padding-right: 0;
    }
}

html[dir='rtl'] #masthead nav {
    float: left;
}

h1, h2, h3, h4, h5, h6, .huge, .large {
    .open-sans-light;
    display: block;
    margin: 0 0 12px 0;
    line-height: 1;
    text-shadow: 0 1px 0 rgba(255, 255, 255, .75);
    color: @textColorSecondary;
    letter-spacing: -.035em;
}

.huge,
.huge h1 {
    .font-size(108px);
}

.large,
.large h1 {
    .font-size(72px);
}

h1,
.huge h2,
.large h2,
.billboard h2 {
    .font-size(48px);
}

h2 {
    .font-size(32px);
}

h3 {
    .font-size(28px);
}

h4 {
    .font-size(24px);
}

h5 {
    .font-size(@largeFontSize);
}

h6 {
    .font-size(@baseFontSize);
}

.small,
small {
    .font-size(@smallFontSize);
    line-height: 1.3;
}

hgroup {
    h1, h2, h3, h4, h5, h6 {
        margin-bottom: 0;
    }
}

p,
ul,
ol,
dl,
hgroup {
    margin: 0 0 24px 0;
}

li {
    margin-left: 24px;
}

dl dt {
    .open-sans-light;
    .font-size(32px);
    line-height: 1;
    letter-spacing: -.035em;
    margin-bottom: @baseLine / 2;
}

dl dd {
    margin-bottom: @baseLine * 2;
}

dl.simple dt {
    .font-size(18px);
    font-weight: bold;
}

dl.simple dd {
    margin-bottom: @baseLine;
}

code {
    color: @textColorTertiary;
    .font-size(@baseFontSize);
}

.center {
    text-align: center;
}

hr {
    margin: @baseLine 0;
    border-bottom: 1px dotted @borderColor;
}

img[data-high-res] {
    display: none;
}

.js img[data-high-res] {
    display: inline;
}

/* }}} */
/* {{{ Forms */

textarea,
input[type=email],
input[type=password],
input[type=text],
input[type=tel] {
    height: @baseLine;
    background: #fafafa;
    border-color: #dbdbdb;
    border-style: solid;
    border-width: 1px;
    @shadow: 0 2px 1px rgba(0,0,0,0.1) inset;
    padding: (@baseLine / 6)  (@baseLine / 3);
    .border-radius(6px);
    .box-shadow(@shadow);
    .transition(all linear .1s);
}

button,
input,
select,
textarea {
    font-family: inherit; // must inherit or falls back to UA stylesheet/system font
}

textarea { height: auto; }

textarea:focus,
input[type=email]:focus,
input[type=password]:focus,
input[type=text]:focus,
input[type=tel]:focus {
    border-color: #bbb;
    @shadow: 0 2px 1px rgba(0,0,0,0.1) inset, 0 0 8px 0 rgba(103,167,208,0.6);
    .box-shadow(@shadow);
    .transition(all linear .1s);
}

.field {
    margin-bottom: @baseLine / 3;
}

.errorlist {
    margin: 0;
    .open-sans;
    color: #c00;

    li {
        list-style-type: none;
        margin: 0;
    }
}

.field-error {
    input[type=email],
    input[type=password],
    input[type=text],
    input[type=tel] {
        border-color: rgb(175,50,50);
        @shadow: 0 2px 1px rgba(0,0,0,0.1) inset, 0 0 8px 0 rgba(175,50,50,.6);
        .box-shadow(@shadow);
    }
}

.super-priority-field {
    display: none;
    visibility: hidden;
}

/* }}} */
/* {{{ Layout */

#main-content,
#main-feature {
    padding-bottom: 48px;
}

.main-column {
    .span(6);
}

.sidebar {
    .span(2);
    .offset(2);
}

.divider.container,
.divider {
    border-bottom: 1px dotted @borderColor;
    padding-bottom: @baseLine * 2;
    margin-bottom: @baseLine *2;
}

.divider-last.container,
.divider-last {
    border-bottom: 0;
    padding-bottom: @baseLine * 2;
}

/* }}} */
/* {{{ Less Framework Grid */

/*      Default Layout: 992px.
        Gutters: 24px.
        Outer margins: 48px.
        Leftover space for scrollbars @1024px: 32px.
-------------------------------------------------------------------------------
cols    1     2      3      4      5      6      7      8      9      10
px      68    160    252    344    436    528    620    712    804    896    */


#masthead,
#main-feature,
#main-content,
#colophon,
.billboard,
.container {
    display: block;
    margin: 0 auto;
    padding-left: 48px;
    padding-right: 48px;
    position: relative;
    width: 896px;
    .clearfix
}

/* }}} */
/* {{{ Header Nav */

#masthead {

    h2 {
        padding: (@baseLine * 1.5) 0 @baseLine 0;
        margin: 0;
    }

    nav {
        float: right;
        margin-right: 16px;
        text-transform: uppercase;
        .font-size(13px);
        .open-sans;

        li {
            .inline-block;
            list-style-type: none;
            margin: 0;

            a,
            b {
                display: inline-block;
                padding: 12px;
                font-weight: normal;
            }

            b,
            .current {
                background-position: 50% 0;
                background-repeat: no-repeat;
                background-image: url("/media/img/sandstone/menu-current.png");
            }


            a,
            a:link,
            a:visited {
                color: @textColorSecondary;
            }

        }

    }

}


/* }}} */
/* {{{ Header Breadcrumbs */

#masthead {

    nav.breadcrumbs {
        padding: 0 0 (@baseLine / 2)  0;
        float: none;

        a,
        span {
            margin-right: .5em;
            margin-left: .5em;
        }

        a:first-child,
        span:first-child {
            margin-left: 0;
        }

    }

}

/* }}} */
/* {{{ Menu Bars */

.billboard {
    padding-top: @baseLine * 2;
    padding-bottom: @baseLine * 2;
    margin-bottom: @baseLine * 2;
    @shadow: 0 0 0 1px #fff inset;
    .box-shadow(@shadow);
    background: #fff;
    border-bottom: 1px solid #ddd;
    .clearfix;
    h1, h2, h3, h4, h5, h6, .huge, .large {
        color: @textColorSecondary;
    }
}

nav.menu-bar {
    text-align: center;
    .open-sans-light;
    margin-bottom: @baseLine * 2;
    padding-top: 0;
    padding-bottom: 0;

    ul {
        margin: 0;
        li {
            .inline-block;
            margin: 0;
        padding-top: (@baseLine / 2);
        padding-bottom: (@baseLine / 2);
            a {
                .inline-block;
                border-left: 1px dotted @borderColor;
                padding: @baseLine / 3 @baseLine;
                span {
                    display: block;
                }
            }
            &:first-child a {
                border-left: 0;
            }
        }
    }
}

/* }}} */
/* {{{ Box Styles */

.inset {
    background-color: rgb(233,233,233);
    background-color: rgba(0,0,0,0.02);
    @shadow: 0 0 3px rgba(0,0,0,0.1) inset;
    .box-shadow(@shadow);
    border-bottom: 1px solid #fff;
}

/* }}} */
/* {{{ Sidebar */

.sidebar {

    nav,
    .nav {
        .open-sans-light;
        .font-size(@largeFontSize);
        color: @textColorSecondary;

        li {
            list-style-type: none;
            border-bottom: 1px dotted #ccc;
            margin: 0;

            a,
            b {
                display: block;
                padding: 8px 0;
            }

            b {
                .open-sans;
                font-weight: normal;
            }

        }

        li:first-child {
            .font-size(24px);
        }

    }

    .reference {
        margin: @baseLine * 2  auto;

        .more {
            display: block;
            padding: (@baseLine / 2) 0;
            border-bottom: 1px dotted @borderColor;
            .open-sans-light;
        }

        p {
            margin: 0;
        }
    }
}

/* }}} */
/* {{{ Footer Email Form */

.footer-newsletter-form,
#newsletter-form {
    padding-top: @baseLine;
    padding-bottom: @baseLine;
    margin-bottom: 0;

    .form-title {
        .span(3);
        margin-left: 0;
    }

    .form-contents {
        .span(4);
    }

    .form-submit {
        .span(3);
    }

    .field {
        padding: 0 0 8px 0;
    }

    input[type=email], select {
        width: (@gridColumnWidth * 3) + (@gridGutterWidth * 2) - 20px;
    }

    .field-privacy {
        .font-size(@smallFontSize);
        input {
            float: left;
        }
        .title {
            display: block;
            padding: 0 0 0 25px;
        }
    }

    &.loading {
        .form-title,
        .form-contents,
        .form-submit {
            .transition(opacity .2s ease-in-out);
            opacity: 0.2;
        }
    }
}

.html-rtl {
    .footer-newsletter-form,
    #newsletter-form {
        .form-title,
        .form-contents,
        .form-submit,
        .field-privacy input {
            float: right;
        }
        .field-privacy .title {
            padding: 0 25px 0 0;
        }
    }
}

.newsletter-form.thank {
    display: none;
    overflow: hidden;
    .form-title {
        width: auto;
        margin: auto;
        padding: 0;
        float: none;
        .animation(sand-bounce-down 0.7s ease-in 0.2s 1 normal both);
    }
    p {
        margin-top: @baseLine;
        .animation(sand-fade-in 0.5s ease-in 0.8s 1 normal both);
    }
}
.js {
    #form-details,
    .form-details { display: none; }
    .has-errors #form-details,
    .has-errors .form-details { display: block; }
    p.form-details {
        margin-top: 8px;
        line-height: 100%;
        color: @textColorSecondary;
    }
}

#footer-email-errors .errorlist,
#newsletter-errors .errorlist {
    .container;
    background: #AF3232;
    color: #fff;
    padding-top: @baseLine / 2;
    padding-bottom: @baseLine / 2;
}

#newsletter-spinner {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* }}} */
/* {{{ Footer */

#colophon {
    .font-size(@baseFontSize);
    background: #fff;
    color: @textColorTertiary;
    line-height: 1.3;
    margin: 0;
    padding: 48px 0;
    width: 100%;

    a:link,
    a:visited {
        color: @linkBlue;
        text-decoration: none;
    }

    a:hover,
    a:focus,
    a:active {
        color: @linkBlueHover;
        text-decoration: underline;
    }

    li {
        margin: 0;
        list-style-type: none;
    }

    .row {
        width: 960px;
        margin: 0 auto;
    }

    p, ul {
        margin-bottom: @baseLine / 2;
        line-height: 1.5;
    }

    .logo {
        margin-bottom: .5em;

        a {
            .image-replaced();
            display: inline-block;
            background: url('/media/img/pebbles/moz-wordmark-dark-reverse.svg') no-repeat;
            .background-size(100px, 32px);
            height: 32px;
            width: 100px;
        }
    }

    &.dark {
        background: #000;
        color: #fff;

        a:link,
        a:visited {
            color: #23c7db;
            text-decoration: none;
        }

        a:hover,
        a:focus,
        a:active {
            color: #23c7db;
            text-decoration: underline;
        }
    }

    &.blue {
        background-color: #005189;
        color: #fff;

        a:link,
        a:visited {
            color: #bee1f5;
        }

        a:hover,
        a:focus,
        a:active {
            color: #fff;
            text-decoration: underline;
        }
    }

    &.dark,
    &.blue {
        .logo a {
            background-image: url('/media/img/pebbles/moz-wordmark-light-reverse.svg');
        }
    }
}

#colophon.universal {

    .row {
        .clearfix;
    }

    h5 {
        color: #000;
        font-weight: bold;
    }

    ul {
        margin-bottom: @baseLine;
    }

    .primary {
        border-bottom: 1px solid @textColorTertiary;

        .col-1 {
            width: 140px;
        }
        .col-2, .col-3 {
            width: 374px;
        }
    }

    &.dark,
    &.blue {
        h5 {
            color: #fff;
            text-shadow: none;
        }

        .primary {
            border-bottom: 1px solid #fff;
        }
    }

    .secondary {
        padding-top: @baseLine;

        .col-1 {
            width: 300px;
        }
        .col-2 {
            width: 612px;
        }
    }

    .col {
        float: left;
        margin-left: 24px;
    }

    .social-links {
        display: inline;
        margin: 0;

        li {
            display: inline;

            &:before {
                content: "\00B7\00A0"; /* &middot;&nbsp; */
            }

            &:first-child:before {
                content: "";
            }

            span {
                .visually-hidden; /* for a11y */
            }
        }
    }

    .legal-links {
        li {
            display: inline;
            &:before {
                content: " | ";
            }
            &:first-child:before {
                content: "";
            }
        }
    }
}

.html-rtl #colophon.universal {
    .col {
        float: right;
    }
}

#colophon.old {

    .col {
        float: left;
        margin-left: 24px;
        width: 282px;

        & > *:last-child {
            margin-bottom: 0;
        }
    }

    .col-2 ul {
        overflow: hidden;

        &.fx-footer-links {
            li {
                float: none;
            }
        }

        li {
            float: left;

            &.wrap:before {
                content: "\00A0\00B7\00A0"; /* &nbsp;&middot;&nbsp; */
            }

            &.clear {
                clear: both;
            }
        }
    }

    .links-social li {
        ul {
            display: inline;
            margin: 0;
        }

        li {
            display: inline;

            &:before {
                content: "\00B7\00A0"; /* &middot;&nbsp; */
            }

            &:first-child:before {
                content: "";
            }

            span {
                .visually-hidden(); /* for a11y */
            }
        }
    }
}

.html-rtl #colophon.old {
    .col, .col-2 ul li {
        float: right;
    }
}

/* }}} */

// Don't display the dynamic platform images when js is disabled
// because the ones in the noscript tag will be shown
.no-js .platform-img.js {
    display: none;
}

.visually-hidden {
    .visually-hidden();
}

.hidden {
    .hidden();
}

/* }}} */
