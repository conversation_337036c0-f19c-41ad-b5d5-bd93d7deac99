// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

@import "../sandstone/lib.less";

.build-table-container {
    .hide {
        display: none;
    }
}

#main-feature {
    h1 {
        .span-all;
        margin-bottom: @baseLine;
    }
    h2 {
        .font-size(24px);
        letter-spacing: -0.25px;
        line-height: 1.4;
        .span-all();
    }
    ul, p {
        .span-all();
        margin-top: @baseLine;
    }
    p.warning {
        font-size: @largeFontSize;
    }
    li {
        margin: 0;
        list-style-type: none;
        a:after {
            content: "\00A0\00BB";
        }
    }
}

.space #main-feature h2 {
    color: #FFF;
}

#language-search {
    padding-top: @baseLine;
    padding-bottom: @baseLine;
    input[type=search] {
        .span(3);
        float: none;
        margin-left: 0;
        padding: 10px;
        height: 52px;
        vertical-align: top;
        line-height: 1.3;
    }
    button {
        width: 40%;
    }
    .search-column {
        float: left;
        width: 460px;
        padding: 5px 0;
    }
    .other-column {
        float: left;
        width: 380px;
        border-left: 1px dotted @borderColor;
        padding-left: ((@gridGutterWidth / 2) - 1px);

        h4 {
            margin-bottom: 2px;
        }
    }
}

#main-content {
    .localized {
        .span-all();
    }
    .localized-testing {
        .span-all();
        margin-top: @baseLine * 2;
    }
    table {
        width: 100%;
        thead {
            th {
                white-space: nowrap;
            }
            th, td {
                padding: 10px 0;
            }
        }
        tbody {
            tr:target {
                color: #fff;
                font-weight: bold;
                background-color: #6fbe4a;
            }
            tr:target a {
                color: #fff;
            }
            tr:target th {
                padding-left: 10px;
            }
            th, td {
                padding: 10px 10px 10px 0;
            }
        }
        .unavailable {
            padding: 10px;
            background-repeat: no-repeat;
            color: @textColorTertiary;
            text-shadow: 0 -1px #fff;
        }
        .download {
            padding: 10px 0;
            a {
                display: block;
                padding: 10px 10px 10px 45px;
                background: transparent url('/media/img/firefox/all/download-icons-desktop.png') left center no-repeat;
            }

            &.win64 a {
                background-position: -300px 50%;
            }
            &.linux a {
                background-position: -600px 50%;
            }
            &.linux64 a {
                background-position: -900px 50%;
            }
            &.osx a {
                background-position: -1200px 50%;
            }
            &[class*="android"] a {
                .at2x('/media/img/firefox/all/download-icons-android.png', 40px, 120px);
                background-position: 0 0;
            }
            &.android-x86 a {
                background-position: 0 -80px;
            }
        }
    }
}

/* Mozilla pager */
.pager-tabs-wrapper {
    position: absolute;
    top: 3px;
    right: 24px;
    z-index: 2;
    .font-size(@largeFontSize);
    .clearfix();

    .pager-tabs, .pager-title {
        float: left;
    }

    .pager-title {
        padding-right: @baseLine/2;
    }

    li {
        float: left;
        list-style-type: none;
        padding: 0;
        margin: 0;

        &:first-child:after {
            content: '| ';
        }
    }

    a {
        padding: 0 7px;

        &.selected {
            color: #000;
            cursor: default;

            &:hover {
                text-decoration: none;
            }
        }
    }
}

/* Desktop */
@media only screen and (min-width: @breakDesktop) {
    body.desktop.release #main-feature h1 {
        .font-size(64px);
    }
}

/* Tablet and Mobile */
@media only screen and (max-width: @breakDesktop) {
    #main-content table {
        .download a {
            overflow: hidden;
            margin: 0 auto;
            padding: 0;
            width: 40px;
            height: 40px;
            text-indent: 200%;
            white-space: nowrap;
        }
        .unavailable {
            text-align: center;
        }
    }
}

/* Tablet Layout: 760px */
@media only screen and (min-width: @breakTablet) and (max-width: @breakDesktop) {

    #main-feature {
        h1 {
            .font-size(48px);
        }
        h2 {
            .span-all();
        }
    }

    #language-search {
        input[type=search] {
            .span_narrow(3);
            float: none;
        }
        button {
            vertical-align: middle;
        }
        .search-column {
            width: 460px;
            padding: 5px 0;
        }
        .other-column {
            width: 310px;
            margin-top: @baseLine / 2;
            h4 {
                margin-bottom: 2px;
            }
        }
    }

    #main-content table thead th:not([colspan]) {
        text-align: center;
    }

}

/* Wide mobile layout: 480px; */
@media only screen and (min-width: @breakMobileLandscape) and (max-width: @breakTablet) {
    #main-content table tbody {
        th {
            width: 50%;
        }
        td[lang] {
            float: left;
            width: 50%;
        }
        td.download {
            margin-top: -18px;
        }
        tr:target th + td {
            padding-left: 10px;
        }
    }

    .pager-tabs-wrapper {
        position: relative;
        top: auto;
        right: auto;
        margin: 0 10px;
    }
}

/* Mobile layout: 320px */
@media only screen and (max-width: @breakTablet) {

    #main-feature {
        h1 {
            .font-size(36px);
        }
        h2 {
            .font-size(18px);
            letter-spacing: normal;
            .span-all();
        }
        p {
            margin-top: @baseLine / 2;
        }
    }

    #language-search {
        input[type=search] {
            width: 100%;
            float: none;
            margin-left: 0;
            vertical-align: middle;
        }
        button {
            width: 100%;
            margin-top: 10px;
        }
        .search-column {
            float: none;
            width: auto;
        }
        .other-column {
            margin: 0;
            float: none;
            width: auto;
            border-left: 0;
            padding-left: 0;
        }
    }

    #main-content {
        table, thead, tbody, th, td, tr, caption {
            display: block;
        }
        table {
            thead {
                display: none;
            }
            .unavailable {
                display: none;
            }
            .download {
                float: left;
                padding: 0;
            }
            tr {
                overflow: hidden;
                border-top: 1px solid rgba(0, 0, 0, 0.2);
            }
            tbody {
                th, td {
                    border: 0;
                }
                th {
                    padding: 5px 0 0;
                }
                td {
                    padding: 0 0 5px;
                }
                tr:target th + td {
                    padding-left: 10px;
                }
            }
        }
    }

    .pager-tabs-wrapper {
        position: relative;
        top: auto;
        right: auto;
        margin: 0 10px;
    }
}

/* The sha-1 download column is hidden by default */
.js .build-table {
    thead tr .winsha1,
    tbody tr .winsha1 {
        display: none;
    }
}

/* IE on Windows XP, Server 2003, Vista need sha-1 button */
.windows.sha-1 .build-table {
    thead tr .win,
    tbody tr .win {
        display: none;
    }

    thead tr .winsha1,
    tbody tr .winsha1 {
        display: block;
    }
}
