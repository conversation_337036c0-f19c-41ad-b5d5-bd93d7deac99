// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.


@import "../sandstone/lib.less";

#lightning {
    padding: (@baseLine * 2) (@baseLine * 4);
    .infobox {
        padding-bottom: @baseLine;
        float: right;
        h1, h2 {
            padding: 0;
            margin: 0;
            .open-sans-light;
            font-weight: normal;
            color: #484848;
            text-shadow: 0 1px 0 rgba(255, 255, 255, 0.75);
            text-align: center;
            line-height: 1.3;
        }
        h1 {
            .font-size(57px);
        }
    }
    h2 {
        letter-spacing: -0.5px;
        .font-size(24px);
    }
    .midbox {
        padding: @baseLine 0 0 0;
        border-top: 1px solid #d6d6d6;
    }
    .topbox {
        img {
            vertical-align: top;
        }
    }
    .holiday {
        float: none;
    }
    table.data {
        width: 100%;
        margin-bottom: @baseLine;
    }
    .calendar-table {
        padding: 0 20px;
    }
    ul.holiday-nav {
        text-align: center;
        padding: @baseLine 0;
        border-bottom: 1px solid #d6d6d6;
        li {
            display: inline;
            float: none;
            width: auto;
        }
    }
}

#download {
    display: inline-block;
    float: right;
    margin-top: @baseLine;
    #download-link {
        color: #fff;
        line-height: 1.3;
        background-color: #669be1;
        background-image: linear-gradient(to bottom, #669be1 50%, #5784bf);
        border-radius: 6px 6px 6px 6px;
        box-shadow: 0 3px rgba(0, 0, 0, 0.1), 0 -4px rgba(0, 0, 0, 0.1) inset;
        display: block;
        margin-bottom: 2px;
        overflow: visible;
        padding-right: 30px;
        span.title {
            background: url('/media/img/sandstone/buttons/download-arrow-small.png') no-repeat scroll 10px 14px transparent;
            display: block;
            .font-size(20px);
            font-style: italic;
            min-height: 30px;
            padding: 8px 8px 0 40px;
            text-shadow: 1px 1px rgba(0, 0, 0, 0.2);
        }
        span.desc {
            color: rgba(0, 0, 0, 0.7);
            display: block;
            margin-top: -4px;
            padding: 0 8px 12px 40px;
            text-shadow: 1px 1px rgba(255, 255, 255, 0.2);
        }
    }
    .download-other {
        color: #bbb;
        display: block;
        .open-sans;
        .font-size(11px);
        text-align: center;
    }
}


.items {
    margin: 0;
    padding: @baseLine 0;
    ul {
        padding: 0;
        margin: 0;
        .clearfix;
    }
    li {
        .span(5);
        .open-sans;
        padding: (@baseLine / 4) 0;
        .font-size(@smallFontSize);
        list-style-type: none;
        .itemtext {
            display: inline-block;
            vertical-align: middle;
            width: 80%;
            a.title {
                .font-size(20px);
            }
            span.desc {
                .font-size(@largeFontSize);
            }
        }
        a {
            display: block;
            &:hover,
            &:active {
                color: darken(@linkBlue, 10%);
                text-decoration: underline;

            }
            &:focus {
                text-decoration: none;
            }
        }
    }
    img {
        display: inline-block;
        vertical-align: middle;
        margin-right: @baseLine;
    }
}

.billboard {
    .transition(box-shadow 0.6s ease 0.2s);
    box-shadow: 0 0 0 0 #fff;
    width: auto;
}

@media only screen and (min-width: @breakTablet) and (max-width: @breakDesktop) {
    #lightning {
        padding: @baseLine;
    }
}

/* Mobile layout: 320px */
@media only screen and (max-width: @breakTablet) {

    #lightning {
        padding: (@baseLine / 2);
        .infobox {
            float: none;
            text-align: center;
            h1 {
                .font-size(36px);
            }
            #download {
                display: none;
            }
        }
        .topbox {
            padding: 0;
        }
        li {
            .span(4);
            margin: 0;
            padding: 0 0 (@baseLine) 0;
            clear: left;
            .itemtext {
                width: 60%;
                a.title {
                    .font-size(@baseFontSize);
                }
                span.desc {
                    .font-size(@smallFontSize);
                }
            }
        }
        #calendar-logo {
            .span_narrow(2);
        }
    }
}

/* Mobile layout: 0 - 480px; */
@media only screen and (max-width: @breakMobileLandscape) {

    #lightning {
        img {
            width: auto;
        }
        #calendar-logo {
            display: none;
        }
    }
}
