// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

@import "../sandstone/lib.less";

#main-feature {
    padding-bottom: 20px;

    h1 strong {
        display: block;
        .font-size(@largeFontSize * 4);
        font-weight: normal;
    }
}

#main-feature h1,
main h2 {
    .font-size(@largeFontSize * 2.5);
}

#main-feature h2 {
    .font-size(@largeFontSize * 1.25);
    line-height: 1.3;
}

main {
    .row {
        overflow: hidden;
        margin: 0 20px;
    }

    section {
        overflow: hidden;
        width: 45%;
        margin: 0 auto;
    }

    header {
        overflow: hidden;

        img {
            display: block;
            float: left;
            margin: 10px 0 0;
            width: 128px;
            height: 128px;
        }

        h2, ul {
            margin: 0 0 20px 150px;
        }

        ul {
            min-height: 7em;
        }

        li {
            margin: 10px 0 0;
            list-style-type: none;
        }
    }

    .download-box {
        text-align: center;
    }
}

/* Tablet Layout: <1000px */
@media only screen and (max-width: @breakDesktop) {
    main {
        header {
            text-align: center;

            img {
                float: none;
                margin: 0 auto 6px;
            }

            h2, ul {
                margin: 0 0 20px;
            }
        }
    }
}

/* Mobile & Wide Mobile layout: <760px */
@media only screen and (max-width: @breakTablet) {
    #main-feature h1 {
        .font-size(@largeFontSize * 3);

        strong {
            font-size: inherit;
        }
    }

    main {
        .row {
            margin: 0;
        }

        section {
            .span-all;
            margin-bottom: 40px;
        }

        header ul {
            min-height: 0;
        }
    }
}
