// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

@import "../sandstone/lib.less";

.sky #outer-wrapper {
    background: #eaeff2;
    background: -webkit-gradient(radial, center center, 0, center center, 60%, color-stop(0%,rgba(234,239,242,.9)), color-stop(60%,rgba(212,221,228,.5))),
                -webkit-linear-gradient(top, rgba(202,225,244,1) 0%, rgba(125,185,232,0) 100%);
    background: -webkit-radial-gradient(center, ellipse cover, rgba(234,239,242,1) 0%, rgba(212,221,228,.4) 60%),
                -webkit-linear-gradient(top, rgba(202,225,244,1) 0%, rgba(125,185,232,0) 100%);
    background: -ms-radial-gradient(center, ellipse cover,  rgba(234,239,242,1) 0%, rgba(212,221,228,.5) 60%),
                -ms-linear-gradient(top, rgba(202,225,244,1) 0%, rgba(125,185,232,0) 100%);
    background: radial-gradient(ellipse at center, rgba(234,239,242,1) 0%,rgba(212,221,228,.5) 50%),
                linear-gradient(to bottom, rgba(202,225,244,1) 0%,rgba(125,185,232,0) 100%);

    &:after {
        z-index: 0;
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        width: 100%;
        background: url('/media/img/thunderbird/landing/bg-noise.png');
    }
}

#wrapper {
    width: 980px;
    padding: 0 10px;
    z-index: 1;
}

#main-feature {
    margin-top: 14px;
    padding-bottom: 20px;

    img {
        display: none;
    }

    h1.large {
        margin: 12px 0;
        .font-size(60px);
        text-align: center;
    }

    p.large {
        margin: 12px 50px;
        .font-size(28px);
        line-height: 1.3;
        text-align: center;
    }
}

.download-button-wrapper {
    margin: 50px auto 0;
    width: 260px;
    text-align: center;

    .unsupported-download,
    .linux-arm-download {
        text-align: center;
    }
}

.sky #main-feature .download-button {
    position: static; /* override oldIE.css */
}

/* Unsupported platforms */
html.windows.arm,
html.oldwin,
html.oldmac,
html.android,
html.ios {
    .download-button .unsupported-download {
        display: block;
        margin: 32px 0 0;
    }
}

#thunderbird-screenshot {
    margin: 40px 20px 0;
    text-align: center;

    .platform-img {
        vertical-align: bottom;
    }
}

#colophon {
    z-index: 1;
}

/* Tablet Layout: 760px */
@media only screen and (min-width: @breakTablet) and (max-width: @breakDesktop) {
    #wrapper {
        width: 760px;
        padding: 0;
    }

    #main-feature {
        h1.large {
            margin: 12px 50px;
        }

        p.large {
            .font-size(24px);
        }
    }
}

/* Mobile layout: 320px */
@media only screen and (max-width: @breakTablet) {
    #wrapper {
        width: 320px;
        padding: 0;
    }

    #masthead h2 {
        display: none;
    }

    #main-feature {
        margin: 20px 10px 40px;
        padding: 0;

        img {
            display: block;
            margin: 0 auto;
        }

        h1.large {
            margin: 12px 0;
            .font-size(36px);
            letter-spacing: -1px;
        }

        p.large {
            margin: 12px 0;
            .font-size(20px);
        }
    }

    #thunderbird-screenshot {
        display: none;
    }
}
