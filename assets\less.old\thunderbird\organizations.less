// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

@import "../sandstone/lib.less";

#masthead h1 {
    padding: @baseLine 0;
}

#main-feature {
    h1,
    h4 {
        .span-all();
    }
}

.stacked {
    h1 {
        .font-size(32px);
        letter-spacing: -1px;

        span {
            margin-left: -.1ex;
            display: block;
        }
    }

    p.large {
        .span-all();
        .font-size(24px);
        line-height: 1.3;
    }
}

#main-content {
  margin: 0 10px;
}

#download .button {
    padding: 10px;
}

#esr-form {
    input[type="email"],
    input[type="text"] {
        max-width: 90%;
    }

}

img#release-overview {
    height: auto;
}

@media only screen and (min-width: @breakDesktop) {
    img#release-overview {
        max-width: none;
    }
}
