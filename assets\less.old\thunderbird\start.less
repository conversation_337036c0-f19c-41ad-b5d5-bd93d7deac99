// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

// A minimum stylesheet for the Thunderbird start page based on the Sky theme.

// Several direction-independent margin/padding properties are used to easier
// support RTL locales. Though the browser support is limited, it should be safe
// to use them here because this CSS is only for Thundebird dedicated pages.

@import "../sandstone/lib.less";

html {
    color: @textColorPrimary;
    font-size: @baseFontSize;
    line-height: 1.5;
    .open-sans;
}

body {
    margin: 1.5rem;
    background: url('/media/img/sandstone/bg-gradient-sky.png') repeat-x,
                url('/media/img/sandstone/grain.png') repeat,
                #EEE;
}

a {
    color: @linkBlue;
    text-decoration: none;

    &:hover,
    &:focus,
    &:active {
        color: @linkBlueHover;
        text-decoration: underline;
    }
}

h1,
h2 {
    margin: 0;
    .open-sans-light;
    font-weight: normal;
}

main {
    position: relative;
    overflow: hidden;
}

header,
section,
footer {
    box-sizing: padding-box;
    margin-top: 1.5rem;
    padding: 0 1.5rem;
}

header h1,
section h2.iconic {
    position: relative;

    &:before {
        display: block;
        position: absolute;
        top: 0;
        background-repeat: no-repeat;
        content: '';
    }
}

header h1:before {
    background-image: url('/media/img/thunderbird/logos/release-100.png');

    html[data-channel="earlybird"] & {
        background-image: url('/media/img/thunderbird/logos/earlybird-100.png');
    }

    html[data-channel="daily"] & {
        background-image: url('/media/img/thunderbird/logos/daily-100.png');
    }
}

section h2.iconic:before {
    background-image: url('/media/img/thunderbird/start/sprite.png');
}

footer a {
    display: inline-block;
    -moz-padding-start: 25px; /* Legacy */
    padding-inline-start: 25px; /* Gecko 41+ */

    &:before {
        -moz-margin-start: -25px; /* Legacy */
        margin-inline-start: -25px; /* Gecko 41+ */
    }
}

footer a,
section a.iconic {
    &:before {
        display: inline-block;
        -moz-margin-end: 5px; /* Legacy */
        margin-inline-end: 5px; /* Gecko 41+ */
        width: 20px;
        height: 20px;
        background-image: url('/media/img/thunderbird/start/sprite.png');
        background-repeat: no-repeat;
        vertical-align: text-bottom;
        content: '';
    }
}

header h1 {
    box-sizing: padding-box;
    font-size: 1.25rem;
    line-height: 1;

    &:before {
        height: 100px;
    }

    span {
        display: block;
        color: #2666A6;
        font-size: 3rem;
        letter-spacing: -.06ex;
    }
}

section {
    h2 {
        font-size: 1.25rem;
        line-height: 1.3;

        &.iconic {
            display: table-cell;
            -moz-padding-start: 55px; /* Legacy */
            padding-inline-start: 55px; /* Gecko 41+ */
            height: 50px;
            vertical-align: middle;
        }

        &:before {
            width: 50px;
            height: 50px;
        }
    }

    &.tip h2:before {
        background-position: 0 0;
    }

    &.addons h2:before {
        background-position: -50px 0;
    }

    &.help h2:before {
        background-position: 0 -50px;
    }

    &.blog h2:before {
        background-position: -50px -50px;
    }

    p {
        margin: .7em 0 0;
    }

    a[href$="Contributing"]:before {
        background-position: -40px -100px;
    }

    a.more {
        .trailing-arrow;
    }
}

footer {
    ul {
        overflow: hidden;
        margin: 0;
        padding: 0;
        list-style-type: none;
    }

    li {
        box-sizing: padding-box;
        padding-bottom: .5rem;
    }

    a {
        &[href*="features"]:before {
            background-position: 0 -100px;
        }

        &[href*="blog"]:before {
            background-position: -20px -100px;
        }

        &[href*="donate"]:before {
            background-position: -40px -100px;
        }

        &[href*="Contributing"]:before {
            background-position: -40px -100px;
        }

        &[href*="addons"]:before {
            background-position: -20px -120px;
        }

        &[href*="support"]:before {
            background-position: -40px -120px;
        }

        &[href*="how"]:before {
            background-position: 0 -120px;
        }
    }
}

[dir="ltr"] {
    header h1,
    section h2.iconic {
        &:before {
            left: 0;
        }
    }
}

[dir="rtl"] {
    header h1,
    section h2.iconic {
        &:before {
            right: 0;
        }
    }
}

@media (min-width: 1024px) {
    footer li {
        width: 50%;
        min-width: 10em;
    }

    [lang|="en"] footer li {
        width: 33%;
    }
}

@media (min-width: 800px) and (max-width: 1023px) {
    footer li {
        width: 50%;
    }
}

@media (min-width: 800px) {
    main {
        > header,
        > div,
        > footer {
            width: 50%;
        }
    }

    [dir="ltr"] main {
        > header,
        > div:last-of-type,
        > footer,
        > footer li {
            float: left;
        }

        > div:first-of-type {
            float: right;
        }
    }

    [dir="rtl"] main {
        > header,
        > div:last-of-type,
        > footer,
        > footer li {
            float: right;
        }

        > div:first-of-type {
            float: left;
        }
    }
}

@media (min-width: 640px) and (max-width: 799px) {
    header,
    section {
        width: 70%;
    }

    footer {
        position: absolute;
        top: 120px;
        width: 30%;
    }

    [dir="ltr"] footer {
        right: 0;
    }

    [dir="rtl"] footer {
        left: 0;
    }
}

@media (min-width: 480px) and (max-width: 639px) {
    footer li {
        width: 50%;
        min-width: 10em;
    }

    [lang|="en"] footer li {
        width: 33%;
    }

    [dir="ltr"] footer li {
        float: left;
    }

    [dir="rtl"] footer li {
        float: right;
    }
}

@media (min-width: 480px) {
    header h1 {
        display: table-cell;
        -moz-padding-start: 110px; /* Legacy */
        padding-inline-start: 110px; /* Gecko 41+ */
        height: 100px;
        vertical-align: middle;

        &:before {
            width: 100px;
        }
    }
}

@media (max-width: 479px) {
    header h1 {
        padding-top: 110px;
        text-align: center;

        &:before {
            width: 100%;
            background-position: center top;
        }
    }
}

@media (-webkit-min-device-pixel-ratio: 1.5), (min-resolution: 1.5dppx), (min-resolution: 144dpi) {
    header h1:before {
        background-image: url('/media/img/thunderbird/logos/release-200.png');
        background-size: 100px 100px;

        html[data-channel="earlybird"] & {
            background-image: url('/media/img/thunderbird/logos/earlybird-200.png');
        }

        html[data-channel="daily"] & {
            background-image: url('/media/img/thunderbird/logos/daily-200.png');
        }
    }
}
