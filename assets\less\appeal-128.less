// Base
@import "new/fonts.less";
@import "new/fonts-extra.less";
@import "new/colours.less";
@import "new/links.less";
@import "new/components/donate-cta.less";

/*-------------------------
* Media Queries
*--------------------------*/
@xxs: 25rem; // 400px
@xs: 30rem; // 480px
@sm: 40rem; // 640px
@md: 48rem; // 768px
@lg: 64rem; // 1024px
@xl: 80rem; // 1280px
@xxl: 90rem; // 1440px

:root {
  --appeal-bg-gradient: linear-gradient(169.85deg, #121621 2.48%, #16294D 32.48%);
  --appeal-overlay-bg-gradient: linear-gradient(0deg, rgba(23, 41, 78, 0.50) 0%, rgba(23, 41, 78, 0.50) 100%), radial-gradient(128% 50% at 0% 100%, #25A6A0 0%, rgba(163, 236, 227, 0.00) 100%), radial-gradient(162.43% 111.8% at 0% 0%, #AE55F7 0%, rgba(174, 85, 247, 0.00) 100%), radial-gradient(106.95% 103.6% at 100.87% 101.99%, #EA7308 0%, rgba(234, 115, 8, 0.00) 100%);

  --bg-appeal-glow: linear-gradient(129.83deg, #37ADF9 0%, #AE55F7 100%);;

  --color-appeal-bg: #82C3F5;
  --color-appeal-main-bg: var(--color-white);
  --color-footer-txt: #105BBC;
  --color-legal-txt: var(--color-white);

  --color-appeal-title: #FEFFFF;
  --color-appeal-sub: #F2F7FC;
  --color-appeal-txt: #18181B;
  --color-appeal-bg-border: #1373D9;

  --bg: var(--appeal-bg-gradient);
  --overlay-bg: var(--appeal-overlay-bg-gradient);
  --txt: var(--color-appeal-txt);
  --shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  --accent-text: var(--color-appeal-txt);
  --closing-text: var(--color-footer-txt);

  --font-heading: 'Metropolis', sans-serif;
  --font-content: Open Sans, system-ui, sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --appeal-overlay-bg-gradient: linear-gradient(0deg,
    rgba(23, 41, 78, 0.50) 0%,
    rgba(23, 41, 78, 0.50) 100%),
    radial-gradient(128% 50% at 0% 100%,
    #A3ECE3 0%,
    rgba(163, 236, 227, 0.00) 100%),
    radial-gradient(162.43% 111.8% at 0% 0%,
    #8022CE 0%,
    rgba(174, 85, 247, 0.00) 100%),
    radial-gradient(106.95% 103.6% at 100.87% 101.99%,
    #FEC18A 0%,
    rgba(234, 115, 8, 0.00) 100%);

    --bg-appeal-glow: linear-gradient(129.83deg, #58C9FF 0%, #A3ECE3 100%);

    --color-appeal-title: #FEFFFF;
    --color-appeal-sub: #F2F7FC;
    --color-appeal-txt: #F7F7F7;
    --color-appeal-bg-border: #58C9FF;
    --color-appeal-main-bg: #1A202C;

    --color-footer-txt: #89BCFF;
    --color-legal-txt: var(--color-white);

    --shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}

.nebula-bg {
  --nebula-bg: url('/media/img/thunderbird/appeal/appeal-128/header.png');
  --nebula-size: 1091px 1008px;
  --nebula-position: center top -192px;
  position: absolute;
  top: 0;
  display: flex;
  width: 100%;
  height: 1300px;
  justify-content: center;
  z-index: -1;

  background: var(--nebula-bg);
  background-repeat: no-repeat;
  background-position: var(--nebula-position);
  background-size: var(--nebula-size);
}

html {
  font-family: var(--font-content);
  -moz-osx-font-smoothing: grayscale;
  font-size: 1rem;
}

body {
  position: relative;
  background: var(--bg);
  color: var(--txt);
  padding: 10rem 0 0;
  margin: 0;
  min-height: 100vh;
  max-width: 100%;
  line-height: 1.6;
  font-size: 0.8125rem;
}

.overlay {
  position: absolute;
  pointer-events: none;
  overflow: hidden;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0.2;
  background: var(--overlay-bg);

  // Figma creates *special* gradients
  transform: rotateZ(180deg) scaleX(-1);
}

/* Minor fixes with new design START */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  position: relative;
}

.self-end {
  align-self: flex-end;
}

.text-right {
  text-align: right;
}

/* Minor fixes with new design END */

section {
  border-color: var(--color-appeal-bg-border);
  background-color: var(--color-appeal-main-bg);
}

#appeal-header {
  position: relative;
  left: -50px;

  display: flex;
  gap: 9px;
  margin: 0 auto 52px;
  padding: 0 24px 24px;

  max-width: 900px;
  align-items: center;
  font-size: 1.25rem;

  h1 {
    font-size: 5rem;
    line-height: 1;
  }

  p {
    color: var(--color-appeal-sub);
    font-weight: 700;
    line-height: 1.5;
    letter-spacing: 0.075rem;
    margin-top: 10px;
    margin-bottom: 30px;
  }
}

#appeal-body {
  font-size: 1.3125rem;
  font-weight: 400;
  line-height: 1.5;

}

main {
  padding: 1em;
  margin: 0 auto;
  max-width: 787px;
  position: relative;
}

.accent-text {
  color: var(--accent-text);
}

#appeal-heading {
  width: 100%;
  text-align: left;
  display: flex;
  flex-direction: column;
  font-size: 2.625rem !important;
  font-weight: 200;
  margin: 0;
  color: var(--color-appeal-title);

  span {
    width: 100%;
    font-size: 5.625rem;
    font-weight: 400;
    line-height: 1;
    margin: 0.625rem 0 0;
  }
}

#illustration {
  width: 60%;
  position: relative;
}

#illustration > #roc {
  position: absolute;
  top: -269px;
  left: -69px;
}

.letter-container {
  padding: 60px 60px 10px;
  background: var(--color-appeal-main-bg);
  border: 1px solid var(--color-appeal-bg-border);
  border-radius: 0.75rem;
  box-shadow: var(--shadow);
  position: relative;
  margin-bottom: 121px;


  p {
    margin: 0 0 1.96875rem; // 1.5 font-size
  }

  p:nth-child(2) {
    margin-top: 3rem;
  }
}

.letter-container:before {
  content: '';
  position: absolute;
  background: var(--bg-appeal-glow);
  inset: -4px;
  margin: -12px 18px;
  border-radius: 0.75rem;
  filter: blur(32px);
  opacity: 0.8;
  z-index: -1;
}

.closing-text {
  font-size: 1rem;
  text-align: center;
  color: var(--color-footer-txt);
  margin: 1rem 0 !important;
}


.heart-container {
  color: var(--color-red-50);
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 32px;
  align-items: start;
  margin-top: 2em;
}

.heart-svg {
  position: relative;
  top: -4px;
}

@media (prefers-color-scheme: dark) {
  .heart-container {
    color: var(--color-red-30);
  }
}

.line {
  display: block;
  height: 1px;
  width: 100%;
  border-top: 1px solid var(--color-blue-60);
}

.left-lines,
.right-lines {
  display: flex;
  flex-direction: column;
  gap: 6px;
  justify-self: stretch;
}

.left-lines {
  align-items: end;
}

.left-lines > .line:nth-child(2) {
  width: 50%;
  margin-right: 3px;
  border-top: 1px solid var(--color-blue-50);
}

.left-lines > .line:nth-child(1) {
  width: 30%;
  border-top: 1px solid var(--color-blue-40);
}

.right-lines > .line:nth-child(2) {
  width: 50%;
  margin-left: 3px;
  border-top: 1px solid var(--color-blue-50);
}

.right-lines > .line:nth-child(1) {
  width: 30%;
  border-top: 1px solid var(--color-blue-40);
}

#footer {
  font-size: 18px;
  line-height: 1.5;
  font-weight: 400;
  width: 100%;
}

/* Here until the new design is merged */
#footer.container {
  position: relative;
  display: grid;
  gap: 60px;
  padding-inline: 1rem;
  margin-inline: auto;
  overflow: hidden;
  box-sizing: border-box;
}

#footer.container.footer {
  place-items: center;
  padding-inline: 1rem;
  padding-block: 1.25rem;
}

#footer a {
  color: var(--color-legal-txt);
  text-decoration-color: var(--color-legal-txt);
  text-underline-offset: 0.25em;
  text-decoration-thickness: .12em;
  text-decoration-style: solid;
  transition: font-size .2s,
  text-decoration-color .2s;
}

#footer a.donate,
#footer a:hover,
#footer a:hover:visited {
  text-decoration-color: var(--accent);
}

#footer a:visited {
  text-decoration-color: var(--color-purple-50);
}

#footer .mzla {
  display: grid;
  place-items: center;
  gap: 15px;
  text-align: center;
  max-inline-size: 75ch;
}

#footer .mozilla-logo {
  --accent: white;
  max-width: 200px;
}

#footer .site-links,
#footer .legal-links {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
  justify-content: center;
}

#footer .legal-links {
  font-size: 1rem;
}

/*
 * Media Queries
 */

@media (max-width: @lg) {
  #appeal-header {
    padding-top: 4rem;
    position: static;
    flex-direction: column-reverse;
    justify-items: center;
    margin-bottom: 6rem;
  }

  #illustration {
    display: flex;
    flex-direction: column-reverse;
    text-align: center;

    position: relative;
    width: 100%;
    height: 120px;
  }

  #illustration > div {
    position: relative;
    width: 100%;
  }

  #illustration > #roc > svg {
    max-width: 300px;
  }

  #illustration > #roc {
    left: 0;
    top: -240px;
  }
}


@media (max-width: @sm) {

  main {
    padding: 0;
  }

  #appeal-heading {
    font-size: 100%;
  }

  #appeal-heading span {
    font-size: 100%;
  }

  #appeal-header p {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .letter-container {
    padding: 1rem;
  }

  .letter-container:before {
    inset: 0;
  }
}

@media (max-width: @xs) {
  #footer.container {
    padding-inline: 0.25rem;
    padding-block: 32px;
  }

  #appeal-header {
    padding: 4rem 1rem 1rem;
    margin-bottom: 6rem;
  }

  #appeal-heading {
    font-size: 3rem !important;
  }
}
