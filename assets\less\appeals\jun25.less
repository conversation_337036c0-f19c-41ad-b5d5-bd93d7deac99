@import '../updates-style.less';
@import '../new/fonts-extra.less';

/*-------------------------
* Media Queries
*--------------------------*/
/* fake, in-between media queries */
@xms: 26.25rem; // 420px;
@ss: 33rem; // 528px ("sorta small")
@mxl: 73.75rem; // 1180px

:root {
  --bg-img: url('/media/img/thunderbird/appeal/jun25/header-high-res.png');
  --bg-img-set: image-set(url('/media/img/thunderbird/appeal/jun25/header-high-res.avif') type('image/avif'),
  url('/media/img/thunderbird/appeal/jun25/header-high-res.webp') type('image/webp'),
  var(--bg-img) type('image/png'));

  --bg-img-sm: url('/media/img/thunderbird/appeal/jun25/header-sm-high-res.png');
  --bg-img-sm-set: image-set(url('/media/img/thunderbird/appeal/jun25/header-sm-high-res.avif') type('image/avif'),
  url('/media/img/thunderbird/appeal/jun25/header-sm-high-res.webp') type('image/webp'),
  var(--bg-img-sm) type('image/png'));

  --bg-appeal-glow: linear-gradient(130deg, rgba(13, 44, 91, 0.00) 0%, #26BAF5 77.4%);

  --color-appeal-main-bg: var(--color-white);
  --color-footer-txt: #105BBC;
  --color-appeal-txt: #18181B;
  --color-appeal-bg-border: #1373D9;

  --txt: var(--color-appeal-txt);
  --shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

  --font-content: Open Sans, system-ui, sans-serif;
  --alt-font-content: 'Metropolis', sans-serif;
  --min-small-screen: 23.438rem;

}

// Scope to just the appeal page
.page-appeal-jun25 {
  background-color: black;

  body {
    margin: auto;
    min-width: 23.438rem;
    font-family: var(--font-content);
    line-height: 1.6;
    background-color: #0f345e;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  #header-gradient {
    --bg-color: linear-gradient(to bottom, rgba(20, 64, 92, 0) 35%, #0f345e 75%);
    background: var(--bg-color);
    position: absolute;
    width: 100%;
    height: 40rem;
    overflow: clip;
    pointer-events: none;
  }

  #appeal-header {
    display: flex;
    flex-direction: column-reverse;
    padding-bottom: 7rem;
    max-width: 80rem;
    width: 100%;

    background-image: var(--bg-img-sm);
    background-image: var(--bg-img-sm-set);
    background-position: top center;
    background-position-y: -1rem;
    background-size: contain;
    background-repeat: no-repeat;


    header {
      padding: 0;
      background: none;
    }

    @media (min-width: @lg) {
      background-image: var(--bg-img);
      background-image: var(--bg-img-set);
    }

  }

  #appeal-heading {
    margin: 0.5rem 0;
    text-align: left;
    display: flex;
    flex-direction: column;
    width: 100%;
    box-sizing: border-box;

    color: #DCEEFB;
    font-family: var(--alt-font-content);
    font-size: ~"clamp(1.5rem, 0.7231rem + 3.3149vw, 3.375rem)";

    // Scale the padding-top up until we hit @lg
    padding: ~"clamp(0rem, -5.4892rem + 23.4206vw, 9.5rem)" 1rem 1rem;

    font-style: normal;
    font-weight: 600;
    line-height: 110%;
    text-shadow: 0 0.125rem 0.1875rem rgba(0, 0, 0, 0.3);

    @media (min-width: @lg) {
      padding: 2.2rem 2rem 1.8rem 2rem;
    }

    & > span {
      background: linear-gradient(180deg, #FFF6BF 30%, #CB8761 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: ~"clamp(2.25rem, 1.3177rem + 3.9779vw, 4.5rem)";
      font-weight: 700;
      line-height: 110%;
      text-shadow: none;
    }
  }

  #illustration {
    width: 100%;
    text-align: right;
    display: flex;
    justify-content: flex-start;

    #roc {
      margin-right: 5rem;
      margin-top: 1rem;
      flex-shrink: 1;
    }

    @media (min-width: @lg) {
      justify-content: flex-end;
    }
  }

  #donate-button-container {
    max-width: 22.188rem;
    margin: 0 auto;

    @media (min-width: @md) {
      max-width: 29.375rem;
    }
  }

  #appeal-body {
    box-sizing: border-box;
    max-width: 80rem;

    @media (min-width: @lg) {
      margin-top: -5.7rem;
    }

    &:before,
    &:after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      background: var(--bg-appeal-glow);
      margin: 0 -10.375rem;
      /* border-radius: 0.75rem; */
      filter: blur(2.75rem);
      opacity: 0.8;
      inset: 11.75rem;
      z-index: 0;
      /* z-index: -1; */
    }

    &:before {
      top: -1rem;
    }

    &:after {
      bottom: -1rem;
    }
  }

  .letter-container {
    z-index: 1;
    width: 100%;
    display: flex;
    flex-direction: column;

    box-sizing: border-box;
    align-items: center;
    text-align: left;

    font-size: 1.3125rem;
    font-weight: 400;
    line-height: 1.5;

    margin-top: -6rem;
    @media (min-width: @md) {
      font-size: 1.375rem;
      line-height: 2rem;
      padding-top: 7rem;
    }
    background: var(--color-appeal-main-bg);
    margin-bottom: 6rem;
    position: relative;

    padding: 6rem 1.063rem .75rem;

    @media (min-width: @lg) {
      border: 0.063rem solid var(--color-appeal-bg-border);
      border-radius: 0.75rem;
      box-shadow: var(--shadow);
      max-width: 60rem;
      margin: 0 auto 6rem;
      padding-left: 4.5rem;
      padding-right: 4.5rem;
    }

    p {
      color: var(--text-icon-base, #1A202C);
      width: 100%;
      line-height: 150%;
      font-weight: 400;
      font-size: 1.375rem;
      margin: 0 0 1.5rem;
    }

    /* Declaring this style here, as it has more precedence. */

    .closing-text {
      font-size: 1rem;
    }

  }

  .letter-container .closing-text {
    font-family: var(--font-content);
    font-weight: 400;
    line-height: 150%;
    text-align: center;
    margin: 1rem 0 !important;
    color: #1373d9;
  }


  .heart-container {
    color: var(--color-red-30);
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2.0rem;
    align-items: center;
    margin-top: 1.75rem;
    width: 100%;
    max-width: 41.125rem;
    justify-content: center;
  }

  .heart-svg {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }

  .line {
    display: block;
    height: 0.0625rem;
    width: 100%;
    border-top: 0.0625rem solid var(--color-blue-40);
  }

  .left-lines,
  .right-lines {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    justify-self: stretch;
    // If smol, hide!
    overflow: hidden;
  }

  .left-lines {
    align-items: end;
  }

  .left-lines > .line:nth-child(2) {
    width: 50%;
    margin-right: 0.1875rem;
    border-top: 0.0625rem solid var(--color-blue-40);
  }

  .left-lines > .line:nth-child(1) {
    width: 30%;
    border-top: 0.0625rem solid var(--color-blue-40);
  }

  .right-lines > .line:nth-child(2) {
    width: 50%;
    margin-left: 0.1875rem;
    border-top: 0.0625rem solid var(--color-blue-40);
  }

  .right-lines > .line:nth-child(1) {
    width: 30%;
    border-top: 0.0625rem solid var(--color-blue-40);
  }

  #decoration {
    left: -3.4375rem;
    gap: 0.1875rem;
    height: 5.625rem;
    width: 31.25rem;
  }

  // Donation button overrides
  .donate-banner {
    border-radius: 0.75rem;
    //--background-left: linear-gradient(360deg, #D22424 0.41%, #7F1616 100.41%);
    --background-left: linear-gradient(135deg, #D22424 50.41%, #7F1616 70.41%);
    --background-right: linear-gradient(180deg, #FFFFFF 0.41%, #FFE8E6 100.41%);
    box-shadow: 0 0 0.625rem 0 #0000001A;
    border: 0.0625rem solid var(--critical-pressed, #7F1D1D);
    color: #7F1616;
    font-family: var(--alt-font-content);
    margin: -7.125rem auto 2rem;
    max-width: 28.875rem;
    min-height: 7.6875rem;
    font-size: 1.5rem;

    &::after {
      top: -13.125rem;
      left: -10rem;
      height: 26.25rem;
      width: 26.25rem;
      transform: rotate(45deg);

      @media (min-width: @md) {
        left: -6rem;
      }
    }

    &:hover::after {
      transform: scale(3);
    }

    #decoration {
      left: 3.2rem;
      transform: rotate(-45deg);
      @media (min-width: @md) {
        left: 7.2rem;
      }
    }

    #donate-banner-left {
      padding: 1.0rem 1.5rem;
      text-align: left;

      color: var(--surface-base, #FEFFFF);
      text-shadow: 0 0.101125rem 0.2021875rem rgba(0, 0, 0, 0.25);
      font-size: 1.5rem;
      font-style: normal;
      font-weight: 600;
      line-height: 130.023%; /* 31.205px */
      letter-spacing: 0.0075rem;

      b {
        font-size: 2.625rem;
        font-weight: 600;
        line-height: 130.023%; /* 54.61px */
        letter-spacing: 0.013125rem;
      }
    }

    #donate-banner-right {
      display: none !important;
    }

    @media (max-width: @md) {
      height: 6.25rem;

      #hover-hearts {
        color: #FFE8E6;
        opacity: 0.5;
      }
    }
  }

  #donate-banner-left, #hover-hearts {
    color: white;
  }

  #donate-banner-right {
    justify-content: flex-end;
    text-align: right;
  }

}
