@import '../updates-style.less';

// Scope to just the appeal page
.page-appeal-nov24 {
  --bg: #FEFFFF;

  // Override our breakpoint here so that it breaks just before hitting the screenshot logo
  @logo-md: 54.375rem; // 870px

  // Prefer webp over the jpeg
  --bg-img: url('/media/img/thunderbird/appeal/nov24/background-image-high-res.jpeg');
  --bg-img-set: image-set(var(--bg-img) type('image/jpeg'),
  url('/media/img/thunderbird/appeal/nov24/background-image-high-res.webp') type('image/webp'));

  body {
    background-image: var(--bg-img);
    background-image: var(--bg-img-set);
    background-position: center top;
    background-repeat: no-repeat;
    background-size: 80.0rem; // 1280px
    background-color: #1E467F;
  }

  // Prevent overflow from feature elements
  #main-content {
    max-width: 100%;
    overflow: hidden;
  }

  #donate-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;

    font-size: 1rem;
    font-weight: 500;

    padding: 0.5em ~"clamp(1.25rem, -3.75rem + 40vw, 3rem)";

    .heart-svg {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      top: .01rem;
    }

    // Fix the button text colour
    &:hover {
      color: #af1e1e;
    }
  }

  .section-text {
    max-inline-size: 90%;
  }

  h1, h1 span {
    --txt: #FEFFFF;
    color: var(--txt);
    line-height: 1;
    margin: 0;
    text-shadow: 0 0.25rem 0.5rem #1A202C;
  }

  h1 {
    font-size: 5.75rem;
    font-weight: 600;

    @media (max-width: @md) {
      font-size: 3rem;
    }
  }

  h1 > span {
    display: block;
    font-size: 2.0rem;
    font-weight: 500;

    @media (max-width: @md) {
      font-size: 1rem;
    }
  }

  .container {
    // Fix for cherry-pick
    display: flex;
    flex-direction: column;
    gap: 3rem;


    padding-top: 0;
    padding-bottom: 0;
    overflow: visible;

    @media (max-width: @md) {
      padding: 0;
    }
  }

  #twenty-years {
    padding-top: 3.75rem;
  }

  #header-graphic {
    box-sizing: border-box;
    position: relative;
    display: grid;
    filter: drop-shadow(0 0.25rem 0.75rem rgba(0, 0, 0, 0.3));
    place-items: center;
    isolation: isolate;
    margin-top: 2rem;
    margin-bottom: -2rem;


    & img {
      box-sizing: border-box;
      width: 12.5rem;
      max-width: 100%;
      z-index: 2;
    }

    span {
      box-sizing: border-box;

      color: #19518F;
      font-size: 2.5rem;
      font-family: Metropolis, sans-serif;
      font-weight: 600;
      line-height: 1;

      // Re-position it instead of using margin so height works with word-wrap.
      top: -3.25rem;
      position: relative;
      word-wrap: anywhere;
      // Image-size
      min-width: 12.40625rem; //198.5px;

      background: white;
      padding: 0.5rem;
      border-radius: 0.5625rem;
      text-transform: uppercase;
      z-index: 2;
    }

    &::before {
      box-sizing: border-box;

      content: '';
      position: absolute;
      inset-inline: 0;
      margin-inline: auto;
      bottom: 4.75rem;
      height: 0.5rem;
      max-width: 50.0rem;
      display: block;
      background: linear-gradient(to right, transparent, white, transparent);
      z-index: 1;
    }
  }

  .footer-graphic {
    z-index: 2;
    margin-top: 2rem;
    right: 0.6rem;
    position: relative;
    width: 17.75rem;

    img {
      width: 100%;
    }

    @media (max-width: @logo-md) {
      margin-top: 8rem;
    }
    @media (max-width: @md) {
      margin-top: 7.5rem;
    }
  }

  .copy-section {
    box-sizing: border-box;
    text-align: left;
    box-shadow: 0 0.25rem 1.5rem 0 #1A202C;

    font-family: 'Inter', sans-serif;
    font-size: 1.0rem;
    line-height: 1.21rem;
    max-width: 41.125rem !important;
    width: 100%;
    background-color: var(--bg);
    border-radius: 0.625rem;
    margin-top: 4rem;
    margin-bottom: 0;
    padding: 2.0rem 2.5rem;

    b {
      font-weight: 700;
    }

    // Override the section-text mobile settings
    @media (max-width: @md) {
      max-width: 18rem;
      padding: 1.5rem 1.5rem !important;
      margin-left: auto;
      margin-right: auto;
    }
  }

  .section-text {
    @media (max-width: @md) {
      padding: 0.5rem;
      max-inline-size: 100%;
    }
  }

  #timeline {
    .container {
      align-items: center;
      gap: 0;
    }
  }

  .timeline-connector {
    position: relative;
    width: 4.0rem;
    height: 1.0rem;
    top: -0.25rem;

    svg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
    }
  }

  .timeline-line {
    position: absolute;
    content: ' ';
    width: 0.375rem;
    height: 76.875rem;
    background-color: var(--bg);

    @media (max-width: @logo-md) {
      height: calc(76.875rem * 2);
    }
  }

  .features {
    --feature-margin: 5rem;

    display: flex;
    flex-direction: column;
    // Gotta account for the negative margins
    margin-top: calc(3.125rem + var(--feature-margin));

    .feature {
      position: relative;
      display: flex;
      flex-direction: row;
      gap: 3.125rem;
      width: 42rem;
      left: 11.375rem;

      // We want negative margins here
      margin-top: calc(-1 * var(--feature-margin));

      // Lovingly hand placed
      .stickers {
        position: static;

        img {
          width: 100%;
        }

        div {
          position: absolute;
        }

        .sticker-im {
          width: 3.75rem;
          height: 3.75rem;

          left: 11.75rem;
          top: -2rem;
        }

        .sticker-dial-up {
          width: 4.4375rem;
          height: 4.5625rem;
          left: 3rem;
        }

        .sticker-chat {
          width: 5.25rem;
          height: 4.9375rem;

          left: 25rem;
          bottom: -3.5rem;
        }

        .sticker-envelope {
          width: 3.375rem;
          height: 2.5625rem;

          bottom: 6.25rem;
          left: 1rem;
        }

        .sticker-envelope-2 {
          width: 3.3125rem;
          height: 3.75rem;

          right: 1.5rem;
          bottom: 5.35rem;
        }

        .sticker-envelope-3 {
          width: 5.3125rem;
          height: 3.875rem;

          right: .5rem;
          bottom: 4.75rem;
        }

        .sticker-early-smart-phone {
          width: 4.9375rem;
          height: 7.4375rem;

          left: -11rem;
          top: 7rem;
        }

        .sticker-flip-phone {
          width: 6.625rem;
          height: 7.8125rem;

          left: -11rem;
          bottom: 9rem;
        }

        .sticker-mail {
          width: 4.0rem;
          height: 3.0rem;

          left: .75rem;
          bottom: 5.75rem;
        }

        .sticker-media-player {
          width: 4.8125rem;
          height: 6.125rem;

          right: -9rem;
          bottom: 7rem;
        }

        .sticker-modern-smart-phone {
          width: 4.9375rem;
          height: 7.4375rem;

          right: -10rem;
          top: 13rem;
        }

        .sticker-video-chat {
          width: 4.625rem;
          height: 4.0625rem;

          left: 12rem;
          bottom: -1rem;
        }
      }

      // Flip some attributes
      &.reverse {
        flex-direction: row-reverse;
        right: 11.375rem;
        left: auto;

        .timeline-blip .blip-and-line .line {
          right: 0.25rem;
          left: auto;
        }

        .screenshot-logo img {
          bottom: -3rem;
          left: -3rem;
          right: auto;
        }
      }

      .timeline-blip {
        display: flex;
        flex-direction: inherit;
        align-items: center;
        gap: 0.75rem;
        width: 10.5rem;

        h3 {
          --txt: #FEFFFF;
          color: var(--txt);
          font-family: 'Metropolis', sans-serif;
          font-weight: 900;
          font-size: 2.0rem;
          line-height: 2.0rem;
          margin: 0;
          display: block;
          text-shadow: 0 0.25rem 0.25rem #00000040;
          min-width: 5.75rem;
        }

        .blip-and-line {
          position: relative;
          display: flex;
          flex-direction: inherit;
          justify-content: center;
          align-items: center;
          z-index: 1;

          --shadow: 0 0.25rem 0.625rem 0 #00000040;

          .blip {
            position: relative;
            display: block;
            background-color: #1373D9;
            border: 0.375rem solid var(--bg);
            border-radius: 100%;
            width: 1rem;
            height: 1rem;
            box-shadow: var(--shadow);
          }

          .line {
            position: relative;
            display: block;
            height: 0.25rem;
            width: 2.25rem;
            background-color: var(--bg);
            border-radius: 0.625rem;
            z-index: -1;
            left: 0.25rem;
            box-shadow: var(--shadow);
          }
        }
      }

      .screenshot {
        position: relative;

        img {
          box-sizing: border-box;
          width: 20.0rem;
          height: 20.0rem;
          border: 0.25rem solid var(--primary-container, #1373D9);
          border-radius: 0.5625rem;
          box-shadow: 0 0.125rem 1.0rem 0 #00000040;
        }

        .screenshot-logo img {
          position: absolute;

          width: 6.25rem !important;
          height: 6.25rem !important;
          border: 0;
          bottom: -3rem;
          right: -3rem;
          box-shadow: none;
        }
      }
    }

    @media (max-width: @logo-md) {
      margin-top: -2rem;
      gap: 0;

      .feature {
        --feature-margin: 5rem;
        flex-direction: column-reverse;
        width: 18rem;
        gap: 10rem;
        left: 0;

        &.year-2004 .timeline-blip {

        }

        &.year-2009 .timeline-blip {
          top: -2rem;
        }

        &.year-2018 .timeline-blip {
          top: -4rem;
        }

        &.year-2024 .timeline-blip {
          top: -5rem;
        }

        .timeline-blip {
          flex-direction: row;
          left: auto;
          right: 0.625rem;
          position: relative;
        }

        .screenshot {
          img {
            width: 18rem;
            height: 18rem;
          }

          .screenshot-logo img {
            bottom: -3rem;
            right: -1rem;
          }
        }


        &.reverse {
          flex-direction: column-reverse;
          right: 0;

          .timeline-blip {
            flex-direction: row-reverse;
            left: 8.1rem;
            position: relative;
          }

          .screenshot {
            .screenshot-logo img {
              bottom: -3rem;
              left: -1rem;
            }
          }
        }

        // Also hand-placed, still lovingly too!
        .stickers {
          .sticker-im {
            left: -1.25rem;
            top: 8rem;
          }

          .sticker-dial-up {
            bottom: 5rem;
            left: 1.5rem;
          }

          .sticker-chat {
            left: auto;
            right: -1rem;
            bottom: 9rem;
          }

          .sticker-envelope {
            bottom: -2.75rem;
            left: .5rem;
          }

          .sticker-envelope-2 {
            bottom: -1.75rem;
            right: 1rem;
          }

          .sticker-envelope-3 {
            right: -0.25rem;
            bottom: 1rem;
          }

          .sticker-early-smart-phone {
            left: auto;
            top: auto;
            right: 1.5rem;
            bottom: -2rem;
          }

          .sticker-flip-phone {
            left: auto;
            right: 0.5rem;
            bottom: 0;
          }

          .sticker-mail {
            left: 0.15rem;
            bottom: 0.75rem;
          }

          .sticker-media-player {
            left: 1rem;
            right: auto;
            bottom: -2rem;
          }

          .sticker-modern-smart-phone {
            right: auto;
            left: 1rem;
            bottom: -3rem;
            top: auto;
          }

          .sticker-video-chat {
            left: -1rem;
            bottom: 10rem;
          }
        }

      }
    }
  }

  // Copy and paste (with minor tweaks) from previous appeal
  // TODO: Centralize this!
  .closing-text {
    font-family: Metropolis, sans-serif;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    color: #F0F8FF;
    margin: 1rem 0 !important;
  }


  .heart-container {
    color: var(--color-red-30);
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2.0rem;
    align-items: center;
    margin-top: 2em;
    width: 100%;
    max-width: 41.125rem;
    justify-content: center;
  }

  .heart-svg {
    position: relative;
    top: -0.25rem;
  }

  .line {
    display: block;
    height: 0.0625rem;
    width: 100%;
    border-top: 0.0625rem solid var(--color-blue-40);
  }

  .left-lines,
  .right-lines {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    justify-self: stretch;
    // If smol, hide!
    overflow: hidden;
  }

  .left-lines {
    align-items: end;
  }

  .left-lines > .line:nth-child(2) {
    width: 50%;
    margin-right: 0.1875rem;
    border-top: 0.0625rem solid var(--color-blue-40);
  }

  .left-lines > .line:nth-child(1) {
    width: 30%;
    border-top: 0.0625rem solid var(--color-blue-40);
  }

  .right-lines > .line:nth-child(2) {
    width: 50%;
    margin-left: 0.1875rem;
    border-top: 0.0625rem solid var(--color-blue-40);
  }

  .right-lines > .line:nth-child(1) {
    width: 30%;
    border-top: 0.0625rem solid var(--color-blue-40);
  }

  #decoration {
    left: -3.4375rem;
    gap: 0.1875rem;
    height: 5.625rem;
    width: 31.25rem;
  }

  // Donation button overrides
  .donate-banner {
    border-radius: 0.75rem;
    --background-left: linear-gradient(180deg, #DC2626 20.11%, #761414 100%);
    --background-right: linear-gradient(180deg, #FFFFFF 0.41%, #FFE8E6 100.41%);
    box-shadow: 0 0 0.625rem 0 #0000001A;
    border: 0.0625rem solid var(--critical-pressed, #7F1D1D);
    color: #7F1D1D;
    font-family: 'Metropolis', sans-serif;
    margin: -7.125rem auto 2rem;
    max-width: 28.875rem;
    min-height: 7.6875rem;

    &::after {
      top: -13.125rem;
      left: -13.75rem;
      height: 26.25rem;
      width: 26.25rem;
    }

    #donate-banner-left {
      padding: 0.6875rem 0.75rem;
      text-shadow: 0 2px 0 #af1e1e,
        0 -2px 0 #af1e1e,
      2px 0 0 #af1e1e,
      -2px 0 0 #af1e1e,
      2px 2px 0 #af1e1e,
        -2px -2px 0 #af1e1e,
        2px -2px 0 #af1e1e,
      -2px 2px 0 #af1e1e;
    }

    #donate-banner-right {
      padding: 1.0625rem 0.75rem;
      padding-inline-start: 0.1875rem;
      text-shadow: 0 2px 0 #ffecea,
        0 -2px 0 #ffecea,
      2px 0 0 #ffecea,
      -2px 0 0 #ffecea,
      2px 2px 0 #ffecea,
        -2px -2px 0 #ffecea,
        2px -2px 0 #ffecea,
      -2px 2px 0 #ffecea;
    }


    @media (max-width: @md) {
      height: 6.25rem;

      #hover-hearts {
        color: #FFE8E6;
        opacity: 0.5;
      }

      #donate-banner-right {
        position: absolute;
        right: 0;
        bottom: -.5rem;
        font-size: ~"clamp(0.9rem, 0.6429rem + 1.7857vw, 1.25rem)";
        // Up the weight by 100
        font-weight: 400;

        b {
          font-weight: 500;
          font-size: ~"clamp(1.25rem, 1.0714rem + 0.8929vw, 1.5rem)";
        }
      }
    }

    @media (max-width: @xxs) {
      // Deepen the font-colour to help with contrast issues
      #donate-banner-right {
        display: none;
      }
    }
  }

  #donate-banner-left, #hover-hearts {
    color: white;
  }

  #donate-banner-right {
    justify-content: flex-end;
    text-align: right;
  }


}