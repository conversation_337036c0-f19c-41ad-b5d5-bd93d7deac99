// 2024 Redesign Style

// Bases
@import "./new-style.less";
@import "./new/animations.less";
@import "./new/shadows.less";
@import "./new/sizing.less";
@import "./new/layout.less";
@import "./new/variables.less";

// Components
@import "./new/components/accordion.less";
@import "./new/components/buttons.less";
@import "./new/components/forms.less";
@import "./new/components/site-nav.less";
@import "./new/components/testimonial.less";
@import "./new/components/pre-footer.less";
@import "./new/components/announcement.less";
@import "./new/components/footer.less";
@import "./new/components/blocks.less";
@import "./new/components/notice-panel.less";
@import "./new/components/dialog.less";

// Page imports
@import "./pages/donations.less";
@import "./pages/home.less";
@import "./pages/who-we-are.less";
@import "./pages/contact-us.less";
@import "./pages/participate.less";
@import "./pages/download.less";
@import "./pages/release-notes.less";
@import "./pages/releases.less";
@import "./pages/system-requirements.less";
@import "./pages/newsletter.less";
@import "./pages/donations-help.less";
@import "./pages/holiday.less";
@import './pages/privacy.less';
@import "./pages/donations-mobile.less";
@import "./pages/products.less";

// Appeal Page imports - These should be split off into their own bundles at some point
@import "./pages/survey.less";
@import "./pages/beta-appeal-24.less";