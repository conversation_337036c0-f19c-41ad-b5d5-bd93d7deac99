// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

// Box Shadow
each(@shadow, #(@k, @v) {
  .@{v} {
    box-shadow: @k;
  }
});

// Opacity
each(@opacity, #(@k, @v) {
  .@{v} {
    opacity: @k;
  }
});

// Drop shadow for PNG images
.shadow-img {
  filter: drop-shadow(5px 5px 6px rgba(0, 0, 0, 0.15));
}
