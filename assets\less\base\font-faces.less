/* - Metropolis -*/
@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Regular.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-Regular.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-RegularItalic.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-RegularItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ExtraLight.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-ExtraLight.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Light.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-Light.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Thin.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-Thin.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 100;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ExtraLightItalic.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-ExtraLightItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 200;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-LightItalic.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-LightItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ThinItalic.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-ThinItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Medium.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-Medium.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-SemiBold.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-SemiBold.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Bold.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-Bold.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-BoldItalic.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-BoldItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 500;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-MediumItalic.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-MediumItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-SemiBoldItalic.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-SemiBoldItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ExtraBold.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-ExtraBold.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ExtraBoldItalic.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-ExtraBoldItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Black.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-Black.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 900;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-BlackItalic.woff2") format("woff2"),
    url("/media/fonts/Metropolis/Metropolis-BlackItalic.woff") format("woff");
}

/* - Inter -*/
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Thin.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-Thin.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 100;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-ThinItalic.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-ThinItalic.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-ExtraLight.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-ExtraLight.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 200;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-ExtraLightItalic.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-ExtraLightItalic.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Light.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-Light.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-LightItalic.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-LightItalic.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Regular.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-Regular.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Italic.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-Italic.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Medium.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-Medium.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 500;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-MediumItalic.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-MediumItalic.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-SemiBold.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-SemiBold.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-SemiBoldItalic.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-SemiBoldItalic.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Bold.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-Bold.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-BoldItalic.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-BoldItalic.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-ExtraBold.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-ExtraBold.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-ExtraBoldItalic.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-ExtraBoldItalic.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Black.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-Black.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 900;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-BlackItalic.woff2?v=3.19") format("woff2"),
    url("/media/fonts/Inter/Inter-BlackItalic.woff?v=3.19") format("woff");
}

@font-face {
  font-family: 'Inter var';
  font-weight: 100 900;
  font-display: swap;
  font-style: normal;
  font-named-instance: 'Regular';
  src: url("/media/fonts/Inter/Inter-roman.var.woff2?v=3.19") format("woff2");
}

@font-face {
  font-family: 'Inter var';
  font-weight: 100 900;
  font-display: swap;
  font-style: italic;
  font-named-instance: 'Italic';
  src: url("/media/fonts/Inter/Inter-italic.var.woff2?v=3.19") format("woff2");
}
