// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

body {
  font-family: @base-font;
  &:extend(.font-base, .tracking-wide, .text-blue-darker);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Font Sizes
each(@fonts, #(@k, @v) {
  .@{v} {
    font-size: @k;
  }
});

// Font Style
each(@font-style, #(@k, @v) {
  .@{v} {
    font-style: @k;
  }
});

// Font Weight
each(@font-weight, #(@k, @v) {
  .@{v} {
    font-weight: @k;
  }
});

// Letter Spacing
each(@letter-spacing, #(@k, @v) {
  .@{v} {
    letter-spacing: @k;
  }
});

// Line Height
each(@line-height, #(@k, @v) {
  .@{v} {
    line-height: @k;
  }
});

// Decoration
each(@decoration, #(@k, @v) {
  .@{v} {
    text-decoration: @k;
  }
});

// Transform
each(@text-transform, #(@k, @v) {
  .@{v} {
    text-transform: @k;
  }
});

// Text Align
each(@text-align, #(@k, @v) {
  .@{v} {
    text-align: @k;
  }
});

each(@breakpoints, #(@k, @v) {
  @media (min-width: @k) {

    each(@fonts, #(@i, @r) {
      .@{v}\:@{r} {
        font-size: @i;
      }
    });

    // Line Height
    each(@line-height, #(@i, @r) {
      .@{v}\:@{r} {
        line-height: @i;
      }
    });

  }
});
