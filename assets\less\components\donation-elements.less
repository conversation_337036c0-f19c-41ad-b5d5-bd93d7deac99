// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

#modal-overlay {
    z-index: 9;
}

.modal {
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;

    #close-modal {
        cursor: pointer;

        svg {
            stroke: @colors[grey];
        }
        &:hover svg {
            stroke: @colors[black];
        }
    }

    form {
        .amount-selection {
            label {
                &:extend(.h-10, .pt-1, .pl-3, .pb-1, .pr-3, .rounded, .border, .border-solid, .border-grey, .bg-white, .font-xl, .font-bold);
                cursor: pointer;
                box-sizing: border-box;
                &:hover {
                    &:extend(.border-blue);
                }
                &:last-child {
                    &:extend(.pt-0, .pb-0, .pr-0, .bg-grey-lighter, .overflow-hidden);
                    flex-basis: @widths[w-40];
                    #amount-other {
                        &:extend(.w-full, .h-full, .pl-2, .border-0, .border-l, .border-solid, .border-grey, .font-xl);
                        box-sizing: border-box;
                        &::placeholder {
                            &:extend(.font-md);
                        }
                    }

                }
                &.active {
                    &:extend(.border-blue, .bg-blue-25, .shadow-none);
                }
            }
        }
    }

    footer {
        ul > li:not(:last-child)::after {
            content: '•';
            &:extend(.ml-3, .text-grey, .font-regular, .leading-none);
        }
    }
}

.ways-to-give-list-info {
    &:extend(.pl-0);
    list-style: none;

    li {
        &:extend(.pb-2);
    }
}

// Helper function / constant for our blue
.colour-blue(@alpha) {
    background-color: rgba(0, 128, 255, @alpha);
}

// Helper mixin to smooth out scale animations in Firefox
.force-smooth-animation {
    rotate: .1deg;
}

#donate-buttons {
    max-height: 60px;
}

#amount-cancel {
    p {
        &:extend(.leading-loose, .my-auto);
    }
}

.btn-donate-and-download {
    &:extend(.flex,
    .bg-white,
    .text-green,
    .font-xl,
    .font-semibold,
    .no-underline,
    .pt-4,
    .pb-4,
    .pl-5,
    .pr-6,
    .mr-4,
    .border-green-light,
    .rounded-sm,
    .border,
    .border-solid,
    .force-smooth-animation);

    @heart-wh: 20px;
    @heart-animation-speed: 0.60s;
    @heart-wave-animation-speed: @heart-animation-speed * 2;

    // Make sure our donate-wave div doesn't render outside of the button
    contain: paint;

    // Only appy our cool animations if they want it.
    @media (prefers-reduced-motion: no-preference) {
        .donate-wave {
            animation: linear @heart-wave-animation-speed heart-wave infinite;
        }

        .donate-heart {
            svg {
                animation: linear @heart-animation-speed alternate heart-bounce infinite;
            }
        }
    }

    .donate-wave {
        display: block;
        position: relative;
        margin: auto;
        // Hand crafted values from the heart. If you adjust sizing, make sure to tweak these!
        left: -256px + (@heart-wh/2);
        top: -256px - (@heart-wh/1.5);

        width: 512px;
        height: 512px;
        border-radius: 50%;
    }

    .donate-heart {
        &:extend(.mr-3, .text-blue);

        margin-top: 0.15rem;
        width: @heart-wh;
        height: @heart-wh;
    }

    p {
        &:extend(.my-auto);
    }

    &:hover,
    &:focus {
        &:extend(.shadow-md, .force-smooth-animation);
        .transform(translateY(-7%));
        .colour-blue(0.1);
    }
}

@keyframes heart-bounce {
    from {
        scale: 1.0;
    }
    to {
        scale: 1.3;
    }
}

@keyframes heart-wave {
    from {
        scale: 0;
    }
    25% {
        scale: 0;
        .colour-blue(0.40);
    }
    35% {
        .colour-blue(0.15);
    }
    to {
        scale: 2.0;
        .colour-blue(0.0);
    }
}