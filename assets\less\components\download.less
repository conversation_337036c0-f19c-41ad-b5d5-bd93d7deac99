// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

/* Retry Banner: "(i) Your download didn't start automatically? [Try Again]" */

// Yes javascript tweaks
.retry-download {
  .retry-text {
    margin-left: auto;
    margin-right: 0;
  }
  .retry-button {
    margin-right: 0;
  }
}

// No javascript tweaks
.no-js .retry-download {
  &:extend(.flex-col, .h-full);
  .retry-text {
    &:extend(.mt-4, .mx-auto);
    p {
      &:extend(.mr-6);
    }
  }
  .retry-button {
    &:extend(.mx-auto);
  }
}

/* Download buttons for the retry banner */

// By default we want to hide all the downloads, and then javascript will show the correct build
.download-hidden {
  &:extend(.hidden);
}
// We want to hide channel titles on javascript enabled browsers
.js .channel-title {
  &:extend(.hidden);
}
// Show everything if we don't have javascript
.no-js .download-hidden {
  display: block;
}
