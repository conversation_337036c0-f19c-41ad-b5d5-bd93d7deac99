// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

.appearance-none {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
}

.form-select {
  &:extend(.block, .w-40, .rounded-sm, .border-none, .bg-grey-light, .text-black, .p-1, .pr-6, .appearance-none);
}

.newsletter-form {
  &:extend(.shadow, .flex, .items-center, .rounded, .self-start, .p-1, .bg-grey-light);
  input {
    &:extend(.w-full, .rounded-sm, .appearance-none, .border-none, .p-3, .md\:p-2, .tracking-wide, .font-bold, .font-md, .md\:mr-2);
  }
  // But<PERSON> is in buttons.less
}