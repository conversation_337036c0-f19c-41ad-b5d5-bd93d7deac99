// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

h1, h2, h3, h4, h5, h6 {
  position: relative;

  .anchor {
    &:extend(.text-blue-dark);
    float: left;
    padding-right: 4px;
    margin-left: -20px;
    line-height: 1;
    opacity: 0;

    svg {
      width: 16px;
      height: 16px;
    }
  }

  &:hover,
  &:focus {
    .anchor {
      opacity: 1;
    }
  }
}

.header-section {
  &:extend(.uppercase, .mt-0, .mb-4, .font-2xl, .text-blue-dark, .flex, .items-center, .leading-normal);

  span {
    &:extend(.bg-blue, .p-3, .mr-6, .text-white, .w-6, .h-6, .rounded-full, .leading-none, .border-blue-lightest);
    border-width: 6px;
    border-style: solid;

    svg {
      width: 24px;
      height: 24px;
    }

    &.green {
      &:extend(.bg-green-light, .border-green-lightest);
    }
  }
}

.header-line {
  &:extend(.block, .bg-blue-lightest);
  height: 1px;
}

.subheader-section {
  &:extend(.mt-0, .mb-4, .font-lg, .text-blue-dark, .leading-normal);
}
