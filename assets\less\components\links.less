// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

a {
  .transition(@transition-default);
  cursor: pointer;
}

.nav-link {
  &:extend(.xl\:ml-6, .xl\:mr-6, .ml-4, .mr-4, .mt-2, .mb-2, .text-white, .no-underline, .relative, .pt-2, .pb-2, .inline-block);
  white-space: nowrap;

  &:after {
    &:extend(.absolute, .block, .pin-l, .pin-r, .pin-b, .bg-white, .opacity-0);
    .transition(all @time @swing);
    .transform(translateY(-10px));
    content: "";
    height: 1px;
  }

  &:hover,
  &:focus {
    &:after {
      &:extend(.opacity-100);
      .transform(translateY(0));
    }
  }
}

.nav-footer .nav-link {
  &:extend(.ml-3, .mr-3, .text-white, .no-underline, .relative, .pt-1, .pb-1);
}

.small-link {
  &:extend(.ml-1, .mr-1, .text-white, .no-underline);

  &:hover,
  &:focus {
    &:extend(.underline);
  }
}

.social-link-large {
  &:extend(.social-link, .font-regular, .bg-grey, .text-blue-dark, .font-semibold, .shadow-md, .mb-4);
  min-width: 164px;

  span {
    &:extend(.w-6, .h-6, .p-1, .bg-blue, .rounded-full, .mr-1, .text-center);
    .transition(@transition-default);

    svg {
      &:extend(.text-grey-lighter);
      // Make the icons just a little bit smaller
      width: 80%;
      height: 80%;
      margin-top: 0.15rem;
    }
  }

  &:hover,
  &:focus {
    &:extend(.bg-blue-dark, .text-white);
  }
}

.inline-link {
  &:extend(.font-semibold, .underline, .text-white);
}

.header-link {
  &:extend(.no-underline, .text-blue);

  &:hover,
  &:focus {
    &:extend(.underline);
  }
}

.social-link {
  &:extend(.ml-1, .mr-1, .text-white, .no-underline, .font-sm, .font-semibold, .flex, .items-center, .bg-black-lightest, .pt-1, .pb-1, .pl-1, .pr-2, .leading-none, .rounded-full);

  span {
    &:extend(.w-3, .h-3, .p-1, .bg-black-lighter, .rounded-full, .mr-1);
    .transition(@transition-default);

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &:hover,
  &:focus {
    &:extend(.bg-blue);
  }
}

.p-links {
  a {
    &:extend(.inline-link);
  }
}

.p-links-blue {
  a {
    &:extend(.inline-link, .text-blue);
  }
}

.blog-link {
  &:extend(.font-bold, .mt-0, .mb-4, .uppercase, .text-blue, .font-md, .no-underline);

  &:hover,
  &:focus {
    &:extend(.underline);
  }
}

.blog-body {
  &:extend(.mt-0, .mb-6, .flex-1);

  .go {
    display: none;
  }

  .thumbnail {
    text-align: center;

    img {
      width: auto;
      height: auto;
      max-width: 100%;
      max-height: 200px;
    }
  }

}
