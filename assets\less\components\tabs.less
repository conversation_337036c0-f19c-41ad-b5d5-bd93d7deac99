// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

.tabs {
  .tab {
    display: none;
  }
}

#release:checked ~ section.tab.release,
#beta:checked ~ section.tab.beta,
#daily:checked ~ section.tab.daily {
  display: flex;
}

.tabs-nav {
  label {
    &:extend(.inline-block, .uppercase, .p-1, .bg-transparent, .rounded-sm, .font-sm);
    cursor: pointer;

    &:hover {
      &:extend(.bg-black-lightest, .text-white);
    }
  }

  li ~ li {
    &:extend(.ml-2);
  }

  #channel-header {
    &:extend(.inline-block, .uppercase, .p-1, .bg-transparent, .rounded-sm, .font-sm);
  }
}

#release:checked ~ aside nav .tabs-nav .release label,
#beta:checked ~ aside nav .tabs-nav .beta label,
#daily:checked ~ aside nav .tabs-nav .daily label {
  &:extend(.bg-blue, .text-white);
}
section.tab.release a.small-link {
  &:extend(.text-blue);
}
