@keyframes fade-in-out {
  0% {
    opacity: 0
  }
  32% {
    opacity: 0;
  }
  33.33% {
    opacity: 1;
  }

  65% {
    opacity: 1;
  }

  66.66% {
    opacity: 0;
  }

  100% {
    opacity: 0;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes sweep {
  0% {
    opacity: 0;
    margin-top: -1rem;
  }
  100% {
    opacity: 1;
    margin-top: 1rem;
  }
}

@keyframes sweep-no-margin {
  0% {
    opacity: 0;
    margin-top: -1rem;
  }
  100% {
    opacity: 1;
    margin-top: 0;
  }
}

@keyframes float-around {
  0% {
    transform: translate(0, -4px);
  }

  25% {
    transform: translate(4px, 0);
  }

  50% {
    transform: translate(0, 4px);
  }

  75% {
    transform: translate(-4px, 0);
  }

  100% {
    transform: translate(0, -4px);
  }
}

@keyframes float-around-shadow {
  0% {
    filter: drop-shadow(0 4px 4px var(--color-gray-10));
  }

  25% {
    filter: drop-shadow(-4px 0 4px var(--color-gray-10));
  }

  50% {
    filter: drop-shadow(0 -4px 4px var(--color-gray-10));
  }

  75% {
    filter: drop-shadow(4px 0 4px var(--color-gray-10));
  }

  100% {
    filter: drop-shadow(0 4px 4px var(--color-gray-10));
  }
}

@keyframes sunset-gradient {
	0% {
		background-position: 0 0;
	}
	40% {
		background-position: 0 40%;
	}
    50% {
		background-position: 0 100%;
	}
    60% {
		background-position: 0 40%;
	}
	100% {
		background-position: 0 0;
	}
}

@keyframes sunset-gradient-linear {
	0% {
		background-position: 0 90%;
	}
    100% {
		background-position: 0 0;
	}
}
