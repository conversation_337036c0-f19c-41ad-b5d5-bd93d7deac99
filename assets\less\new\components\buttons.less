@import "../functions.less";
@import '../mixins.less';

.btn,
.btn-download,
[type="button"] {
  @outline();
  // Moved from @outline
  &:focus {
    outline: var(--focus-outline);
    outline-offset: var(--focus-outline-offset);
  }
}

.btn {
  cursor: pointer;
}

.btn:disabled {
  cursor: not-allowed;
  filter: grayscale(50%);
}

/* Remove some list padding */
.download-dumb > ul {
  padding: 0;
}

.download-button > ul {
  padding: 0;
}

.header-donate, .header-download {
  font-weight: 500;
  margin-top: -0.05rem;
  margin-left: -1rem;

  a.btn {
    padding: 0.72em 1em;
    border-width: 0.125rem; //2px;
  }

  a.btn::after {
    background: transparent;
  }

  svg {
    width: 1rem;
  }
}

.btn-donate,
.btn-donate-lg,
.btn-download-inline,
.btn-download-link,
.download-link.btn-download,
.btn,
button {
  font-size: var(--font-md);
  background: var(--btn-bg, transparent);
  border-radius: 0.375rem; //6px;
  border: .125em solid currentColor;
  color: currentColor;
  padding: .5em 3em;
  position: relative;
  transition: font-size .2s;
  text-decoration: none;
  white-space: nowrap;
}

.btn-donate::before,
.btn-donate-lg::before,
.btn-download-inline::before,
.btn-download-link::before,
.download-link.btn-download::before,
.btn::before {
  content: '';
  position: absolute;
  background: var(--glow);
  border-radius: 50%;
  filter: blur(32px);
  opacity: .25;
  inset: -.25em -2.5em;
  z-index: -2;
}

.btn-donate::after,
.btn-donate-lg::after,
.btn-download-inline::after,
.btn-download-link::after,
.download-link.btn-download::after,
.btn::after {
  content: '';
  position: absolute;
  background-color: transparent;
  background-image: radial-gradient(circle, var(--accent), var(--color-purple-60) 30%);
  background-size: 400%;
  background-position: center;
  opacity: .5;
  inset: 0;
  z-index: -1;
  transition: all .3s;
}

.download-link.btn-download:hover::after,
.btn:hover::after,
button:hover::after {
  opacity: .75;
  background-size: 500%;
}

/*
/** HACK for Mozilla Firefox */
@-moz-document url-prefix() {
  .download-link.btn-download::after {
    inset-inline-end: 0.25rem; //4px;
  }
}

.download-button .ios-download,
.download-button .linux-arm-download,
.download-button .unrecognized-download,
.download-button .unsupported-download,
.download-button .unsupported-download-osx,
.download-button .nojs-download {
  display: none;
}

.download-button .os_msi,
.download-button .os_winsha1,
.download-button .os_win64,
.download-button .os_win8-64,
.download-button .os_linux,
.download-button .os_linux64,
.win7-8.x86.x64 .download-button .os_win8,
.win10up.x86.x64 .download-button .os_win,
.android .download-button-desktop,
.windows.arm .download-button .os_win,
.linux.arm .download-button .os_linux,
.linux.x86.x64 .download-list .os_linux,
.download-button .os_win,
.download-button .os_win8,
.download-button .os_osx,
.download-button .os_android,
.download-button .os_ios,
.no-js .download-list,
.other .download-list {
  display: none !important;
}

.win7-8.x86 .download-button .os_win8,
.win7-8.x86.x64 .download-button .os_win8-64,
.win10up.x86.x64 .download-button .os_win64,
.win10up.x86 .download-button .os_win,
.linux .download-button .os_linux,
.linux.x86.x64 .download-button .os_linux64,
.osx .download-button .os_osx,
.android .download-button .os_android,
.download-button-android .os_android,
.android .download-button-desktop .download-list,
.android .download-button-desktop small.os_win,
.download-button-ios .os_ios,
.ios .download-button .os_ios,
.ios .download-button .ios-download,
.ios .download-button-desktop .download-list,
.other .download-button-android .download-list,
.other .download-button small.os_win {
  display: block !important;
}

.windows.arm .download-button .unsupported-download,
.linux.arm .download-button .linux-arm-download,
.chromeos .download-button .unsupported-download,
.oldwin .download-button .unsupported-download,
.oldmac .download-button .unsupported-download {
  display: block;
  max-width: 15.625rem; //250px;
}

.windows.arm .download-button .fx-privacy-link,
.linux.arm .download-button .fx-privacy-link,
.chromeos .download-button .fx-privacy-link,
.oldwin .download-button .fx-privacy-link,
.oldmac .download-button .fx-privacy-link {
  display: none;
}

.android .download-button-desktop .nojs-download,
.ios .download-button-desktop .nojs-download,
.no-js .download-button .nojs-download {
  display: block;
}

.other .download-button .unrecognized-download {
  display: block;
}

.download-button .download-list .os_android.x86,
.download-button .download-other.os_android .api-15,
.android.x86 .download-button .download-list .os_android.armv7up,
.android.x86 .download-button .download-other.os_android .x86 {
  display: none !important;
}

.android.x86 .download-button .download-list .os_android.x86 {
  display: block !important;
}

.android.x86 .download-button .download-other.os_android .armv7up {
  display: inline !important;
}

.windows.sha-1 .download-button .os_win {
  display: none !important;
}

.windows.sha-1 .download-button .os_winsha1 {
  display: block !important;
}

.download-button ul {
  list-style: none;
}

.download-button ul li {
  margin-block: 1.25rem; //20px;
}

.download-dumb {
  ul li {
    margin-block: 2.5rem; //40px;
  }
}

.all-downloads-link {
  margin-bottom: 2rem; //32px;
}


// Make the smart download button a bit larger
.download-button {
  .btn-download {
    font-size: var(--font-lg);
    font-weight: 600;
  }
}

// TODO: This is for the fancy padding-box hover border effect thingy. I'll clean this up later
.btn-no-bg {
  --btn-background-color: var(--color-white);
}

.btn-white-bg {
  --btn-background-color: var(--color-white);
}

.btn-black-bg {
  --btn-background-color: var(--color-black);
}

.btn-no-bg::after {
  background: transparent;
}

.btn-white-bg::after {
  opacity: 1 !important;
  background: var(--color-white) !important;
}

.btn-black-bg::after {
  opacity: 1 !important;
  background: var(--color-black) !important;
}

.btn-gradient::after {
  content: '';
  position: absolute;
  background-color: transparent;
  background-image: linear-gradient(90deg, var(--color-blue-60) 30%, var(--color-purple-60) 70%);
  background-size: 400%;
  background-position: center;
  opacity: 0.9;
  inset: 0;
  z-index: -1;
  transition: all .3s;
}

.btn-black-bg:hover,
.btn-white-bg:hover,
.btn-no-bg:hover {
  background: linear-gradient(var(--btn-background-color), var(--btn-background-color)) padding-box,
  linear-gradient(to right, var(--color-blue-50), var(--color-purple-50)) border-box;
  border-color: transparent;
  transition: all 250ms;
  color: black;

  &::after {
    opacity: 0.75;
  }
}

.btn-black-bg:active,
.btn-white-bg:active,
.btn-no-bg:active {
  background: linear-gradient(var(--btn-background-color), var(--btn-background-color)) padding-box,
  linear-gradient(to right, var(--color-button-border-active), var(--color-button-border-active)) border-box;
  border-color: transparent;
  transition: all 250ms;

  // Remove the focus state
  outline: none;
}

.btn-newsletter {
  position: relative;
  border: none;
  border-radius: 0;
  border-inline-start: 2px solid currentColor;
  padding: .75rem 2.5rem;
  margin: 0;
  font-size: var(--font-md);
  // FIXME: This needs to get cleaned up and applied to btns generically
  --btn-background-color: rgba(0, 0, 0, 0.8);
  @btn-no-bg-styling();
}

.btn-newsletter-icon {
  display: none;
  padding: .75em 1em;
}

@media (max-width: @sm) {
  .btn-newsletter-text {
    display: none;
  }

  .btn-newsletter-icon {
    display: block;
  }
}

// Slimmer for the download page
.btn-slim {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  font-size: var(--font-md) !important;
  min-width: 8.0rem;
  padding: 0.5rem 1rem;
}

.btn-no-bg-dark {
  --btn-background-color: rgba(0, 0, 0, 0.8);
  &:hover {
      color: currentColor;
  }
}

.download-icon {
  position: relative;
  top: 0.1rem;
  display: inline-block;
  width: 1rem;
  height: 1rem;
}

// Container for the newer donation buttons

.download-buttons-container {
  margin-top: 5rem;
  margin-bottom: 4rem;
}

// TODO: need to clean the old ones
.download-button-products {
  --txt: var(--color-white);
}

.no-js, .other {
  .download-button-products {
    flex-direction: column;
  }
}

.download-button-page,
.download-button-products {
  display: flex;
  gap: 2rem;
  justify-content: center;
  align-items: center;
  color: var(--txt);
  text-align: center;
}

@media (max-width: @lg) {
  .download-button-page, .download-button-products {
    flex-direction: column;
  }
}


// Desktop - The operating system short-code (e.g. osx, linux, win10up, etc...)
[data-download-os] {
  display: none;
}

// desktop or android
// This is only used when showing all platforms
[data-download-platform] {
  display: none;
}

.no-js-label, .no-js-title {
  display: none;
}

.js-label {
  display: inline-block;
}

// For non-javascript users show everything
// - Also for android users
.no-js {
  .no-js-label {
    display: inline-block;
  }

  .js-label {
    display: none;
  }

  [data-download-os] {
    display: block !important;
  }
}

// HACK: For desktop page...
.page-desktop {
  &.android {
    [data-download-platform="other"] {
      display: block !important;
    }
  }
}

// This is only for no-js, and unrecognized devices
.no-js,
.other,
.ios,
.chromeos,
.oldwin,
.oldmac,
.fxos {
  [data-download-platform="other"] {
    display: block !important;
  }
}

// Selectively show the correct download button
.js {
  // macOS
  &.osx {
    [data-download-os="osx"] {
      display: block !important;
    }
  }

  // Windows
  &.win7-8 {
    &.x64 [data-download-os="win8-64"] {
      display: block !important;
    }

    &.x86.x64 [data-download-os="win8"] {
      display: none !important;
    }

    &.x86 [data-download-os="win8"] {
      display: block !important;
    }
  }

  &.win10up {
    &.x64 [data-download-os="win64"] {
      display: block !important;
    }

    &.x86.x64 [data-download-os="win"] {
      display: none !important;
    }

    &.x86 [data-download-os="win"] {
      display: block !important;
    }
  }

  &.linux {
    &.x64 [data-download-os="linux64"] {
      display: block !important;
    }

    &.x86.x64 [data-download-os="linux"] {
      display: none !important;
    }

    &.x86 [data-download-os="linux"] {
      display: block !important;
    }
  }

  // Platform hiding/un-hiding
  &.win7-8,
  &.win10up,
  &.osx,
  &.linux {
    [data-download-platform="desktop"] {
      display: block !important;
    }
  }

  &.android {
    [data-download-platform="android"] {
      display: block !important;
    }
  }
}