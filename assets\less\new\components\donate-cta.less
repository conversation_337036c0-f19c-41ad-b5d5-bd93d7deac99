@import "../colours.less";

.donate-banner {
  --background-left: linear-gradient(92deg, #EA5757 60%, #DD2929 92.83%);
  --background-right: linear-gradient(to right, #FFEFEF, #FFEFEF);
  --accent-text: #5A0002;

  background: var(--color-red-50);
  background-image: var(--background-right);
  background-position: top left, center;
  background-size: contain;
  background-repeat: no-repeat;
  border-radius: 3px;
  box-shadow: 4px 4px 24px rgba(0, 0, 0, 0.15);
  color: white;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  font-size: 1.125rem;
  line-height: 1.3;
  margin: -114px auto 0;
  text-decoration: none;
  text-shadow: var(--shadow);
  transition: transform 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
  max-width: 462px;
  min-height: 123px;
  z-index: 10;
}

.donate-banner::after {
  content: '';
  position: absolute;
  top: -210px;
  left: -220px; /* -20px for l10n overlap */
  height: 420px;
  width: 420px;
  background: var(--background-left);
  transform: rotate(36deg);
  transition: transform 0.3s ease-in-out;
}

.donate-banner:hover {
  transform: scale(1.05);
}

.donate-banner:hover::after {
  transform: scale(3)
}

.donate-banner:hover #decoration,
.donate-banner:hover #donate-banner-right {
  opacity: 0;
}

.donate-banner:hover #donate-banner-left {
  transform: translate(24px, 6px);
}

#donate-banner-left,
#donate-banner-right {
  display: flex;
  flex-direction: column;

  flex: 1;
  transition: all .5s ease-in-out;
  z-index: 2;
}

#donate-banner-left {
  padding: 11px 12px;
  font-weight: 300;
}

#donate-banner-right {
  padding: 17px 12px;
  font-size: 1.5rem;
  font-weight: 300;
  padding-inline-start: 3px;
  text-shadow: 0 0 4px var(--color-gray-10), 0 1px 1px var(--color-gray-10);
}

#donate-banner-left > b {
  font-size: 2.0625rem;
  font-weight: 700;
  line-height: 1;
}

#donate-banner-right > b {
  font-size: 1.5rem;
  font-weight: 400;
}

#decoration > #pill-1 {
  animation: 20s infinite pill-float linear;
  animation-delay: 0.2s;
}

#decoration > #pill-2 {
  animation: 30s infinite pill-float linear;
}

#decoration > #pill-3 {
  animation: 35s infinite pill-float linear;
  animation-delay: 0.1s;
}

#decoration > #pill-4 {
  animation: 25s infinite pill-float linear;
  animation-delay: 0.3s;
}

#decoration > #pill-5 {
  animation: 40s infinite pill-float linear;
  animation-delay: 0.4s;
}

#decoration > #pill-6 {
  animation: 30s infinite pill-float linear;
  animation-delay: 0.5s;
}

#decoration > #pill-7 {
  animation: 25s infinite pill-float linear;
  animation-delay: 0.6s;
}

#decoration > #pill-8 {
  animation: 20s infinite pill-float linear;
  animation-delay: 0.7s;
}

#decoration > #pill-9 {
  animation: 40s infinite pill-float linear;
  animation-delay: 0.8s;
}

#decoration > #pill-10 {
  animation: 35s infinite pill-float linear;
  animation-delay: 0.4s;
}

#decoration {
  position: absolute;
  left: -55px;
  display: grid;
  gap: 3px;
  grid-template-columns: repeat(20, 1fr);
  grid-template-rows: repeat(10, 1fr);
  place-items: stretch;
  height: 90px;
  width: 500px;
  transform: rotate(-54deg);
  transition: opacity .5s ease-in-out;
  z-index: 1;
}

.pill {
  display: block;
  background: var(--color-red-40);
  opacity: 0.75;
  border-radius: 1000px;
  transform: translateX(-250px);
}

@keyframes pill-float {
  from {
    transform: translateX(-250px);
  }

  to {
    transform: translateX(250px);
  }
}

#pill-1 {
  grid-column: 4 / span 8;
  grid-row: 5 / span 3;

}

#pill-2 {
  grid-column: 10 / span 4;
  grid-row: 6 / span 1;
}

#pill-3 {
  grid-column: 8 / span 3;
  grid-row: 3 / span 1;
}

#pill-4 {
  grid-column: 4 / span 3;
  grid-row: 3 / span 1;
}

#pill-5 {
  grid-column: 6 / span 4;
  grid-row: 9 / span 1;
}

#pill-6 {
  grid-column: 11 / span 1;
  grid-row: 9 / span 1;
}

#pill-7 {
  grid-column: 13 / span 2;
  grid-row: 10 / span 1;
}

#pill-8 {
  grid-column: 13 / span 3;
  grid-row: 8 / span 1;
}

#pill-9 {
  grid-column: 14 / span 3;
  grid-row: 4 / span 1;
}

#pill-10 {
  grid-column: 12 / span 1;
  grid-row: 4 / span 1;
}

#hover-hearts {
  position: absolute;
  inset-block-end: 12px;
  inset-inline-end: 13px;
  display: none;
  gap: 3px;
  z-index: 1;
}

#hover-heart-1,
#hover-heart-2,
#hover-heart-3 {
  display: flex;
  align-items: end;
  opacity: 0;
}

#hover-heart-1 > svg {
  animation-delay: 0.6s;
}

#hover-heart-3 {
  animation-delay: 0.8s;
}

#hover-heart-1 > svg {
  transform: rotate(-45deg);
  width: 48px;
  height: 48px;
}

#hover-heart-2 > svg {
  width: 64px;
  height: 64px;
  transform: translateY(-9px);
}

#hover-heart-3 > svg {
  transform: rotate(45deg);
  width: 48px;
  height: 48px;
}

.donate-banner:hover #hover-hearts {
  display: flex;
}

.donate-banner:hover #hover-hearts > #hover-heart-1 {
  animation: 1s heart-fade forwards ease-in-out;
  animation-delay: 0.1s;
}

.donate-banner:hover #hover-hearts > #hover-heart-2 {
  animation: 1s heart-fade forwards ease-in-out;
}

.donate-banner:hover #hover-hearts > #hover-heart-3 {
  animation: 1s heart-fade forwards ease-in-out;
  animation-delay: 0.2s;
}

@keyframes heart-fade {
  from {
    opacity: 0;
    transform: translateY(12px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}