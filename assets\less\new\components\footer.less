
footer {
  background-color: black;
  color: var(--color-gray-20);
}

.container.footer {
  place-items: center;
  padding-inline: 1rem;
  padding-block: 6rem;
  // Prevent overflow on smol devices
  box-sizing: border-box;
  // Always have this. (FIXME: This should be done cleaner)
  gap: 60px !important;
}

.footer-ln {
  font-size: 1.1rem;
}

.legal-links {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
  justify-content: center;
}

.legal-links {
  font-size: 1rem;
}



.mozilla-logo {
  --accent: white;
  max-width: 200px;
}

#footer {
  width: 100%;
  position: relative;
  margin-top: auto;
  margin-bottom: 0;
  padding-bottom: 2rem;
  min-height: 10rem;
  display: flex;
  justify-content: center;


  .mzla {
    justify-content: flex-start;
    align-content: center;
  }
}