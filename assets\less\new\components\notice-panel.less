.notice-panel.warning {
  --text-gradient: linear-gradient(-270deg, var(--color-magenta-50), var(--color-purple-50));
  --bg-warning: color-mix(in srgb, var(--color-magenta-50) 5%, transparent);
  --text-warning: var(--color-magenta-90);
  --warning-links: var(--color-purple-80);
  --list-dot-inner: var(--color-magenta-90);
  --list-dot-outer: var(--color-magenta-10);
}

.notice-panel.info {
  --text-gradient: linear-gradient(-270deg, var(--color-teal-50), var(--color-blue-50));
  --bg-warning: color-mix(in srgb, var(--color-teal-50) 5%, transparent);
  --text-warning: var(--color-teal-90);
  --warning-links: var(--color-blue-80);
  --list-dot-inner: var(--color-teal-90);
  --list-dot-outer: var(--color-teal-10);
}

// Steal this from start for now
.notice-panel {
  place-items: center;
  background: var(--bg-warning);
  color: var(--text-warning);
  border: 1px solid var(--text-warning);
  border-radius: 6px;
  padding: 0.5em 1em;
  max-width: 40.0rem;
  font-weight: 400;

  margin: auto;

  p {
    margin: 0;
  }

  b {
    font-weight: 700;
  }

  a {
    color: var(--warning-links);
  }
}