/*
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 */

/* Open Sans */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/opensans-regular.woff2") format("woff2"),
  url("/media/fonts/opensans-regular.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/opensans-italic.woff2") format("woff2"),
  url("/media/fonts/opensans-italic.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/opensans-light.woff2") format("woff2"),
  url("/media/fonts/opensans-light.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/opensans-lightitalic.woff2") format("woff2"),
  url("/media/fonts/opensans-lightitalic.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/opensans-semibold.woff2") format("woff2"),
  url("/media/fonts/opensans-semibold.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/opensans-semibolditalic.woff2") format("woff2"),
  url("/media/fonts/opensans-semibolditalic.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/opensans-bold.woff2") format("woff2"),
  url("/media/fonts/opensans-bold.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/opensans-bolditalic.woff2") format("woff2"),
  url("/media/fonts/opensans-bolditalic.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/opensans-extrabold.woff2") format("woff2"),
  url("/media/fonts/opensans-extrabold.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/opensans-extrabolditalic.woff2") format("woff2"),
  url("/media/fonts/opensans-extrabolditalic.woff") format("woff");
}

/* Nicer fallbacks */
@font-face {
  font-family: 'system-ui';
  font-style: normal;
  font-weight: 300;
  src: local(".SFNS-Light"), local(".SFNSText-Light"), local("Segoe UI Light"), local("Ubuntu Light");
}

@font-face {
  font-family: 'system-ui';
  font-style: italic;
  font-weight: 300;
  src: local(".SFNS-LightItalic"), local(".SFNSText-LightItalic"), local("Segoe UI Light Italic"), local("Ubuntu Light Italic");
}

@font-face {
  font-family: 'system-ui';
  font-style: normal;
  font-weight: 400;
  src: local(".SFNS-Regular"), local(".SFNSText-Regular"), local("Segoe UI"), local("Ubuntu");
}

@font-face {
  font-family: 'system-ui';
  font-style: italic;
  font-weight: 400;
  src: local(".SFNS-Italic"), local(".SFNSText-Italic"), local("Segoe UI Italic"), local("Ubuntu Italic");
}

@font-face {
  font-family: 'system-ui';
  font-style: normal;
  font-weight: 500;
  src: local(".SFNS-Medium"), local(".SFNSText-Medium"), local("Segoe UI Semibold"), local("Ubuntu Medium");
}

@font-face {
  font-family: 'system-ui';
  font-style: italic;
  font-weight: 500;
  src: local(".SFNS-MediumItalic"), local(".SFNSText-MediumItalic"), local("Segoe UI Semibold Italic"), local("Ubuntu Medium Italic");
}

@font-face {
  font-family: 'system-ui';
  font-style: normal;
  font-weight: 700;
  src: local(".SFNS-Bold"), local(".SFNSText-Bold"), local("Segoe UI Bold"), local("Ubuntu Bold");
}

@font-face {
  font-family: 'system-ui';
  font-style: italic;
  font-weight: 700;
  src: local(".SFNS-BoldItalic"), local(".SFNSText-BoldItalic"), local("Segoe UI Bold Italic"), local("Ubuntu Bold Italic");
}