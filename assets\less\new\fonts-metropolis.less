/*
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 */
/* - Metropolis -*/
@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Regular.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-Regular.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-RegularItalic.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-RegularItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ExtraLight.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-ExtraLight.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Light.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-Light.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Thin.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-Thin.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 100;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ExtraLightItalic.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-ExtraLightItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 200;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-LightItalic.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-LightItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ThinItalic.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-ThinItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Medium.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-Medium.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-SemiBold.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-SemiBold.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Bold.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-Bold.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-BoldItalic.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-BoldItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 500;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-MediumItalic.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-MediumItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-SemiBoldItalic.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-SemiBoldItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ExtraBold.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-ExtraBold.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ExtraBoldItalic.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-ExtraBoldItalic.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Black.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-Black.woff") format("woff");
}

@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 900;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-BlackItalic.woff2") format("woff2"),
  url("/media/fonts/Metropolis/Metropolis-BlackItalic.woff") format("woff");
}
