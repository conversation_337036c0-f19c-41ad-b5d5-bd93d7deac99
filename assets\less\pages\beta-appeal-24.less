.windows.x86.page-beta-appeal-24 {
  .btn-container.default {
    display: none !important;
  }
  .btn-container.win32bit {
    display: block !important;
  }
  .btn-container.win64bit {
    display: none !important;
  }
}
.windows.x86.x64.page-beta-appeal-24 {
  .btn-container.default {
    display: none !important;
  }
  .btn-container.win32bit {
    display: none !important;
  }
  .btn-container.win64bit {
    display: block !important;
  }
}

.page-beta-appeal-24 {
  body {
    font-family: 'Metropolis', sans-serif;
    font-size: var(--font-md);
    line-height: 1.875rem;
    font-weight: 400;

    --nav-height: 42.5px;
  }

  #masthead {
    padding-bottom: 110px;
  }

  .tagline {
    text-wrap: balance;
    margin: auto !important;
    max-width: 56.25rem;
  }

  .container {
    gap: 20px;
    padding-bottom: 9.5rem; // Hardcoded to help button have some space
  }

  .btn-container {
    &.win64bit,
    &.win32bit {
      display: none;
    }
  }

  .btn {
    font-weight: 700;
    font-size: var(--font-lg);
  }

  .appeal-footer-container {
    background-color: var(--color-black);
    color: var(--color-gray-20);
  }

  .page-separator-cover {
    margin-bottom: -2px;
  }

  .footer {
    padding-bottom: 1rem;
  }

  .two-columns {
    display: flex;

    a {
      color: var(--color-link);
    }


    .image {
      max-width: 20rem; // 320px~
      margin: auto;
    }

    .section-text {
      text-align: left;
      max-inline-size: 100%;

      display: flex;
      flex-direction: column;
      gap: 2rem;

      p {
        margin: auto;
        max-width: 45.125rem; // 722px~
      }
    }
  }

  @media (max-width: @md) {
    .two-columns {
      flex-direction: column;
      gap: 1rem;
    }
  }
}


