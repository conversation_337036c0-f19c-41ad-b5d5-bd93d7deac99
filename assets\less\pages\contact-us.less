.page-contact-us {
  .block-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1.5rem;

    @media (max-width: @lg) {
      padding: 1rem;
    }
  }
  .block {
    display: flex;
    flex-direction: column;
    border: var(--border-size) var(--border-color) solid;
    border-radius: var(--border-radius-lg);
    padding: 1.75rem;
    width: 20rem;
    text-align: left;

    @media (max-width: @lg) {
      width: 100%;
    }
  }
  .block-head {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    justify-content: left;
    align-items: center;
    h2 {
      margin: 0;
    }
  }
}