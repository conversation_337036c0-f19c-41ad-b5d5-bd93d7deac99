.page-donations {
  .image {
    padding: 2rem;

    @media (max-width: @lg) {
      order: -1;
      padding: 0;
      width: 100%;
    }
  }

  .two-columns {
    display: flex;
    flex-direction: row;
    gap: 2rem;
    margin: auto;
    padding-inline: 2rem;
    width: auto;

    @media (max-width: @lg) {
      flex-direction: column;
      margin: 0;
    }

    .info-column {
      text-align: left;
      min-width: 50%;

      .card-list {
        padding: 0;
        display: flex;
        flex-direction: row;
        gap: 0.75rem;
        list-style: none;
      }
    }

    .donation-column {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      min-width: 45%;
      min-height: 480px;


      iframe {
        width: 100%;
        // Fade in so it's not super jarring...
        animation-duration: 2s;
        animation-name: fade-in;

        border: var(--border-size) solid var(--border-color);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-xl);

      }

      p {
        position: absolute;
        bottom: -1rem;
        font-size: var(--font-sm);
        width: 100%;
      }
    }
  }

  .faq {
    h2 {
      text-align: center;
      margin-bottom: 1rem;
    }
  }

  .check-address {
    list-style: none;
    padding-left: 0;
  }
}


