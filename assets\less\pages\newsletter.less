.page-newsletter {
  #main-content {
    display: none;
  }
  .pre-footer-cover-container {
    display: none;
  }
  .header-separator {
    background-color: var(--color-black);
  }
  #whats-next {
    .section-text {
      display: flex;
      flex-direction: column;
    }
    .section-text, p {
      width: 100%;
      margin-left: auto;
      margin-right: auto;

      max-inline-size: 80%;
    }
  }
}