.page-desktop {
  #masthead {
    --bg-hero: url('/media/img/thunderbird/base/desktop/banner-bg-high-res.png');
    --bg-hero-set: image-set(url('/media/img/thunderbird/base/desktop/banner-bg-high-res.avif') type('image/avif'),
    url('/media/img/thunderbird/base/desktop/banner-bg-high-res.webp') type('image/webp'),
    var(--bg-hero) type('image/png'));
  }
}

.page-mobile {
  #masthead {
    --bg-hero: url('/media/img/thunderbird/base/mobile/banner-bg-high-res.png');
    --bg-hero-set: image-set(url('/media/img/thunderbird/base/mobile/banner-bg-high-res.avif') type('image/avif'),
    url('/media/img/thunderbird/base/mobile/banner-bg-high-res.webp') type('image/webp'),
    var(--bg-hero) type('image/png'));
  }
}

.page-desktop, .page-mobile {
  #masthead,
  header {
    --bg-hero-gradient: linear-gradient(to top, var(--color-ink-90), black 80%);

    display: grid;
    place-items: center;
    min-height: 50vh;
    background-color: black;
    background-image: var(--bg-hero), var(--bg-hero-gradient);
    background-image: var(--bg-hero-set), var(--bg-hero-gradient);
    background-size: auto 85%, 100%;
    background-position: bottom center, center;
    background-repeat: no-repeat;
    color: var(--color-gray-20);
    isolation: isolate;
    padding-top: var(--nav-height);

    // More we take from the bottom, the more we need to pull back up
    padding-bottom: 10.5rem;

    // Adjust the hero text for the display background
    .hero {
      // Product pages are a lil' smaller
      --font-tagline-clamp: clamp(var(--font-tagline-min), var(--font-tagline-ideal), 4rem);

      position: relative;
      top: 7rem;
    }
  }

  .container {
    gap: 2rem;
  }

  .cover-container {

    .page-separator-cover {
      color: var(--bg);
    }
  }

  .hero-download {
    margin-top: 3rem;
  }

  // Override logo colours
  .logo {
    --accent: var(--color-blue-20);
  }

  .logo:hover,
  .mozilla-logo:hover {
    color: var(--accent);
  }

  .site-nav {
    color: var(--nav-txt);

    --hamburger-background-inactive: var(--color-black);
    --hamburger-foreground-inactive: var(--color-gray-30);
    --hamburger-background-active: var(--color-white);
    --hamburger-foreground-active: var(--color-black);
  }

  .content-block {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20.0rem;
    height: 20.0rem;
    border-radius: 1.0rem;
    box-shadow: var(--shadow-2xl);

    img, video, picture {
      width: 100%;
      height: 100%;
      background-color: lightgrey;
      border-radius: 1.0rem;
    }

    // Temp until missing assets come in
    svg {
      border-radius: 1.0rem;
      background: linear-gradient(180deg, #e0b3ff 0%, #a880c0 100%);
    }
  }

  .section-quote {
    width: 100%;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    &::before, &::after {
      content: '';
      display: block;
      width: 100%;
      height: 0.125rem;

      background-color: #dedede;
    }
  }

  .info-resource {
    picture, img {
      border-radius: 100%;
    }

    p {
      max-inline-size: 20rem;
    }
  }

  .testimonials {
    max-width: initial;
  }
}
