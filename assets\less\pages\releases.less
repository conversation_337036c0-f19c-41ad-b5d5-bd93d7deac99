.page-releases {
  .release-list {
    width: 100%;
  }

  .release-container {
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 1rem;
    border-bottom: var(--border-size) solid;
    border-color: var(--border-color);
    padding-top: 2rem;
    padding-bottom: 2rem;
    @media (max-width: @md) {
      flex-direction: column;
    }

    &:last-child {
      border: none;
    }
  }

  .release-major {
    font-size: 150%;
    min-width: 15%;
    margin-top: 0;
    text-align: left;

    a {
      text-decoration-color: var(--bg);
      text-decoration-thickness: 1px;
      text-underline-offset: 0.2em;
      &:hover {
        text-decoration-color: var(--accent);
      }
    }
  }

  .release-minors {
    width: 100%;
  }

  .release-minors ul {
    display: grid;
    grid-template-rows: repeat(auto-fit, minmax(0, 2rem));
    grid-template-columns: repeat(auto-fit, minmax(0, 8.5rem));
    grid-auto-flow: column;
    max-height: 20rem;

    text-align: left;

    width: 100%;
    padding-left: 2rem;
    padding-right: 2rem;
    gap: 0.5rem;
    align-items: start;
    list-style: none;
    line-height: 1.5;
    margin: auto;
  }


  .two-columns {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    margin: auto;
    justify-content: center;
    text-align: left;
  }

  .atom-feed {
    display: flex;
    width: 4rem;
    text-align: right;
    align-items: center;
  }

  @media (max-width: @xs) {
    .release-minors ul {
      /* More of a hack than anything */
      max-height: 40rem;
      padding: 0;
    }
  }
}
