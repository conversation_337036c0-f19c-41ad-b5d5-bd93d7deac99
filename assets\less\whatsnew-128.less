// Base
@import "new/fonts.less";
@import "new/colours.less";
@import "new/links.less";
@import "new/components/appeal-footer.less";

/*-------------------------
* Media Queries
*--------------------------*/
@xs: 30rem; // 480px
@sm: 40rem; // 640px
@md: 48rem; // 768px
@lg: 64rem; // 1024px
@xl: 80rem; // 1280px
@xxl: 90rem; // 1440px

:root {
  // New palette
  --color-purple-20: #F5E8FF;

  --text-color: var(--color-white);
  --extension-background: #1A202C;
  --appeal-background: linear-gradient(169.85deg, #121621 2.48%, #16294D 32.48%);
  --font-content: 'Inter', sans-serif;
  --canvas-width: 80.0rem;
}

html {
  background-color: var(--extension-background);
}

body {
  margin: auto;
  max-width: var(--canvas-width);
  background: var(--appeal-background);
  color: var(--text-color);
  font-family: var(--font-content);
}

header {
  background: none;
  display: block;
  padding: 0;
  text-align: left;
}

h1, h2, h3, h4, h5, h6 {
  margin: 1rem 0;
  line-height: 1.21;
}

h2 {
  color: var(--color-purple-20);
  font-weight: 200;
  font-size: 3.0rem;
}

h3 {
  font-weight: 600;
  font-size: 2.25rem;
}

h4 {
  font-weight: 600;
  font-size: 1.5rem;
}

#footer {
  background: var(--color-black);
}

#footer.container {
  height: 8.4375rem;
  max-width: 100%;
}


p {
  padding: 0;
  margin: 0;
  text-align: left;
  font-size: 1.5rem;
  line-height: 1.375;
}

.main-content {
  --main-content-gap: 6.25rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  gap: var(--main-content-gap);

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(68.19% 101.99% at 100.87% 101.99%, #EA7308 0%, rgba(234, 115, 8, 0) 100%),
    radial-gradient(100% 128% at 0% 0%, #AE55F7 0%, rgba(174, 85, 247, 0) 100%),
    radial-gradient(50% 128% at 0% 100%, #25A6A0 0%, rgba(163, 236, 227, 0) 100%),
    linear-gradient(0deg, rgba(23, 41, 78, 0.5), rgba(23, 41, 78, 0.5));
    // Figma produces some funky gradient css
    transform: scaleY(-1);
    opacity: 0.2;
    z-index: 0;
  }

  .sticky-nebula {
    top: 0;
    left: 0;
    position: fixed;
    background-image: url('/media/img/thunderbird/appeal/whatsnew-128/nebula-background.png');
    background-repeat: no-repeat;
    background-size: min(90%, 66.75rem);
    background-position: top center;
    width: 100%;
    height: 100%;
    z-index: 0;
  }
}

.pre-footer {
  padding-top: 6.25rem;
  padding-bottom: 3.75rem; // There's about 40 pixels of fake pre-footer after this
  display: flex;
  flex-direction: column;
  background-color: #0f1d36;
  background-image: url('/media/svg/appeal/whatsnew-128/blurred-blobs-bg.svg');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  z-index: 1;
  position: relative;
  min-height: 37.5rem;

  .additional-features {
    padding: 5.0rem 0;
  }

  .nebula-enables {
    max-width: 35.9375rem;

    p {
      font-size: 1.3125rem;
    }
  }

  section {
    margin: auto;
    max-width: 66.875rem;

    p {
      text-align: center;
    }
  }
}

.appeal-footer-container {
  // So z-index doesn't bleed
  position: relative;
  background-color: #0f1d36;
}

.two-column {
  display: flex;
  position: relative;

  width: 100%;
  gap: 5.9375rem;
  z-index: 1;
  max-width: 66.875rem;

  justify-content: center;
  align-items: start;

  .copy {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    gap: 1.8125rem;
  }

  h3 {
    margin-top: -2.5rem;
    display: flex;
    justify-content: space-between;
    align-items: end;
  }

  &.rust-revolution {
    width: 100%;
    margin-bottom: calc(-1 * var(--main-content-gap));

    .copy {
      max-width: 59%;
      margin-right: auto;
    }
  }

  &.flipped {
    flex-direction: row-reverse;
    @media (max-width: @lg) {
      flex-direction: column;
    }
  }
}

.right-wrap {
  margin: -28.5rem 0 -1rem auto;
  z-index: 1;

  img {
    max-width: 36.25rem;
    width: 100%;
    height: 100%;
  }
}

.three-column {
  display: flex;
  width: 100%;
  margin: auto;
  justify-content: center;
  gap: 1.875rem;
  box-sizing: border-box;

  .column {
    display: flex;
    flex-basis: 28%;

    .feature {
      position: relative;
      left: 1rem;
      height: 100%;
      flex-shrink: 0;
    }

    .content {
      display: flex;
      flex-direction: column;
    }

    p {
      font-size: 1.0rem;
      text-align: left;
    }
  }
}

.appeal-welcome {
  width: 100%;
  position: relative;
  text-align: left;
  margin-bottom: 1.875rem;

  .header-collection {
    display: flex;
    justify-content: space-between;
    gap: 4rem;
    width: 100%;
    overflow: hidden;
  }

  .space-things {
    margin-left: 0.9375rem;
    margin-right: -0.9375rem;
    padding-top: 2.5rem;
  }

  .screenshot-128 {
    padding-top: 3.625rem;
    margin: auto 0 auto auto;
    flex-shrink: 0;

    img, picture {
      max-height: 37.3125rem;
    }
  }

  .nebula-branding {
    margin-top: -23.5rem;
    margin-bottom: 2.5rem;
    overflow: hidden;
  }

  .welcome-message {
    position: relative;
    width: 35.8125rem;
    box-shadow: -0.5rem -0.75rem 1.125rem -0.4375rem rgba(0, 133, 255, 0.25), 0.5rem 1.5625rem 1.125rem -0.4375rem rgba(0, 133, 255, 0.32), 0px 0px 2.0rem 0px rgba(0, 0, 0, 0.50);

    padding: 2.1875rem 2.625rem;
    color: var(--color-white);
    margin-left: 4.25rem;
    margin-bottom: -3.4375rem;
    z-index: 1;

    header {
      color: var(--color-white);
      font-size: 2.125rem;
      font-weight: 600;
    }

  }

  .non-carousel-carousel {
    z-index: -1;
    bottom: 0;
    left: 0;
  }
}

.fast-and-fluid {
  gap: 0.625rem;
  width: 100%;
}

.with-frame {
  width: 32.4375rem;
  box-sizing: border-box;

  background: linear-gradient(#121420 0 0) padding-box,
  linear-gradient(131.3deg, #37ADF9 -4.69%, #AE55F7 106.58%) border-box;
  border-radius: 1.5rem;
  border: 0.125rem solid transparent;
  box-shadow: -0.5rem -0.5rem 1.16875rem -0.4375rem rgba(0, 133, 255, 0.25);
}


.picture.with-frame {
  display: flex;
  justify-content: center;
  justify-self: center;
  flex-shrink: 0;

  box-sizing: border-box;
  width: 30.75rem;
  padding: 2rem 1rem;

  img, picture {
    width: 100%;
  }
}

.accent-colors {
  .with-frame.picture {
    padding-right: 0;
  }
}


.picture.rocket {
  position: relative;
  min-height: 400px;
  height: 100%;
  width: 100%;

  background-image: url('/media/svg/appeal/whatsnew-128/fast-and-fluid-rocket.svg'), url('/media/svg/appeal/whatsnew-128/fast-and-fluid-stars.svg');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;

}

.page-separator-cover {
  position: relative;
  pointer-events: none;
  bottom: 0;
  width: 100%;
  line-height: 0;
  color: var(--color-black);
}

@media (max-width: @xl) {
  .two-column {
    margin: 0 1rem;

    &.rust-revolution {
      .copy {
        max-width: 80%
      }
    }
  }

  .right-wrap {
    margin-top: 0;
  }
}

@media (max-width: @lg) {
  .appeal-welcome {
    .nebula-branding {
      margin: 4.8125rem auto 4.375rem;
    }

    .space-things,
    .screenshot-128 {
      display: none;
    }

    .non-carousel-carousel {
      margin-top: 5rem;
    }

    .welcome-message {
      margin: auto;
      width: 80%;
    }
  }

  .two-column {
    flex-direction: column;

    .copy {
      max-width: 80%;
      margin: auto;
    }

    .with-frame {
      margin: auto;
      width: 80%;
    }

    // Reset the margin-left so it's centre aligned
    // Same for rust-revolution
    &.fast-and-fluid {
      .copy {
        margin-left: auto;
      }
    }

    &.rust-revolution {
      .copy {
        margin-left: auto;
      }


    }
  }

  .right-wrap {
    margin-bottom: -6rem;
  }

  .three-column {
    flex-direction: column;

    .content {
      flex-basis: 80%;
    }

    .feature {
      top: -1rem;
    }
  }

  .pre-footer {
    padding-left: 1rem;
    padding-right: 1rem;
    background-image: url('/media/svg/appeal/whatsnew-128/blurred-blobs-bg-90deg.svg');

    .additional-features {
      padding: 5rem 1rem;
    }
  }

  #footer {
    min-height: 16rem;
  }
}

@media (max-width: @sm) {
  .appeal-welcome .welcome-message {
    width: 100%;
  }

  .two-column .with-frame {
    width: 100%;
  }

  .three-column {
    .column {
      flex-direction: column;

      .feature {
        left: 0;
        top: 0;
        text-align: center;
      }

      .content, p {
        text-align: center;
      }
    }
  }

  .pre-footer {
    .additional-features {
      padding: 5rem 0;
    }
  }

}