:root {
  --color-red-10: #fee2e2;
  --color-red-20: #fecaca;
  --color-red-30: #fca5a5;
  --color-red-40: #f87171;
  --color-red-50: #ef4444;
  --color-red-60: #dc2626;
  --color-red-70: #b91c1c;
  --color-red-80: #991b1b;
  --color-red-90: #7f1d1d;

  --color-orange-10: #ffedd5;
  --color-orange-20: #fed7aa;
  --color-orange-30: #fdba74;
  --color-orange-40: #fb923c;
  --color-orange-50: #f97316;
  --color-orange-60: #ea580c;
  --color-orange-70: #c2410c;
  --color-orange-80: #9a3412;
  --color-orange-90: #7c2d12;

  --color-amber-10: #fef3c7;
  --color-amber-20: #fde68a;
  --color-amber-30: #fcd34d;
  --color-amber-40: #fbbf24;
  --color-amber-50: #f59e0b;
  --color-amber-60: #d97706;
  --color-amber-70: #b45309;
  --color-amber-80: #92400e;
  --color-amber-90: #78350f;

  --color-yellow-10: #fef9c3;
  --color-yellow-20: #fef08a;
  --color-yellow-30: #fde047;
  --color-yellow-40: #facc15;
  --color-yellow-50: #eab308;
  --color-yellow-60: #ca8a04;
  --color-yellow-70: #a16207;
  --color-yellow-80: #854d0e;
  --color-yellow-90: #713f12;

  --color-green-10: #dcfce7;
  --color-green-20: #bbf7d0;
  --color-green-30: #86efac;
  --color-green-40: #4ade80;
  --color-green-50: #22c55e;
  --color-green-60: #16a34a;
  --color-green-70: #15803d;
  --color-green-80: #166534;
  --color-green-90: #14532d;

  --color-teal-10: #cdfaf7;
  --color-teal-20: #9ff4f0;
  --color-teal-30: #62e9e6;
  --color-teal-40: #27d3d6;
  --color-teal-50: #0db7bd;
  --color-teal-60: #0a929d;
  --color-teal-70: #0e757f;
  --color-teal-80: #135e67;
  --color-teal-90: #144e56;

  --color-blue-10: #ddeefe;
  --color-blue-20: #bce0fd;
  --color-blue-30: #88ccfc;
  --color-blue-40: #4cb1f9;
  --color-blue-50: #2493ef;
  --color-blue-60: #1373d9;
  --color-blue-70: #105bbc;
  --color-blue-80: #124c9a;
  --color-blue-90: #15427c;

  --color-purple-10: #f3e8ff;
  --color-purple-20: #e9d5ff;
  --color-purple-30: #d8b4fe;
  --color-purple-40: #c084fc;
  --color-purple-50: #a855f7;
  --color-purple-60: #9333ea;
  --color-purple-70: #7e22ce;
  --color-purple-80: #6b21a8;
  --color-purple-90: #581c87;

  --color-magenta-10: #fbe7f9;
  --color-magenta-20: #f8cff3;
  --color-magenta-30: #f4a9e8;
  --color-magenta-40: #ee75d7;
  --color-magenta-50: #e247c4;
  --color-magenta-60: #cd26a5;
  --color-magenta-70: #b01a86;
  --color-magenta-80: #91186e;
  --color-magenta-90: #79195c;

  --color-brown-10: #f4e9d7;
  --color-brown-20: #efdfc4;
  --color-brown-30: #e4cdab;
  --color-brown-40: #d7bc96;
  --color-brown-50: #b6986c;
  --color-brown-60: #96764b;
  --color-brown-70: #755b38;
  --color-brown-80: #51412c;
  --color-brown-90: #47341f;

  --color-gray-05: #fafafa;
  --color-gray-10: #f4f4f5;
  --color-gray-20: #e4e4e7;
  --color-gray-30: #d4d4d8;
  --color-gray-40: #a1a1aa;
  --color-gray-50: #71717a;
  --color-gray-60: #52525b;
  --color-gray-70: #3f3f46;
  --color-gray-80: #27272a;
  --color-gray-90: #18181b;

  --color-ink-10: #f1f3fa;
  --color-ink-20: #e3e5f2;
  --color-ink-30: #cdd0e5;
  --color-ink-40: #9b9ec2;
  --color-ink-50: #6e6f9b;
  --color-ink-60: #52507c;
  --color-ink-70: #3e3c67;
  --color-ink-80: #2a284b;
  --color-ink-90: #1a1838;

  --color-white: #ffffff;
  --color-black: #000000;

  --bg: #31293e;
  --txt: var(--color-gray-05);

  --max-width: 1280px;
  --slider: 50%;

  --icon-glow: var(--color-gray-50);
  --info-header: var(--color-gray-20);
  --info-txt: var(--color-gray-10);

  --button-background-color: var(--color-gray-20);
  --button-hover-text-color: var(--color-gray-70);
  --button-hover-background-color: var(--color-gray-10);
  --button-active-background-color: var(--color-gray-30);
  --button-border-color: var(--color-gray-40);
  --button-border-radius: 6px;
  --button-border-size: 2px;
  --button-text-color: var(--color-gray-70);
  --button-margin: 9px;
  --button-padding: 9px;
  --focus-outline-color: var(--color-blue-50);
  --focus-outline-offset: 4px;
  --focus-outline: 4px solid var(--focus-outline-color);
  --icon-size: 32px;
}

body {
  padding: 0;
  margin: 0;
  font-size: 1rem;
  background-color: var(--bg);
  color: var(--txt);
  background-image: url("/media/archived/img/thunderbird/backgrounds/blob.png");
  background-repeat: no-repeat;
  background-position: center -300px;
  background-size: 80vw;
}

header {
  --max-width: 85ch;
  padding-inline: 16px;
  padding-block: 50px 400px;
}

header h1 {
  font-weight: 100;
  font-size: 2.5rem;
  text-shadow: 0 2px 6px var(--color-ink-90);
  position: relative;
  display: flex;
  flex-direction: column;
  line-height: 2em;
  margin-block: 30px 0;
}

.tb-title {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 4rem;
}

.version,
.alive {
  color: transparent;
  font-weight: 900;
  line-height: 0;
  text-shadow: none;
}

.version {
  font-size: 10rem;
  background: linear-gradient(30deg, transparent 25%, var(--color-purple-20));
  -webkit-background-clip: text;
  background-clip: text;
  line-height: 1;
  align-self: end;
  margin-block-start: -9rem;
}

.supernova {
  filter: drop-shadow(0 2px 6px var(--color-ink-90));
}

.whats-new-section *,
.container * {
  box-sizing: border-box;
}

.whats-new-section {
  display: grid;
  place-items: center;
  min-height: 30rem;
  gap: 24px;
  margin-block-end: 128px;
}

.whats-new-section:first-of-type {
  margin-block-start: -350px;
}

img {
  max-width: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: var(--max-width);
  margin: 0 auto;
}

.split {
  flex-direction: row;
}

.split.reverse {
  flex-direction: row-reverse;
}

.split > .info {
  flex-basis: calc(66% - 12px);
}

.split > .image {
  flex-basis: calc(33% - 12px);
}

.info {
  padding: 64px 32px;
}

.slider-box {
  background-image: url("/media/archived/img/thunderbird/whatsnew/115/new-old-fade.png");
  background-size: auto 95%;
  background-position: center;
  background-repeat: no-repeat;
  aspect-ratio: 16 / 10;
  width: 100%;
  position: relative;
  isolation: isolate;
}

#before,
#after {
  background-size: auto 100%;
  background-position: left center;
  background-repeat: no-repeat;
  width: 100%;
  height: 100%;
  position: absolute;
}

#before {
  background-image: url("/media/archived/img/thunderbird/whatsnew/115/102.png");
  z-index: 0;
}

#after {
  background-image: url("/media/archived/img/thunderbird/whatsnew/115/115.png");
  z-index: 1;
  width: calc(var(--slider) - 9px);
}

.slider {
  position: absolute;
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 100%;
  background: transparent;
  color: transparent;
  outline: none;
  margin: 0;
  z-index: 2;
}

input[type="range"]::-moz-range-thumb,
input[type="range"]::-webkit-range-thumb {
  -webkit-appearance: none;
  appearance: none;
  accent-color: transparent;
  color: transparent;
  width: 6px;
  height: 105%;
  border: none;
  background: transparent;
  cursor: pointer;
  opacity: 0;
}

.slider-bar {
  pointer-events: none;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  background-image: linear-gradient(
    to bottom,
    transparent,
    var(--color-magenta-90) 20%,
    var(--color-purple-90) 80%,
    transparent
  );
  border: none;
  width: 6px;
  height: 105%;
  inset-inline-start: calc(var(--slider) - 9px);
}

.slider-tab {
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  margin-inline: -29px;
  margin-block-end: 32px;
  width: 32px;
  height: 32px;
  border: 1px solid rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  background-color: var(--color-ink-80);
  background-image: linear-gradient(
    to bottom,
    var(--color-ink-70),
    var(--color-ink-90)
  );
  z-index: 3;
}

.slider-tab::before,
.slider-tab::after {
  content: "";
  padding: 4px;
  margin-inline: 1px;
  display: inline-block;
  border: solid var(--color-ink-20);
  border-radius: 1px;
  border-width: 0 1px 1px 0;
}

.slider-tab::before {
  transform: rotate(135deg);
}

.slider-tab::after {
  transform: rotate(-45deg);
}

.caption-ln {
  margin-block: 9px;
  color: currentColor;
  text-decoration-color: currentColor;
  text-underline-offset: 0.25em;
  text-decoration-thickness: .12em;
  text-decoration-style: dotted;
  transition: font-size .2s,
              text-decoration-color .2s;
}

.caption-ln.donate,
.caption-ln:hover,
.caption-ln:hover:visited {
  text-decoration-color: var(--color-blue-50);
}

.caption-ln:visited {
  text-decoration-color: var(--color-purple-50);
}

.info p {
  color: var(--info-txt);
  font-weight: 500;
  font-size: 1.8rem;
  margin-block: 0.5em 1em;
  line-height: 1.35em;
  padding-inline-start: 44px;
}

.info .header {
  color: var(--info-header);
  display: flex;
  gap: 12px;
}

.info .header .icon svg {
  height: 32px;
  width: 32px;
}

.info .header .icon {
  color: var(--color-ink-20);
  position: relative;
  width: 32px;
  height: 32px;
}

.info .header .icon::before {
  content: "";
  position: absolute;
  inset-block-start: -6px;
  inset-inline-start: -6px;
  background: var(--icon-glow);
  border-radius: 24px;
  height: 48px;
  width: 48px;
  filter: blur(24px);
  opacity: 0.5;
  z-index: -1;
}

.info .header h3 {
  font-weight: 600;
  font-size: 2rem;
  margin: 0;
}

.image {
  display: flex;
  flex-direction: column;
  place-items: center;
  height: 100%;
}

.image.split {
  flex-direction: row;
}

.image.split > div {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-basis: calc(50% - 12px);
}

.nebula {
  height: 0;
  position: relative;
  z-index: -1;
}

.nebula img {
  filter: blur(2px);
  opacity: 0.5;
  max-width: 60%;
  position: absolute;
  inset-block-end: 0;
}

.nebula:nth-of-type(even) img {
  transform: rotate(180deg);
  right: 0;
}

.nebula:nth-of-type(odd) img {
  left: 0;
}

.utb {
  --icon-glow: var(--color-purple-50);
  --info-header: var(--color-purple-20);
  --info-txt: var(--color-purple-10);
}

.iconography {
  --icon-glow: var(--color-teal-50);
  --info-header: var(--color-teal-20);
  --info-txt: var(--color-teal-10);
}

.density,
.accessability {
  --icon-glow: var(--color-blue-50);
  --info-header: var(--color-blue-20);
  --info-txt: var(--color-blue-10);
  overflow-x: auto;
}

.appmenu {
  --icon-glow: var(--color-green-50);
  --info-header: var(--color-green-20);
  --info-txt: var(--color-green-10);
}

.folderpane {
  --icon-glow: var(--color-magenta-50);
  --info-header: var(--color-magenta-20);
  --info-txt: var(--color-magenta-10);
}

.tags {
  --icon-glow: var(--color-orange-50);
  --info-header: var(--color-orange-20);
  --info-txt: var(--color-orange-10);
}

.cardsview {
  --icon-glow: var(--color-ink-50);
  --info-header: var(--color-ink-20);
  --info-txt: var(--color-ink-10);
}

.ab {
  --icon-glow: var(--color-brown-50);
  --info-header: var(--color-brown-20);
  --info-txt: var(--color-brown-10);
}

.calendar {
  --icon-glow: var(--color-red-50);
  --info-header: var(--color-red-20);
  --info-txt: var(--color-red-10);
}

.density-window {
  flex-direction: column;
  gap: 24px;
}

.density-cards img {
  margin-inline-start: -30%;
  margin-block-start: 5%;
  align-self: start;
  z-index: 2;
}

.density-cards img:first-child {
  margin-inline-start: 0;
  margin-block-start: 10%;
  z-index: 3;
}

.density-cards img:last-child {
  margin-block-start: 0;
  z-index: 1;
}

.toolbar {
  display: flex;
  flex-wrap: wrap;
  font-size: 1.25rem;
  background: transparent;
  background-image: linear-gradient(
    to left,
    transparent,
    var(--color-gray-30) 10%,
    var(--color-gray-30) 90%,
    transparent
  );
  padding-inline: 6rem;
  padding-block: 9px;
  max-width: 100%;
}

.more h3 {
  text-align: center;
  font-size: 3rem;
  font-weight: 300;
}

.button {
  -webkit-appearance: none;
  appearance: none;
  background-color: var(--button-background-color);
  color: var(--button-text-color);
  border: var(--button-border-size) solid var(--button-border-color);
  border-radius: var(--button-border-radius);
  padding: var(--button-padding);
  margin: var(--button-margin);
  min-width: 6em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: inherit;
}

.button:hover {
  color: var(--button-hover-text-color);
  background-color: var(--button-hover-background-color);
  border-color: var(--button-border-color);
}

.button:focus-visible,
.button.focused {
  outline: var(--focus-outline);
  outline-offset: var(--focus-outline-offset);
}

.button:hover:active {
  background-color: var(--button-active-background-color);
  border-color: var(--button-border-color);
}

.button.icon-only {
  --button-background-color: var(--color-gray-05);
  --button-padding: 9px;
  padding: var(--button-padding);
  min-width: auto;
  aspect-ratio: 1;
}

.button.icon-only > svg {
  height: var(--icon-size);
  width: var(--icon-size);
}

.button-group .button + .button:not(:last-child).icon-only.selected,
.button.icon-only.selected {
  background-color: var(--color-blue-50);
  color: var(--color-white);
  border: var(--button-border-size) solid var(--color-blue-70);
  outline: var(--button-border-size) solid solid var(--color-blue-70);
  z-index: 4;
}

.button-group {
  display: inline-flex;
  color: var(--button-text-color);
  border: var(--button-border-size) solid var(--button-border-color);
  border-radius: var(--button-border-radius);
  margin: var(--button-margin);
  position: relative;
  isolation: isolate;
  z-index: 1;
}

.button-group .button + .button:not(:last-child) {
  border-inline-end: var(--button-border-size) solid var(--button-border-color);
}

.button-group :is(.button) {
  --button-margin: 0;
  border: none;
  border-radius: 0;
  z-index: 2;
}

.button-group .button:focus-visible {
  outline-offset: 0;
  z-index: 3;
}

.button-group .button:first-child {
  border-inline-end: var(--button-border-size) solid var(--button-border-color);
  border-start-start-radius: calc(var(--button-border-radius) - var(--button-border-size));
  border-end-start-radius: calc(var(--button-border-radius) - var(--button-border-size));
}

.button-group .button:last-child {
  border-start-end-radius: calc(var(--button-border-radius) - var(--button-border-size));
  border-end-end-radius: calc(var(--button-border-radius) - var(--button-border-size));
}

.keyboard-keys {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
  gap: 24px;
  font-size: 2rem;
  margin-block: 48px;
}

.key {
  background-color: var(--color-ink-80);
  color: var(--color-ink-10);
  border: var(--button-border-size) solid var(--color-ink-90);
  border-radius: var(--button-border-radius);
  box-shadow: 0 6px 0 var(--color-ink-90);
  padding-inline: calc(var(--button-padding) * 2);
  padding-block: var(--button-padding);
  margin: var(--button-margin);
}

.key:hover {
  background-color: var(--color-ink-70);
  border: var(--button-border-size) solid var(--color-ink-90);
  box-shadow: 0 8px 0 var(--color-ink-90);
}

.key:focus-visible {
  outline: var(--focus-outline);
  outline-offset: var(--focus-outline-offset);
}

.key:hover:active,
.key.pressed {
  background-color: var(--color-ink-90);
  border: var(--button-border-size) solid var(--color-black);
  box-shadow: none;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/*------------------------*/
/* Donation Appeal Styles */
/*------------------------*/
#initAppeal {
  margin-block-end: 30px;
}

.logo {
  height: 250px;
  width: 250px;
  margin: 0 auto;
  margin-block-end: -80px;
}

.alive {
  font-size: 8rem;
  background: linear-gradient(170deg, transparent, var(--color-purple-20) 60%);
  -webkit-background-clip: text;
  background-clip: text;
  line-height: 1;
  margin-block-start: -30px;
  margin-inline-start: 1.25em;
}

.appeal-container {
  padding: 1em;
  margin: 0 auto;
  margin-block-start: -300px;
  max-width: 800px;
  position: relative;
}

.donate-banner {
  background: var(--color-purple-90);
  background-image: linear-gradient(60deg, var(--color-ink-90) 10%, var(--bg));
  background-position: top left, center;
  background-size: contain;
  background-repeat: no-repeat;
  border-radius: 6px;
  box-shadow: 4px 4px 24px rgba(0, 0, 0, 0.15);
  color: white;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  font-size: 1.1rem;
  line-height: 1.1;
  margin: 0 auto;
  margin-top: -114px;
  text-decoration: none;
  transition: transform 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
  max-width: 430px;
  min-height: 120px;
  z-index: 10;
}

.donate-banner::after {
  content: "";
  position: absolute;
  top: -210px;
  left: -240px;
  height: 420px;
  width: 420px;
  background-color: var(--bg);
  background-image: linear-gradient(60deg, #4f3b64 75%, var(--color-ink-70));
  transform: rotate(36deg);
  transition: transform 0.3s ease-in-out;
}

.donate-banner:hover {
  transform: scale(1.05);
}

.donate-banner:hover::after {
  transform: scale(3);
}

.donate-banner:hover #decoration,
.donate-banner:hover #donate-banner-right {
  opacity: 0;
}

.donate-banner:hover #donate-banner-left {
  transform: translate(24px, 6px);
}

#donate-banner-left,
#donate-banner-right {
  flex: 1;
  padding: 15px;
  transition: all 0.5s ease-in-out;
  z-index: 2;
}

#donate-banner-right {
  place-self: end;
  padding-inline-start: 3px;
}

#donate-banner-left > strong {
  font-size: 1.7rem;
}

#donate-banner-right > strong {
  font-size: 1.3rem;
  font-weight: 400;
}

#decoration > #pill-1 {
  animation: 20s infinite pill-float linear;
  animation-delay: 0.2s;
}

#decoration > #pill-2 {
  animation: 30s infinite pill-float linear;
}

#decoration > #pill-3 {
  animation: 35s infinite pill-float linear;
  animation-delay: 0.1s;
}

#decoration > #pill-4 {
  animation: 25s infinite pill-float linear;
  animation-delay: 0.3s;
}

#decoration > #pill-5 {
  animation: 40s infinite pill-float linear;
  animation-delay: 0.4s;
}

#decoration > #pill-6 {
  animation: 30s infinite pill-float linear;
  animation-delay: 0.5s;
}

#decoration > #pill-7 {
  animation: 25s infinite pill-float linear;
  animation-delay: 0.6s;
}

#decoration > #pill-8 {
  animation: 20s infinite pill-float linear;
  animation-delay: 0.7s;
}

#decoration > #pill-9 {
  animation: 40s infinite pill-float linear;
  animation-delay: 0.8s;
}

#decoration > #pill-10 {
  animation: 35s infinite pill-float linear;
  animation-delay: 0.4s;
}

#decoration {
  position: absolute;
  left: -59px;
  display: grid;
  gap: 3px;
  grid-template-columns: repeat(20, 1fr);
  grid-template-rows: repeat(10, 1fr);
  place-items: stretch;
  height: 90px;
  width: 500px;
  transform: rotate(-55deg);
  transition: opacity 0.5s ease-in-out;
  z-index: 1;
}

.pill {
  display: block;
  background: linear-gradient(to left, var(--color-magenta-20), transparent);
  opacity: 0.75;
  border-radius: 1000px;
  transform: translateX(-250px);
}

@keyframes pill-float {
  from {
    transform: translateX(-250px);
  }

  to {
    transform: translateX(250px);
  }
}

#pill-1 {
  grid-column: 4 / span 8;
  grid-row: 5 / span 3;
}

#pill-2 {
  grid-column: 10 / span 4;
  grid-row: 6 / span 1;
}

#pill-3 {
  grid-column: 8 / span 3;
  grid-row: 3 / span 1;
}

#pill-4 {
  grid-column: 4 / span 3;
  grid-row: 3 / span 1;
}

#pill-5 {
  grid-column: 6 / span 4;
  grid-row: 9 / span 1;
}

#pill-6 {
  grid-column: 11 / span 1;
  grid-row: 9 / span 1;
}

#pill-7 {
  grid-column: 13 / span 2;
  grid-row: 10 / span 1;
}

#pill-8 {
  grid-column: 13 / span 3;
  grid-row: 8 / span 1;
}

#pill-9 {
  grid-column: 14 / span 3;
  grid-row: 4 / span 1;
}

#pill-10 {
  grid-column: 12 / span 1;
  grid-row: 4 / span 1;
}

#hover-hearts {
  position: absolute;
  inset-block-end: 12px;
  inset-inline-end: 13px;
  display: none;
  gap: 3px;
  z-index: 1;
}

#hover-heart-1,
#hover-heart-2,
#hover-heart-3 {
  display: flex;
  align-items: end;
  opacity: 0;
}

#hover-heart-1 > svg {
  animation-delay: 0.6s;
}

#hover-heart-3 {
  animation-delay: 0.8s;
}

#hover-heart-1 > svg {
  transform: rotate(-45deg);
  width: 32px;
  height: 32px;
}

#hover-heart-2 > svg {
  width: 64px;
  height: 64px;
  transform: translateY(-9px);
}

#hover-heart-3 > svg {
  transform: rotate(45deg);
  width: 32px;
  height: 32px;
}

.donate-banner:hover #hover-hearts {
  display: flex;
}

.donate-banner:hover #hover-hearts > #hover-heart-1 {
  animation: 1s heart-fade forwards ease-in-out;
  animation-delay: 0.1s;
}

.donate-banner:hover #hover-hearts > #hover-heart-2 {
  animation: 1s heart-fade forwards ease-in-out;
}

.donate-banner:hover #hover-hearts > #hover-heart-3 {
  animation: 1s heart-fade forwards ease-in-out;
  animation-delay: 0.2s;
}

@keyframes heart-fade {
  from {
    opacity: 0;
    transform: translateY(12px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.letter-container {
  padding-inline: 60px;
  padding-block: 90px 60px;
  background: color-mix(in srgb, var(--bg) 95%, transparent);
  border-radius: 6px;
  position: relative;
  margin-block: -50px 121px;
}

.letter-container:before {
  content: "";
  position: absolute;
  background: var(--color-blue-50);
  background-image: linear-gradient(
    127deg,
    var(--color-teal-50),
    var(--color-magenta-50)
  );
  inset: -4px;
  border-radius: 12px;
  filter: blur(64px);
  opacity: 0.8;
  z-index: -1;
}

.heart-container {
  color: var(--color-purple-20);
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 9px;
  align-items: start;
  margin-top: 2em;
  width: 100%;
}

.heart-container svg {
  height: 32px;
  width: 32px;
}

.line {
  display: block;
  height: 1px;
  width: 100%;
  border-top: 1px solid var(--color-blue-20);
}

.left-lines,
.right-lines {
  display: flex;
  flex-direction: column;
  gap: 6px;
  justify-self: stretch;
  margin-block-start: 8px;
}

.left-lines {
  align-items: end;
}

.left-lines > .line:nth-child(2) {
  width: 50%;
  margin-right: 3px;
  border-top: 1px solid var(--color-purple-20);
}

.left-lines > .line:nth-child(1) {
  width: 30%;
  border-top: 1px solid var(--color-magenta-20);
}

.right-lines > .line:nth-child(2) {
  width: 50%;
  margin-left: 3px;
  border-top: 1px solid var(--color-purple-20);
}

.right-lines > .line:nth-child(1) {
  width: 30%;
  border-top: 1px solid var(--color-magenta-20);
}

.team-text {
  background: linear-gradient(
    to left,
    var(--color-green-20),
    var(--color-blue-20)
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

@media only screen and (max-width: 1024px) {
  :root {
    --icon-size: 16px;
    --button-border-radius: 3px;
    --button-border-size: 1px;
    --button-margin: 3px;
    --button-padding: 3px;
    --focus-outline-offset: 2px;
    --focus-outline: 2px solid var(--focus-outline-color);
  }

  .split,
  .split.reverse {
    flex-direction: column;
  }

  .split > .info,
  .split > .image,
  .image.split > div {
    flex-basis: auto;
  }

  .image.split {
    flex-direction: column;
    gap: 64px;
  }

  .toolbar {
    font-size: 1rem;
  }

  .keyboard-keys {
    font-size: 1.75rem;
  }
}

@media only screen and (max-width: 680px) {
  header h1 {
    font-size: 2rem;
  }

  .tb-title {
    font-size: 3rem;
  }

  .version {
    font-size: 8rem;
  }

  .alive {
    font-size: 6rem;
  }
}

@media only screen and (max-width: 480px) {
  header h1 {
    font-size: 1.75rem;
  }

  .tb-title {
    font-size: 2rem;
  }

  .version {
    font-size: 6rem;
  }

  .alive {
    font-size: 4rem;
  }
}
