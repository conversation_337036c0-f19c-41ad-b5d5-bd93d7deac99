/* Bug 991516 */

@font-face {
    font-family: X-LocaleSpecific-Light;
    font-weight: normal;
    src: url('/media/fonts/l10n/az/mplus-2c-light-az-subset.woff') format('woff');
}

@font-face {
    font-family: X-LocaleSpecific-Light;
    font-weight: bold;
    src: url('/media/fonts/l10n/az/mplus-2c-medium-az-subset.woff') format('woff');
}

@font-face {
    font-family: X-LocaleSpecific;
    font-weight: normal;
    src: url('/media/fonts/l10n/az/mplus-2c-regular-az-subset.woff') format('woff');
}

@font-face {
    font-family: X-LocaleSpecific;
    font-weight: bold;
    src: url('/media/fonts/l10n/az/mplus-2c-bold-az-subset.woff') format('woff');
}

@font-face {
    font-family: X-LocaleSpecific-Extrabold;
    font-weight: 800;
    src: url('/media/fonts/l10n/az/mplus-2c-black-az-subset.woff') format('woff');
}

/* Bug 1174423 */

* {
    /* !important required for locale specific override */
    font-variant: normal !important; /* stylelint-disable-line declaration-no-important */
}
