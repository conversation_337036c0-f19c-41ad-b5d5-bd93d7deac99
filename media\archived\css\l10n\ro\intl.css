/* Bug 907793, 1263732 */

@font-face {
    font-family: X-LocaleSpecific-Light;
    font-weight: normal;
    font-style: normal;
    src: url('/media/fonts/l10n/ro/opensans-light-ro-subset.woff2') format('woff2'),
         url('/media/fonts/l10n/ro/opensans-light-ro-subset.woff') format('woff');
}

@font-face {
    font-family: X-LocaleSpecific-Light;
    font-weight: bold;
    font-style: normal;
    src: url('/media/fonts/l10n/ro/opensans-semibold-ro-subset.woff2') format('woff2'),
         url('/media/fonts/l10n/ro/opensans-semibold-ro-subset.woff') format('woff');
}

@font-face {
    font-family: X-LocaleSpecific-Light;
    font-weight: normal;
    font-style: italic;
    src: url('/media/fonts/l10n/ro/opensans-lightitalic-ro-subset.woff2') format('woff2'),
         url('/media/fonts/l10n/ro/opensans-lightitalic-ro-subset.woff') format('woff');
}

@font-face {
    font-family: X-LocaleSpecific;
    font-weight: normal;
    font-style: normal;
    src: url('/media/fonts/l10n/ro/opensans-regular-ro-subset.woff2') format('woff2'),
         url('/media/fonts/l10n/ro/opensans-regular-ro-subset.woff') format('woff');
}

@font-face {
    font-family: X-LocaleSpecific;
    font-weight: bold;
    font-style: normal;
    src: url('/media/fonts/l10n/ro/opensans-bold-ro-subset.woff2') format('woff2'),
         url('/media/fonts/l10n/ro/opensans-bold-ro-subset.woff') format('woff');
}

@font-face {
    font-family: X-LocaleSpecific;
    font-weight: normal;
    font-style: italic;
    src: url('/media/fonts/l10n/ro/opensans-italic-ro-subset.woff2') format('woff2'),
         url('/media/fonts/l10n/ro/opensans-italic-ro-subset.woff') format('woff');
}

@font-face {
    font-family: X-LocaleSpecific;
    font-weight: bold;
    font-style: italic;
    src: url('/media/fonts/l10n/ro/opensans-bolditalic-ro-subset.woff2') format('woff2'),
         url('/media/fonts/l10n/ro/opensans-bolditalic-ro-subset.woff') format('woff');
}

@font-face {
    font-family: X-LocaleSpecific-Extrabold;
    font-weight: 800;
    font-style: normal;
    src: url('/media/fonts/l10n/ro/opensans-extrabold-ro-subset.woff2') format('woff2'),
         url('/media/fonts/l10n/ro/opensans-extrabold-ro-subset.woff') format('woff');
}

@font-face {
    font-family: X-LocaleSpecific-Extrabold;
    font-weight: 800;
    font-style: italic;
    src: url('/media/fonts/l10n/ro/opensans-extrabolditalic-ro-subset.woff2') format('woff2'),
         url('/media/fonts/l10n/ro/opensans-extrabolditalic-ro-subset.woff') format('woff');
}
