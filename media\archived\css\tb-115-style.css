/* - Metropolis -*/
@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Regular.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-Regular.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-RegularItalic.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-RegularItalic.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ExtraLight.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-ExtraLight.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Light.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-Light.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Thin.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-Thin.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 100;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ExtraLightItalic.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-ExtraLightItalic.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 200;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-LightItalic.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-LightItalic.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ThinItalic.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-ThinItalic.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Medium.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-Medium.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-SemiBold.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-SemiBold.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Bold.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-Bold.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-BoldItalic.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-BoldItalic.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 500;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-MediumItalic.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-MediumItalic.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-SemiBoldItalic.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-SemiBoldItalic.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ExtraBold.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-ExtraBold.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-ExtraBoldItalic.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-ExtraBoldItalic.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-Black.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-Black.woff") format("woff");
}
@font-face {
  font-family: metropolis;
  font-style: italic;
  font-weight: 900;
  font-display: swap;
  src: url("/media/fonts/Metropolis/Metropolis-BlackItalic.woff2") format("woff2"), url("/media/fonts/Metropolis/Metropolis-BlackItalic.woff") format("woff");
}
/* - Inter -*/
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Thin.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-Thin.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 100;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-ThinItalic.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-ThinItalic.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-ExtraLight.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-ExtraLight.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 200;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-ExtraLightItalic.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-ExtraLightItalic.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Light.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-Light.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-LightItalic.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-LightItalic.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Regular.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-Regular.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Italic.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-Italic.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Medium.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-Medium.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 500;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-MediumItalic.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-MediumItalic.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-SemiBold.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-SemiBold.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-SemiBoldItalic.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-SemiBoldItalic.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Bold.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-Bold.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-BoldItalic.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-BoldItalic.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-ExtraBold.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-ExtraBold.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-ExtraBoldItalic.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-ExtraBoldItalic.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-Black.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-Black.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 900;
  font-display: swap;
  src: url("/media/fonts/Inter/Inter-BlackItalic.woff2?v=3.19") format("woff2"), url("/media/fonts/Inter/Inter-BlackItalic.woff?v=3.19") format("woff");
}
@font-face {
  font-family: 'Inter var';
  font-weight: 100 900;
  font-display: swap;
  font-style: normal;
  font-named-instance: 'Regular';
  src: url("/media/fonts/Inter/Inter-roman.var.woff2?v=3.19") format("woff2");
}
@font-face {
  font-family: 'Inter var';
  font-weight: 100 900;
  font-display: swap;
  font-style: italic;
  font-named-instance: 'Italic';
  src: url("/media/fonts/Inter/Inter-italic.var.woff2?v=3.19") format("woff2");
}
:root {
  --color-red-10: #fee2e2;
  --color-red-20: #fecaca;
  --color-red-30: #fca5a5;
  --color-red-40: #f87171;
  --color-red-50: #ef4444;
  --color-red-60: #dc2626;
  --color-red-70: #b91c1c;
  --color-red-80: #991b1b;
  --color-red-90: #7f1d1d;
  --color-orange-10: #ffedd5;
  --color-orange-20: #fed7aa;
  --color-orange-30: #fdba74;
  --color-orange-40: #fb923c;
  --color-orange-50: #f97316;
  --color-orange-60: #ea580c;
  --color-orange-70: #c2410c;
  --color-orange-80: #9a3412;
  --color-orange-90: #7c2d12;
  --color-amber-10: #fef3c7;
  --color-amber-20: #fde68a;
  --color-amber-30: #fcd34d;
  --color-amber-40: #fbbf24;
  --color-amber-50: #f59e0b;
  --color-amber-60: #d97706;
  --color-amber-70: #b45309;
  --color-amber-80: #92400e;
  --color-amber-90: #78350f;
  --color-yellow-10: #fef9c3;
  --color-yellow-20: #fef08a;
  --color-yellow-30: #fde047;
  --color-yellow-40: #facc15;
  --color-yellow-50: #eab308;
  --color-yellow-60: #ca8a04;
  --color-yellow-70: #a16207;
  --color-yellow-80: #854d0e;
  --color-yellow-90: #713f12;
  --color-green-10: #dcfce7;
  --color-green-20: #bbf7d0;
  --color-green-30: #86efac;
  --color-green-40: #4ade80;
  --color-green-50: #22c55e;
  --color-green-60: #16a34a;
  --color-green-70: #15803d;
  --color-green-80: #166534;
  --color-green-90: #14532d;
  --color-teal-10: #cdfaf7;
  --color-teal-20: #9ff4f0;
  --color-teal-30: #62e9e6;
  --color-teal-40: #27d3d6;
  --color-teal-50: #0db7bd;
  --color-teal-60: #0a929d;
  --color-teal-70: #0e757f;
  --color-teal-80: #135e67;
  --color-teal-90: #144e56;
  --color-blue-10: #ddeefe;
  --color-blue-20: #bce0fd;
  --color-blue-30: #88ccfc;
  --color-blue-40: #4cb1f9;
  --color-blue-50: #2493ef;
  --color-blue-60: #1373d9;
  --color-blue-70: #105bbc;
  --color-blue-80: #124c9a;
  --color-blue-90: #15427c;
  --color-purple-10: #f3e8ff;
  --color-purple-20: #e9d5ff;
  --color-purple-30: #d8b4fe;
  --color-purple-40: #c084fc;
  --color-purple-50: #a855f7;
  --color-purple-60: #9333ea;
  --color-purple-70: #7e22ce;
  --color-purple-80: #6b21a8;
  --color-purple-90: #581c87;
  --color-magenta-10: #fbe7f9;
  --color-magenta-20: #f8cff3;
  --color-magenta-30: #f4a9e8;
  --color-magenta-40: #ee75d7;
  --color-magenta-50: #e247c4;
  --color-magenta-60: #cd26a5;
  --color-magenta-70: #b01a86;
  --color-magenta-80: #91186e;
  --color-magenta-90: #79195c;
  --color-brown-10: #f4e9d7;
  --color-brown-20: #efdfc4;
  --color-brown-30: #e4cdab;
  --color-brown-40: #d7bc96;
  --color-brown-50: #b6986c;
  --color-brown-60: #96764b;
  --color-brown-70: #755b38;
  --color-brown-80: #51412c;
  --color-brown-90: #47341f;
  --color-gray-05: #fafafa;
  --color-gray-10: #f4f4f5;
  --color-gray-20: #e4e4e7;
  --color-gray-30: #d4d4d8;
  --color-gray-40: #a1a1aa;
  --color-gray-50: #71717a;
  --color-gray-60: #52525b;
  --color-gray-70: #3f3f46;
  --color-gray-80: #27272a;
  --color-gray-90: #18181b;
  --color-ink-10: #f1f3fa;
  --color-ink-20: #e3e5f2;
  --color-ink-30: #cdd0e5;
  --color-ink-40: #9b9ec2;
  --color-ink-50: #6e6f9b;
  --color-ink-60: #52507c;
  --color-ink-70: #3e3c67;
  --color-ink-80: #2a284b;
  --color-ink-90: #1a1838;
  --bg: white;
  --txt: var(--color-ink-80);
  --accent: var(--color-blue-60);
  --glow: var(--color-purple-70);
  --separator-color: var(--color-gray-20);
  --nav-bg: transparent;
  --nav-txt: white;
  --nav-height: 80px;
  --offset-x: 0;
  --offset-y: 0;
}
html {
  font-family: 'Inter', sans-serif;
}
@supports (font-variation-settings: normal) {
  html {
    font-family: 'Inter var', sans-serif;
  }
}
picture,
img {
  max-width: 100%;
}
body {
  font-size: 18px;
  line-height: 1.5;
  font-weight: 300;
  background: var(--bg);
  color: var(--txt);
  position: relative;
  scroll-padding-top: var(--nav-height);
  min-height: 100vh;
}
.hidden {
  display: none;
}
h1,
h2 {
  font-family: 'Metropolis', sans-serif;
  font-weight: 600;
}
a {
  color: currentColor;
  text-decoration-color: currentColor;
  text-underline-offset: 0.25em;
  text-decoration-thickness: 0.12em;
  text-decoration-style: dotted;
  transition: font-size 0.2s, text-decoration-color 0.2s;
}
a.donate,
a:hover,
a:hover:visited {
  text-decoration-color: var(--accent);
}
a:visited {
  text-decoration-color: var(--color-purple-50);
}
.icon {
  display: inline-block;
  height: 32px;
  width: 32px;
}
.icon-apple {
  position: relative;
  top: -2px;
}
.icon .icon-linux-fg {
  fill: transparent !important;
}
.icon-tux-alt .icon-linux-fg {
  fill: var(--color-gray-20) !important;
}
.icon-tux-alt .icon-linux-bg {
  fill: transparent !important;
}
/* Remove some list padding */
.download-dumb > ul {
  padding: 0;
}
.download-button > ul {
  padding: 0;
}
.btn-donate,
.btn-donate-lg,
.btn-download-inline,
.btn-download-link,
.download-link.btn-download,
a.btn,
button {
  font-size: 1.25rem;
  background: var(--btn-bg, transparent);
  border-radius: 6px;
  border: 0.12em solid currentColor;
  color: currentColor;
  padding: 0.5em 3em;
  position: relative;
  transition: font-size 0.2s;
  text-decoration: none;
  white-space: nowrap;
}
.btn-donate::before,
.btn-donate-lg::before,
.btn-download-inline::before,
.btn-download-link::before,
.download-link.btn-download::before,
a.btn::before,
button::before {
  content: '';
  position: absolute;
  background: var(--glow);
  border-radius: 50%;
  filter: blur(32px);
  opacity: 0.25;
  inset: -0.25em -5em;
  z-index: -2;
}
.btn-donate::after,
.btn-donate-lg::after,
.btn-download-inline::after,
.btn-download-link::after,
.download-link.btn-download::after,
a.btn::after,
button::after {
  content: '';
  position: absolute;
  background-color: transparent;
  background-image: radial-gradient(circle, var(--accent), var(--color-purple-60) 30%);
  background-size: 400%;
  background-position: center;
  opacity: 0.5;
  inset: 0;
  z-index: -1;
  transition: all 0.3s;
}
.download-link.btn-download:hover::after,
a.btn:hover::after,
button:hover::after {
  opacity: 0.75;
  background-size: 500%;
}
/** HACK for Mozilla Firefox */
@-moz-document url-prefix() {
  .download-link.btn-download::after {
    inset-inline-end: 4px;
  }
}
.download-button .ios-download,
.download-button .linux-arm-download,
.download-button .unrecognized-download,
.download-button .unsupported-download,
.download-button .unsupported-download-osx,
.download-button .nojs-download {
  display: none;
}
.download-button .os_msi,
.download-button .os_winsha1,
.download-button .os_win64,
.download-button .os_linux,
.download-button .os_linux64,
.win7up.x86.x64 .download-button .os_win,
.android .download-button-desktop,
.windows.arm .download-button .os_win,
.linux.arm .download-button .os_linux,
.linux.x86.x64 .download-list .os_linux,
.download-button .os_win,
.download-button .os_osx,
.download-button .os_android,
.download-button .os_ios,
.no-js .download-list,
.other .download-list {
  display: none !important;
}
.win7up.x86.x64 .download-button .os_win64,
.linux .download-button .os_linux,
.linux.x86.x64 .download-button .os_linux64,
.windows .download-button .os_win,
.osx .download-button .os_osx,
.android .download-button .os_android,
.download-button-android .os_android,
.android .download-button-desktop .download-list,
.android .download-button-desktop small.os_win,
.download-button-ios .os_ios,
.ios .download-button .os_ios,
.ios .download-button .ios-download,
.ios .download-button-desktop .download-list,
.other .download-button-android .download-list,
.other .download-button small.os_win {
  display: block !important;
}
.windows.arm .download-button .unsupported-download,
.linux.arm .download-button .linux-arm-download,
.chromeos .download-button .unsupported-download,
.oldwin .download-button .unsupported-download,
.oldmac .download-button .unsupported-download {
  display: block;
  max-width: 250px;
}
.windows.arm .download-button .fx-privacy-link,
.linux.arm .download-button .fx-privacy-link,
.chromeos .download-button .fx-privacy-link,
.oldwin .download-button .fx-privacy-link,
.oldmac .download-button .fx-privacy-link {
  display: none;
}
.android .download-button-desktop .nojs-download,
.ios .download-button-desktop .nojs-download,
.no-js .download-button .nojs-download {
  display: block;
}
.other .download-button .unrecognized-download {
  display: block;
}
.download-button .download-list .os_android.x86,
.download-button .download-other.os_android .api-15,
.android.x86 .download-button .download-list .os_android.armv7up,
.android.x86 .download-button .download-other.os_android .x86 {
  display: none !important;
}
.android.x86 .download-button .download-list .os_android.x86 {
  display: block !important;
}
.android.x86 .download-button .download-other.os_android .armv7up {
  display: inline !important;
}
.windows.sha-1 .download-button .os_win {
  display: none !important;
}
.windows.sha-1 .download-button .os_winsha1 {
  display: block !important;
}
.download-button ul {
  list-style: none;
}
.download-button ul li {
  margin-block: 40px;
}
.site-nav {
  box-sizing: border-box;
  position: fixed;
  top: 0;
  inset-inline: 0;
  display: flex;
  padding-inline: 1rem;
  padding-block: 12px;
  background: var(--nav-bg);
  color: var(--nav-txt);
  border-bottom: 1px solid transparent;
  box-shadow: none;
  height: var(--nav-height);
  backdrop-filter: blur(32px);
  transition: height 0.2s;
  z-index: 100000;
}
.site-nav .btn-donate::before,
.site-nav .btn-donate-lg::before,
.site-nav .btn-download-inline::before,
.site-nav .btn-download-link::before,
.site-nav .download-link.btn-download::before,
.site-nav a.btn::before,
.site-nav button::before {
  display: none;
}
.scrolled-nav {
  --nav-height: 60px;
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 8px -2px rgba(0, 0, 0, 0.25);
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(24px) saturate(120%);
  transition: all 0.5s;
  z-index: 100000;
}
.scrolled-nav .nav-ln,
.scrolled-nav button,
.scrolled-nav a.btn {
  transition: font-size 0.2s;
  font-size: 1rem;
}
.scrolled-nav .nav-ln:hover {
  font-size: 1rem;
}
.nav-container {
  display: flex;
  flex-grow: 1;
  justify-content: space-between;
  max-width: 1280px;
  margin: 0 auto;
}
svg {
  max-height: 100%;
  max-width: 100%;
}
.logo {
  --accent: var(--color-blue-20);
  max-height: 100%;
  max-width: 300px;
  min-width: 200px;
  height: 60px;
  width: auto;
  transition: color 0.2s;
}
.logo:hover,
.mozilla-logo:hover {
  color: var(--accent);
  transform: none;
}
.nav-links {
  display: flex;
  align-items: center;
  gap: 30px;
}
/* Hide the header download button if the browser does not support javascript */
.no-js .header-download {
  display: none !important;
}
.nav-ln,
.cta-ln,
.footer-ln {
  font-size: 1.25rem;
  line-height: 1rem;
  font-weight: 600;
  text-decoration: none;
  position: relative;
  transition: font-size 0.2s;
}
.nav-ln::after,
.cta-ln::after,
.footer-ln::after {
  content: '';
  position: absolute;
  inset-inline: 0;
  bottom: -0.3em;
  height: 2px;
  background-color: var(--color-blue-60);
  background-image: linear-gradient(to right, var(--color-blue-50), var(--color-purple-50));
}
.nav-ln:hover,
.footer-ln:hover {
  line-height: 1rem;
}
.cta-ln:hover {
  font-size: 1.3rem;
  line-height: 1rem;
}
.nav-ln::after,
.footer-ln::after {
  width: 0;
  transition: width 0.3s;
}
.nav-ln:hover::after,
.footer-ln:hover::after {
  width: 100%;
}
.nav-btn::before {
  display: none;
}
header,
section {
  position: relative;
  text-align: center;
  scroll-padding: var(--nav-size);
}
#masthead,
header {
  display: grid;
  place-items: center;
  min-height: 80vh;
  background-color: black;
  background-image: url('../img/thunderbird/new/hero/hero.png'), linear-gradient(to top, var(--color-ink-90), black 80%);
  background-size: auto 80vh, 100%;
  background-position: bottom center, center;
  background-repeat: no-repeat;
  color: var(--color-gray-20);
  padding-top: var(--nav-size);
  isolation: isolate;
}
.container.hero {
  padding-block: 10vh 40vh;
}
.hero-text {
  font-size: 1.5rem;
  font-weight: 300;
  text-align: center;
  line-height: 1.4;
  max-inline-size: 50.5rem;
}
.tagline {
  font-size: 6rem;
  line-height: 1;
  font-weight: bold;
  margin-bottom: 1rem;
}
.txt-gradient {
  background-color: var(--accent);
  background-image: linear-gradient(to right, var(--color-blue-50), var(--color-purple-50));
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}
.sub-tag {
  margin-top: 0;
}
.hero-download {
  display: grid;
  place-items: center;
  gap: 12px;
}
.mask {
  color: var(--bg);
  filter: drop-shadow(0 -2px 0 var(--separator-color));
  position: absolute;
  inset-inline: 0;
  bottom: 0;
  height: 75px;
}
.mask > svg {
  position: absolute;
  inset-inline: 0;
  max-height: none;
  max-width: none;
  bottom: -1px;
}
.container {
  position: relative;
  display: grid;
  gap: 60px;
  padding-inline: 1rem;
  padding-block: 200px;
  margin-inline: auto;
  max-width: 1280px;
  overflow: hidden;
}
.section-text {
  max-inline-size: 62ch;
  text-wrap: balance;
  margin-inline: auto;
  margin-bottom: 2em;
}
.section-text :where(h2) {
  font-size: 2.75rem;
  font-weight: 800;
  margin: 0;
}
.section-text :where(h3) {
  font-size: 1.4rem;
  font-weight: 300;
  margin: 0;
  margin-bottom: 2em;
}
.cta {
  margin-top: 80px;
}
.graphic {
  position: relative;
  isolation: isolate;
  width: 100%;
}
.carousel {
  position: absolute;
  inset: 0;
  z-index: 2;
}
.carousel > picture {
  position: absolute;
  inset: 0;
  opacity: 1;
  z-index: 3;
}
.carousel > picture:last-child {
  opacity: 0;
  animation-duration: 30s;
  animation-name: fade-in-out;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}
@keyframes fade-in-out {
  0% {
    opacity: 0;
  }
  32% {
    opacity: 0;
  }
  33.33% {
    opacity: 1;
  }
  65% {
    opacity: 1;
  }
  66.66% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}
.floating-icons {
  --size: 60px;
  position: absolute;
  display: flex;
  inset-block: 200px;
  inset-inline: 0;
  overflow: hidden;
  opacity: 0.75;
  z-index: -1;
}
.competitor-icons,
.blank-icons {
  transform: translate(0, -4px);
}
.competitor-icon {
  position: absolute;
  top: calc(var(--set-y) - var(--size));
  left: calc(var(--set-x) - var(--size));
  display: block;
  height: var(--size);
  width: var(--size);
  filter: drop-shadow(0 4px 4px var(--color-gray-10));
  animation: 20s infinite normal float-around, 20s infinite normal float-around-shadow;
  z-index: -1;
}
.blank-icon {
  position: absolute;
  top: calc(var(--rand-y) - var(--size));
  left: calc(var(--rand-x) - var(--size));
  background-color: var(--color-gray-30);
  background-image: linear-gradient(to bottom, #f2f2f2, #d3d3d3);
  border-radius: calc(var(--size) / 3);
  height: var(--size);
  width: var(--size);
  opacity: calc(30% + var(--rand-y));
  filter: blur(5px);
  animation: 20s infinite normal float-around;
  z-index: -2;
}
.box-1 {
  --rand-x: 30%;
  --rand-y: 22%;
  --size: 23px;
  animation-delay: 1s;
}
.box-2 {
  --rand-x: 20%;
  --rand-y: 26%;
  --size: 58px;
  animation-delay: 4s;
}
.box-3 {
  --rand-x: 44%;
  --rand-y: 32%;
  --size: 14px;
  animation-delay: 7s;
}
.box-4 {
  --rand-x: 60%;
  --rand-y: 29%;
  --size: 39px;
  animation-delay: 3s;
}
.box-5 {
  --rand-x: 50%;
  --rand-y: 30%;
  --size: 52px;
  animation-delay: 10s;
}
.box-6 {
  --rand-x: 5%;
  --rand-y: 10%;
  --size: 47px;
  animation-delay: 2s;
}
.box-7 {
  --rand-x: 95%;
  --rand-y: 14%;
  --size: 31px;
  animation-delay: 4s;
}
.box-8 {
  --rand-x: 48%;
  --rand-y: 50%;
  --size: 56px;
  animation-delay: 2s;
}
.apple {
  --set-x: 55%;
  --set-y: 32%;
  animation-delay: 2s;
}
.outlook {
  --set-x: 80%;
  --set-y: 30%;
  animation-delay: 4s;
}
.yahoo {
  --set-x: 90%;
  --set-y: 20%;
  animation-delay: 7s;
}
.gmail {
  --set-x: 38%;
  --set-y: 30%;
  animation-delay: 3s;
}
.proton {
  --set-x: 19%;
  --set-y: 25%;
  animation-delay: 2s;
}
@keyframes float-around {
  0% {
    transform: translate(0, -4px);
  }
  25% {
    transform: translate(4px, 0);
  }
  50% {
    transform: translate(0, 4px);
  }
  75% {
    transform: translate(-4px, 0);
  }
  100% {
    transform: translate(0, -4px);
  }
}
@keyframes float-around-shadow {
  0% {
    filter: drop-shadow(0 4px 4px var(--color-gray-10));
  }
  25% {
    filter: drop-shadow(-4px 0 4px var(--color-gray-10));
  }
  50% {
    filter: drop-shadow(0 -4px 4px var(--color-gray-10));
  }
  75% {
    filter: drop-shadow(4px 0 4px var(--color-gray-10));
  }
  100% {
    filter: drop-shadow(0 4px 4px var(--color-gray-10));
  }
}
#free-from-manipulation {
  background-image: linear-gradient(to top, color-mix(in srgb, var(--color-purple-10) 50%, transparent), transparent 20%);
  background-position: bottom center;
  background-repeat: no-repeat;
  overflow: hidden;
}
.planets {
  position: absolute;
  inset: 0;
  opacity: 0.3;
  z-index: -1;
}
.planet-thunderbird {
  position: absolute;
  inset-inline: 0;
  bottom: 0;
  filter: drop-shadow(0 0 50px var(--color-blue-30));
}
.planet-firefox {
  position: absolute;
  right: 0;
  bottom: 20%;
  height: 280px;
  width: 280px;
  filter: drop-shadow(0 0 50px var(--color-orange-40));
}
.testimonials {
  display: grid;
  gap: 60px;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(4, 1fr);
  margin-inline: auto;
  margin-block: 0 60px;
  max-width: 1000px;
  text-align: left;
}
.testimonial-card {
  display: grid;
  place-items: center;
  grid-column: span 1;
  grid-row: span 1;
  padding: 1.5rem;
  border-radius: 24px;
  box-shadow: 0 4px 12px -2px var(--color-gray-30);
  overflow: hidden;
}
.card-lg {
  --accent: var(--color-purple-20);
  position: relative;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-column: span 2;
  grid-row: span 2;
  background-image: linear-gradient(to left, var(--accent), transparent 90%);
}
.card-lg:nth-child(even) {
  --accent: var(--color-blue-20);
  grid-column: 2 / span 2;
  grid-row: 3 / span 2;
}
.quote {
  font-weight: 400;
  display: grid;
  gap: 15px;
  grid-column: span 2;
  z-index: 2;
}
.visual {
  position: absolute;
  overflow: hidden;
  width: 200%;
}
.visual.unified-inbox {
  left: 10%;
}
.visual.extensions {
  left: 30%;
}
.attrib {
  display: grid;
  align-items: center;
  column-gap: 15px;
  grid-template-columns: auto 1fr;
  grid-template-rows: repeat(2, 1fr);
  line-height: 1.3;
}
.profile-pic {
  height: 50px;
  width: 50px;
  grid-row: span 2;
  border-radius: 50%;
  overflow: hidden;
}
.name {
  align-self: end;
}
.title {
  align-self: start;
  font-size: 0.9rem;
}
.cta-sub-heading {
  font-family: 'Inter', sans-serif;
  font-size: 1.5rem;
  font-weight: 300;
  line-height: 1.5;
}
.cta-heading {
  font-size: 5rem;
  font-weight: 900;
  line-height: 1.3;
  margin: 0;
}
#free-your-inbox .cta {
  --btn-bg: color-mix(in srgb, var(--bg) 90%, transparent);
  display: grid;
  place-items: center;
  gap: 9px;
  margin: 0;
}
#free-your-inbox .mask {
  --bg: black;
}
#whats-next {
  background-color: black;
  background-image: linear-gradient(to top, var(--color-ink-90), black 80%);
  background-size: auto 80vh, 100%;
  background-position: bottom center, center;
  background-repeat: no-repeat;
  color: var(--color-gray-20);
  overflow: hidden;
}
#whats-next .container {
  gap: 0;
}
#whats-next p {
  margin-block: 0;
  max-inline-size: 45ch;
}
.social-list {
  display: flex;
  gap: 24px;
  justify-content: center;
  margin-block: 60px;
}
.newsletter {
  display: grid;
  place-items: center;
  margin: 0;
}
.newsletter-form {
  border: 2px solid currentColor;
  border-radius: 6px;
  max-width: 100%;
  padding: 0;
  margin: 0;
  overflow: hidden;
}
.newsletter-form input {
  background: none;
  border: none;
  color: currentColor;
  padding: 0.75rem 1rem;
  margin: 0;
  font-size: 1rem;
}
.btn-newsletter {
  position: relative;
  background: none;
  border: none;
  border-radius: 0;
  border-inline-start: 2px solid currentColor;
  padding: 0.75rem 2.5rem;
  margin: 0;
  font-size: 1rem;
}
.btn-newsletter::after {
  content: '';
  position: absolute;
  background-color: transparent;
  background-image: radial-gradient(circle, var(--accent), var(--color-purple-60) 20%);
  background-size: 0;
  background-position: center;
  opacity: 0;
  inset: 0;
  z-index: 0;
  transition: all 0.3s;
}
.btn-newsletter:hover::after {
  opacity: 0.25;
  background-size: 800%;
}
.btn-newsletter-icon {
  display: none;
  padding: 0.75em 1em;
}
.social-list a {
  text-decoration: none;
}
.phone {
  max-width: calc(100vw - 2rem);
  width: 500px;
  margin-inline: auto;
  margin-block: -125px -750px;
}
footer {
  background-color: black;
  color: var(--color-gray-20);
}
.container.footer {
  place-items: center;
  padding-inline: 1rem;
  padding-block: 6rem;
}
.footer-ln {
  font-size: 1.1rem;
}
.site-links,
.legal-links {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
  justify-content: center;
}
.legal-links {
  font-size: 1rem;
}
.mzla {
  display: grid;
  place-items: center;
  gap: 15px;
  text-align: center;
  max-inline-size: 75ch;
}
.mozilla-logo {
  --accent: white;
  max-width: 200px;
}
.fill-current {
  display: none;
}
@media (max-width: 860px) {
  .testimonials {
    display: grid;
    gap: 15px;
    grid-template-columns: 1fr;
    grid-template-rows: auto;
  }
  .testimonial-card,
  .card-lg,
  .card-lg:nth-child(even) {
    grid-template-columns: 1fr;
    grid-column: span 1;
    grid-row: span 2;
    place-items: start;
    background-image: none;
  }
  .nav-ln {
    display: none;
  }
  .visual {
    display: none;
  }
}
@media (max-width: 780px) {
  .nav-container {
    justify-content: center;
  }
  .nav-links {
    display: none;
  }
  .container {
    padding-inline: 0.25rem;
    padding-block: 32px;
  }
  .testimonials {
    margin-block: 0;
  }
  .cta {
    margin-top: 32px;
  }
  .tagline {
    font-size: 4rem;
  }
  .btn-newsletter-text {
    display: none;
  }
  .btn-newsletter-icon {
    display: inline-block;
  }
  .phone {
    margin-block: -125px -350px;
  }
}
@media (max-width: 512px) {
  .container {
    padding-block: 32px;
  }
  .tagline {
    font-size: 3rem;
  }
  .cta-heading {
    font-size: 3rem;
  }
}
.download-other {
  text-align: center;
  margin-top: -1rem;
}
.small-link {
  font-size: 80%;
}
