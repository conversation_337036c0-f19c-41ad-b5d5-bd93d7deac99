/*-------------------------
* Colours
*--------------------------*/
body {
  font-family: 'Open Sans', X-LocaleSpecific, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.font-base,
body,
.btn-donate,
.btn-donate-lg,
.btn-download-inline,
.btn-download-link {
  font-size: 13px;
}
.font-xs {
  font-size: 9px;
}
.font-sm,
.social-link,
.tabs-nav label,
.tabs-nav #channel-header,
.social-link-large {
  font-size: 11px;
}
.font-regular,
.social-link-large,
.btn-body,
.btn-beta.btn-beta-pill,
.btn-link,
.btn-banner,
.modal footer ul > li:not(:last-child)::after {
  font-size: 12px;
}
.font-md,
.blog-link,
.btn-download,
.btn-newsletter,
.btn-join,
.btn-beta,
.btn-daily,
.btn-banner,
.modal form .amount-selection label:last-child #amount-other::placeholder,
.faq-entry .faq-answer,
.newsletter-form input {
  font-size: 15px;
}
.font-lg,
.subheader-section,
.markup-page h3 {
  font-size: 17px;
}
.font-xl,
.modal form .amount-selection label,
.modal form .amount-selection label:last-child #amount-other,
.btn-donate-and-download {
  font-size: 19px;
}
.font-2xl,
.btn-donate-lg,
.faq-entry .faq-question,
.header-section,
.markup-page h2 {
  font-size: 21px;
}
.font-3xl {
  font-size: 25px;
}
.font-4xl {
  font-size: 41px;
}
.font-hero {
  font-size: 59px;
}
.font-style-normal {
  font-style: normal;
}
.font-style-italic {
  font-style: italic;
}
.font-style-oblique {
  font-style: oblique;
}
.font-hairline {
  font-weight: 100;
}
.font-thin {
  font-weight: 200;
}
.font-light {
  font-weight: 300;
}
.font-normal {
  font-weight: 400;
}
.font-medium {
  font-weight: 500;
}
.font-semibold,
.social-link-large,
.inline-link,
.social-link,
.btn-donate,
.btn-donate-lg,
.btn-download,
.btn-download-inline,
.btn-download-link,
.btn-body,
.btn-join,
.btn-beta,
.btn-daily,
.btn-link,
.btn-banner,
.btn-donate-and-download,
.social-link-large,
.p-links a,
.p-links-blue a,
.markup-page a {
  font-weight: 600;
}
.font-bold,
.blog-link,
.btn-newsletter,
.btn-beta.btn-beta-pill,
.modal form .amount-selection label,
.newsletter-form input {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-black {
  font-weight: 900;
}
.tracking-tighter {
  letter-spacing: -0.05em;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-normal,
.btn-beta.btn-beta-pill {
  letter-spacing: 0;
}
.tracking-wide,
body,
.faq-entry .faq-answer,
.newsletter-form input {
  letter-spacing: 0.025em;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.tracking-widest {
  letter-spacing: 0.1em;
}
.leading-none,
.social-link,
.btn-newsletter,
.modal footer ul > li:not(:last-child)::after,
.header-section span,
.social-link-large {
  line-height: 1;
}
.leading-tight {
  line-height: 1.25;
}
.leading-snug {
  line-height: 1.375;
}
.leading-normal,
.btn-beta.btn-beta-pill,
.faq-entry .faq-question,
.header-section,
.subheader-section,
.markup-page h2,
.markup-page h3 {
  line-height: 1.5;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-loose,
#amount-cancel p,
.faq-entry .faq-answer {
  line-height: 2;
}
.underline,
.small-link:hover,
.small-link:focus,
.inline-link,
.header-link:hover,
.header-link:focus,
.blog-link:hover,
.blog-link:focus,
.p-links a,
.p-links-blue a,
.markup-page a {
  text-decoration: underline;
}
.line-through {
  text-decoration: line-through;
}
.no-underline,
.nav-link,
.nav-footer .nav-link,
.small-link,
.header-link,
.social-link,
.blog-link,
.btn-donate,
.btn-donate-lg,
.btn-download,
.btn-download-inline,
.btn-download-link,
.btn-body,
.btn-block-white,
.btn-join,
.btn-beta,
.btn-daily,
.btn-link,
.btn-banner:hover,
.btn-banner:focus,
.btn-donate-and-download,
.social-link-large {
  text-decoration: none;
}
.uppercase,
.blog-link,
.btn-donate,
.btn-download,
.btn-download-inline,
.btn-body,
.btn-newsletter,
.btn-join,
.btn-beta,
.btn-daily,
.btn-link,
.header-section,
.tabs-nav label,
.tabs-nav #channel-header,
.markup-page h2 {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize,
.btn-download-link {
  text-transform: capitalize;
}
.normal-case,
.btn-body.btn-secondary {
  text-transform: none;
}
.text-left {
  text-align: left;
}
.text-center,
.social-link-large span {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-justify {
  text-align: justify;
}
@media (min-width: 640px) {
  .sm\:font-base {
    font-size: 13px;
  }
  .sm\:font-xs {
    font-size: 9px;
  }
  .sm\:font-sm {
    font-size: 11px;
  }
  .sm\:font-regular {
    font-size: 12px;
  }
  .sm\:font-md {
    font-size: 15px;
  }
  .sm\:font-lg {
    font-size: 17px;
  }
  .sm\:font-xl {
    font-size: 19px;
  }
  .sm\:font-2xl {
    font-size: 21px;
  }
  .sm\:font-3xl {
    font-size: 25px;
  }
  .sm\:font-4xl {
    font-size: 41px;
  }
  .sm\:font-hero {
    font-size: 59px;
  }
  .sm\:leading-none {
    line-height: 1;
  }
  .sm\:leading-tight {
    line-height: 1.25;
  }
  .sm\:leading-snug {
    line-height: 1.375;
  }
  .sm\:leading-normal {
    line-height: 1.5;
  }
  .sm\:leading-relaxed {
    line-height: 1.625;
  }
  .sm\:leading-loose {
    line-height: 2;
  }
}
@media (min-width: 768px) {
  .md\:font-base {
    font-size: 13px;
  }
  .md\:font-xs {
    font-size: 9px;
  }
  .md\:font-sm {
    font-size: 11px;
  }
  .md\:font-regular {
    font-size: 12px;
  }
  .md\:font-md {
    font-size: 15px;
  }
  .md\:font-lg {
    font-size: 17px;
  }
  .md\:font-xl {
    font-size: 19px;
  }
  .md\:font-2xl {
    font-size: 21px;
  }
  .md\:font-3xl {
    font-size: 25px;
  }
  .md\:font-4xl {
    font-size: 41px;
  }
  .md\:font-hero {
    font-size: 59px;
  }
  .md\:leading-none {
    line-height: 1;
  }
  .md\:leading-tight {
    line-height: 1.25;
  }
  .md\:leading-snug {
    line-height: 1.375;
  }
  .md\:leading-normal,
  .btn-newsletter {
    line-height: 1.5;
  }
  .md\:leading-relaxed {
    line-height: 1.625;
  }
  .md\:leading-loose {
    line-height: 2;
  }
}
@media (min-width: 1024px) {
  .lg\:font-base {
    font-size: 13px;
  }
  .lg\:font-xs {
    font-size: 9px;
  }
  .lg\:font-sm {
    font-size: 11px;
  }
  .lg\:font-regular {
    font-size: 12px;
  }
  .lg\:font-md {
    font-size: 15px;
  }
  .lg\:font-lg {
    font-size: 17px;
  }
  .lg\:font-xl {
    font-size: 19px;
  }
  .lg\:font-2xl {
    font-size: 21px;
  }
  .lg\:font-3xl {
    font-size: 25px;
  }
  .lg\:font-4xl {
    font-size: 41px;
  }
  .lg\:font-hero {
    font-size: 59px;
  }
  .lg\:leading-none {
    line-height: 1;
  }
  .lg\:leading-tight {
    line-height: 1.25;
  }
  .lg\:leading-snug {
    line-height: 1.375;
  }
  .lg\:leading-normal {
    line-height: 1.5;
  }
  .lg\:leading-relaxed {
    line-height: 1.625;
  }
  .lg\:leading-loose {
    line-height: 2;
  }
}
@media (min-width: 1280px) {
  .xl\:font-base {
    font-size: 13px;
  }
  .xl\:font-xs {
    font-size: 9px;
  }
  .xl\:font-sm {
    font-size: 11px;
  }
  .xl\:font-regular {
    font-size: 12px;
  }
  .xl\:font-md {
    font-size: 15px;
  }
  .xl\:font-lg {
    font-size: 17px;
  }
  .xl\:font-xl {
    font-size: 19px;
  }
  .xl\:font-2xl {
    font-size: 21px;
  }
  .xl\:font-3xl {
    font-size: 25px;
  }
  .xl\:font-4xl {
    font-size: 41px;
  }
  .xl\:font-hero {
    font-size: 59px;
  }
  .xl\:leading-none {
    line-height: 1;
  }
  .xl\:leading-tight {
    line-height: 1.25;
  }
  .xl\:leading-snug {
    line-height: 1.375;
  }
  .xl\:leading-normal {
    line-height: 1.5;
  }
  .xl\:leading-relaxed {
    line-height: 1.625;
  }
  .xl\:leading-loose {
    line-height: 2;
  }
}
.text-transparent {
  color: transparent;
}
.bg-transparent,
.btn-body.btn-secondary,
.btn-link,
.btn-banner,
.tabs-nav label,
.tabs-nav #channel-header {
  background-color: transparent;
}
.border-transparent,
.btn-body.btn-secondary {
  border-color: transparent;
}
.text-white,
.nav-link,
.nav-footer .nav-link,
.small-link,
.social-link-large:hover,
.social-link-large:focus,
.inline-link,
.social-link,
.btn-donate,
.btn-donate-lg,
.btn-download,
.btn-download-inline:hover,
.btn-download-inline:focus,
.btn-body.btn-release:hover,
.btn-body.btn-release:focus,
.btn-inline:hover,
.btn-inline:focus,
.btn-inline:active,
.btn-newsletter,
.btn-join,
.btn-beta,
.btn-daily,
.btn-link,
.header-section span,
.tabs-nav label:hover,
#release:checked ~ aside nav .tabs-nav .release label,
#beta:checked ~ aside nav .tabs-nav .beta label,
#daily:checked ~ aside nav .tabs-nav .daily label,
.social-link-large,
.p-links a,
.p-links-blue a,
.markup-page a {
  color: #fff;
}
.bg-white,
.nav-link:after,
.btn-download-inline,
.btn-body,
.btn-block-white:hover,
.btn-block-white:focus,
.btn-link:hover,
.btn-link:focus,
.modal form .amount-selection label,
.btn-donate-and-download {
  background-color: #fff;
}
.border-white {
  border-color: #fff;
}
.text-black,
.btn-inline,
.btn-banner,
.form-select {
  color: #000;
}
.bg-black {
  background-color: #000;
}
.border-black {
  border-color: #000;
}
.text-black-25 {
  color: rgba(0, 0, 0, 0.25);
}
.bg-black-25 {
  background-color: rgba(0, 0, 0, 0.25);
}
.border-black-25 {
  border-color: rgba(0, 0, 0, 0.25);
}
.text-black-light {
  color: #212123;
}
.bg-black-light,
#thunderbird-newsletter {
  background-color: #212123;
}
.border-black-light {
  border-color: #212123;
}
.text-black-lighter {
  color: #34343A;
}
.bg-black-lighter,
.social-link span {
  background-color: #34343A;
}
.border-black-lighter {
  border-color: #34343A;
}
.text-black-lightest,
.btn-download-link {
  color: #45454B;
}
.bg-black-lightest,
.social-link,
.btn-inline:hover,
.btn-inline:focus,
.tabs-nav label:hover,
.social-link-large {
  background-color: #45454B;
}
.border-black-lightest {
  border-color: #45454B;
}
.text-grey,
.modal footer ul > li:not(:last-child)::after {
  color: #f9f9f9;
}
.bg-grey,
.social-link-large {
  background-color: #f9f9f9;
}
.border-grey,
.modal form .amount-selection label,
.modal form .amount-selection label:last-child #amount-other {
  border-color: #f9f9f9;
}
.text-grey-light {
  color: #f0f0f0;
}
.bg-grey-light,
.btn-inline,
.form-select,
.newsletter-form {
  background-color: #f0f0f0;
}
.border-grey-light,
.markup-page hr {
  border-color: #f0f0f0;
}
.text-grey-lighter,
.social-link-large span svg {
  color: #fdfdfd;
}
.bg-grey-lighter,
.modal form .amount-selection label:last-child {
  background-color: #fdfdfd;
}
.border-grey-lighter {
  border-color: #fdfdfd;
}
.text-grey-lightest {
  color: #b1b1b3;
}
.bg-grey-lightest {
  background-color: #b1b1b3;
}
.border-grey-lightest {
  border-color: #b1b1b3;
}
.text-blue-dark,
.social-link-large,
.faq-entry .faq-question,
h1 .anchor,
h2 .anchor,
h3 .anchor,
h4 .anchor,
h5 .anchor,
h6 .anchor,
.header-section,
.subheader-section,
.markup-page h2,
.markup-page h3 {
  color: #36385A;
}
.bg-blue-dark,
.social-link-large:hover,
.social-link-large:focus {
  background-color: #36385A;
}
.border-blue-dark {
  border-color: #36385A;
}
.text-orange {
  color: #ff9500;
}
.bg-orange {
  background-color: #ff9500;
}
.border-orange {
  border-color: #ff9500;
}
.text-blue-lightest {
  color: #B2D9FF;
}
.bg-blue-lightest,
.header-line {
  background-color: #B2D9FF;
}
.border-blue-lightest,
.header-section span {
  border-color: #B2D9FF;
}
.text-blue,
.header-link,
.p-links-blue a,
.blog-link,
.btn-body.btn-secondary,
.btn-link:hover,
.btn-link:focus,
.btn-donate-and-download .donate-heart,
.markup-page a,
section.tab.release a.small-link {
  color: #0080FF;
}
.bg-blue,
.social-link-large span,
.social-link:hover,
.social-link:focus,
.btn-body.btn-release:hover,
.btn-body.btn-release:focus,
.btn-inline:active,
.btn-newsletter:active,
.header-section span,
#release:checked ~ aside nav .tabs-nav .release label,
#beta:checked ~ aside nav .tabs-nav .beta label,
#daily:checked ~ aside nav .tabs-nav .daily label {
  background-color: #0080FF;
}
.border-blue,
.modal form .amount-selection label:hover,
.modal form .amount-selection label.active {
  border-color: #0080FF;
}
.text-blue-dark,
.social-link-large,
.faq-entry .faq-question,
h1 .anchor,
h2 .anchor,
h3 .anchor,
h4 .anchor,
h5 .anchor,
h6 .anchor,
.header-section,
.subheader-section,
.markup-page h2,
.markup-page h3 {
  color: #245CAD;
}
.bg-blue-dark,
.social-link-large:hover,
.social-link-large:focus {
  background-color: #245CAD;
}
.border-blue-dark {
  border-color: #245CAD;
}
.text-blue-darker,
body {
  color: #36385A;
}
.bg-blue-darker {
  background-color: #36385A;
}
.border-blue-darker {
  border-color: #36385A;
}
.text-green,
.btn-body,
.btn-donate-and-download {
  color: #058B00;
}
.bg-green {
  background-color: #058B00;
}
.border-green {
  border-color: #058B00;
}
.text-green-light,
.btn-download-inline,
.btn-download-link:hover,
.btn-download-link:focus {
  color: #11AD00;
}
.bg-green-light,
.btn-download-inline:hover,
.btn-download-inline:focus,
.header-section span.green {
  background-color: #11AD00;
}
.border-green-light,
.btn-download-inline,
.btn-body,
.btn-donate-and-download {
  border-color: #11AD00;
}
.text-green-lightest {
  color: #7ac97b;
}
.bg-green-lightest {
  background-color: #7ac97b;
}
.border-green-lightest,
.header-section span.green {
  border-color: #7ac97b;
}
.text-purple {
  color: #ad3bff;
}
.bg-purple {
  background-color: #ad3bff;
}
.border-purple {
  border-color: #ad3bff;
}
.text-red {
  color: #a4000f;
}
.bg-red {
  background-color: #a4000f;
}
.border-red {
  border-color: #a4000f;
}
.rounded-none {
  border-radius: 0;
}
.rounded-xs,
.btn-donate,
.btn-download,
.btn-join,
.btn-beta,
.btn-daily {
  border-radius: 0.1rem;
}
.rounded-sm,
.btn-download-inline,
.btn-body,
.btn-inline,
.btn-newsletter,
.btn-link,
.btn-banner,
.btn-donate-and-download,
.form-select,
.newsletter-form input,
.tabs-nav label,
.tabs-nav #channel-header {
  border-radius: 0.125rem;
}
.rounded,
.btn-donate-lg,
.btn-block-white,
.modal form .amount-selection label,
.newsletter-form {
  border-radius: 0.25rem;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-full,
.social-link-large span,
.social-link,
.social-link span,
.btn-beta.btn-beta-pill,
.header-section span,
.social-link-large {
  border-radius: 9999px;
}
.border-solid,
.btn-download-inline,
.btn-body,
.modal form .amount-selection label,
.modal form .amount-selection label:last-child #amount-other,
.btn-donate-and-download,
.markup-page hr {
  border-style: solid;
}
.border-dashed {
  border-style: dashed;
}
.border-dotted {
  border-style: dotted;
}
.border-none,
.btn-inline,
.btn-newsletter,
.form-select,
.newsletter-form input {
  border-style: none;
}
.border,
.btn-download-inline,
.btn-body,
.modal form .amount-selection label,
.btn-donate-and-download {
  border-width: 1px;
}
.border-0,
.modal form .amount-selection label:last-child #amount-other,
.markup-page hr {
  border-width: 0;
}
.border-t,
.markup-page hr {
  border-top-width: 1px;
}
.border-t-0 {
  border-top-width: 0;
}
.border-r {
  border-right-width: 1px;
}
.border-r-0 {
  border-right-width: 0;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-0 {
  border-bottom-width: 0;
}
.border-l,
.modal form .amount-selection label:last-child #amount-other {
  border-left-width: 1px;
}
.border-l-0 {
  border-left-width: 0;
}
.container {
  width: 100%;
}
.container-sm {
  max-width: 640px;
}
.container-md {
  max-width: 768px;
}
.container-lg {
  max-width: 1024px;
}
.container-xl {
  max-width: 1280px;
}
.block,
.nav-link:after,
.btn-donate,
.btn-donate-lg,
.form-select,
.header-line {
  display: block;
}
.inline-block,
.nav-link,
.btn-download,
.btn-download-inline,
.btn-download-link,
.btn-body,
.btn-join,
.btn-beta,
.btn-daily,
.btn-link,
.btn-banner,
.tabs-nav label,
.tabs-nav #channel-header {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex,
.social-link,
.btn-body span,
.btn-donate-and-download,
.newsletter-form,
.header-section,
.social-link-large,
.markup-page h2 {
  display: flex;
}
.table {
  display: table;
}
.hidden,
.download-hidden,
.js .channel-title {
  display: none;
}
@media (min-width: 640px) {
  .sm\:block {
    display: block;
  }
  .sm\:inline-block {
    display: inline-block;
  }
  .sm\:inline {
    display: inline;
  }
  .sm\:flex {
    display: flex;
  }
  .sm\:table {
    display: table;
  }
  .sm\:hidden {
    display: none;
  }
}
@media (min-width: 768px) {
  .md\:block {
    display: block;
  }
  .md\:inline-block {
    display: inline-block;
  }
  .md\:inline {
    display: inline;
  }
  .md\:flex {
    display: flex;
  }
  .md\:table {
    display: table;
  }
  .md\:hidden {
    display: none;
  }
}
@media (min-width: 1024px) {
  .lg\:block {
    display: block;
  }
  .lg\:inline-block {
    display: inline-block;
  }
  .lg\:inline {
    display: inline;
  }
  .lg\:flex {
    display: flex;
  }
  .lg\:table {
    display: table;
  }
  .lg\:hidden {
    display: none;
  }
}
@media (min-width: 1280px) {
  .xl\:block {
    display: block;
  }
  .xl\:inline-block {
    display: inline-block;
  }
  .xl\:inline {
    display: inline;
  }
  .xl\:flex {
    display: flex;
  }
  .xl\:table {
    display: table;
  }
  .xl\:hidden {
    display: none;
  }
}
.shadow,
.btn-donate,
.btn-donate-lg,
.btn-download,
.btn-join,
.btn-beta,
.btn-daily,
.newsletter-form {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.shadow-md,
.social-link-large,
.btn-donate:hover,
.btn-donate:focus,
.btn-donate-lg:hover,
.btn-donate-lg:focus,
.btn-download:hover,
.btn-download:focus,
.btn-download-inline:hover,
.btn-download-inline:focus,
.btn-body:hover,
.btn-body:focus,
.btn-block-white:hover,
.btn-block-white:focus,
.btn-newsletter:hover,
.btn-newsletter:focus,
.btn-join:hover,
.btn-join:focus,
.btn-beta:hover,
.btn-beta:focus,
.btn-daily:hover,
.btn-daily:focus,
.btn-link:hover,
.btn-link:focus,
.btn-donate-and-download:hover,
.btn-donate-and-download:focus {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
.shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
.shadow-2xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
.shadow-inner {
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
}
.shadow-outline {
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}
.shadow-none,
.modal form .amount-selection label.active {
  box-shadow: none;
}
.opacity-0,
.nav-link:after {
  opacity: 0;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-100,
.nav-link:hover:after,
.nav-link:focus:after {
  opacity: 1;
}
.shadow-img {
  filter: drop-shadow(5px 5px 6px rgba(0, 0, 0, 0.15));
}
.flex-initial {
  flex: 0 1 auto;
}
.flex-1,
.blog-body {
  flex: 1 1 0%;
}
.flex-auto {
  flex: 1 1 auto;
}
.flex-none {
  flex: none;
}
.flex-row {
  flex-direction: row;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-col,
.no-js .retry-download {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-no-wrap {
  flex-wrap: nowrap;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}
.items-stretch {
  align-items: stretch;
}
.items-start {
  align-items: flex-start;
}
.items-center,
.social-link,
.btn-body span,
.faq-entry .faq-question,
.newsletter-form,
.header-section,
.social-link-large,
.markup-page h2 {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.items-baseline {
  align-items: baseline;
}
.content-start {
  align-content: flex-start;
}
.content-center {
  align-content: center;
}
.content-end {
  align-content: flex-end;
}
.content-between {
  align-content: space-between;
}
.content-around {
  align-content: space-around;
}
.self-auto {
  align-self: auto;
}
.self-start,
.newsletter-form {
  align-self: flex-start;
}
.self-center {
  align-self: center;
}
.self-end {
  align-self: flex-end;
}
.self-stretch {
  align-self: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-center {
  justify-content: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.flex-grow {
  flex-grow: 1;
}
.flex-grow-0 {
  flex-grow: 0;
}
.flex-shrink {
  flex-shrink: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
@media (min-width: 640px) {
  .sm\:flex-row {
    flex-direction: row;
  }
  .sm\:flex-row-reverse {
    flex-direction: row-reverse;
  }
  .sm\:flex-col {
    flex-direction: column;
  }
  .sm\:flex-col-reverse {
    flex-direction: column-reverse;
  }
  .sm\:flex-grow {
    flex-grow: 1;
  }
  .sm\:flex-grow-0 {
    flex-grow: 0;
  }
  .sm\:items-stretch {
    align-items: stretch;
  }
  .sm\:items-start {
    align-items: flex-start;
  }
  .sm\:items-center {
    align-items: center;
  }
  .sm\:items-end {
    align-items: flex-end;
  }
  .sm\:items-baseline {
    align-items: baseline;
  }
}
@media (min-width: 768px) {
  .md\:flex-row {
    flex-direction: row;
  }
  .md\:flex-row-reverse {
    flex-direction: row-reverse;
  }
  .md\:flex-col {
    flex-direction: column;
  }
  .md\:flex-col-reverse {
    flex-direction: column-reverse;
  }
  .md\:flex-grow {
    flex-grow: 1;
  }
  .md\:flex-grow-0 {
    flex-grow: 0;
  }
  .md\:items-stretch {
    align-items: stretch;
  }
  .md\:items-start {
    align-items: flex-start;
  }
  .md\:items-center {
    align-items: center;
  }
  .md\:items-end {
    align-items: flex-end;
  }
  .md\:items-baseline {
    align-items: baseline;
  }
}
@media (min-width: 1024px) {
  .lg\:flex-row {
    flex-direction: row;
  }
  .lg\:flex-row-reverse {
    flex-direction: row-reverse;
  }
  .lg\:flex-col {
    flex-direction: column;
  }
  .lg\:flex-col-reverse {
    flex-direction: column-reverse;
  }
  .lg\:flex-grow {
    flex-grow: 1;
  }
  .lg\:flex-grow-0 {
    flex-grow: 0;
  }
  .lg\:items-stretch {
    align-items: stretch;
  }
  .lg\:items-start {
    align-items: flex-start;
  }
  .lg\:items-center {
    align-items: center;
  }
  .lg\:items-end {
    align-items: flex-end;
  }
  .lg\:items-baseline {
    align-items: baseline;
  }
}
@media (min-width: 1280px) {
  .xl\:flex-row {
    flex-direction: row;
  }
  .xl\:flex-row-reverse {
    flex-direction: row-reverse;
  }
  .xl\:flex-col {
    flex-direction: column;
  }
  .xl\:flex-col-reverse {
    flex-direction: column-reverse;
  }
  .xl\:flex-grow {
    flex-grow: 1;
  }
  .xl\:flex-grow-0 {
    flex-grow: 0;
  }
  .xl\:items-stretch {
    align-items: stretch;
  }
  .xl\:items-start {
    align-items: flex-start;
  }
  .xl\:items-center {
    align-items: center;
  }
  .xl\:items-end {
    align-items: flex-end;
  }
  .xl\:items-baseline {
    align-items: baseline;
  }
}
.list-none {
  list-style-type: none;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden,
.modal form .amount-selection label:last-child {
  overflow: hidden;
}
.overflow-visible {
  overflow: visible;
}
.overflow-scroll {
  overflow: scroll;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute,
.nav-link:after,
.btn-body span {
  position: absolute;
}
.relative,
.nav-link,
.nav-footer .nav-link,
.btn-body {
  position: relative;
}
.sticky {
  position: sticky;
}
.pin-t,
.btn-body span {
  top: 0;
}
.pin-r,
.nav-link:after,
.btn-body span {
  right: 0;
}
.pin-b,
.nav-link:after,
.btn-body span {
  bottom: 0;
}
.pin-l,
.nav-link:after {
  left: 0;
}
.pin-x {
  left: 0;
  right: 0;
}
.pin-y {
  top: 0;
  bottom: 0;
}
.w-auto {
  width: auto;
}
.w-2,
.btn-body span,
.btn-body span svg {
  width: 0.5rem;
}
.w-3,
.social-link span {
  width: 0.75rem;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6,
.social-link-large span,
.header-section span {
  width: 1.5rem;
}
.w-16 {
  width: 4rem;
}
.w-24 {
  width: 6rem;
}
.w-40,
.form-select {
  width: 10rem;
}
.w-48 {
  width: 12rem;
}
.w-56 {
  width: 14rem;
}
.w-64 {
  width: 16rem;
}
.w-1\/2 {
  width: 50%;
}
.w-1\/3 {
  width: 33.333333%;
}
.w-2\/3 {
  width: 66.666667%;
}
.w-1\/4 {
  width: 25%;
}
.w-2\/4 {
  width: 50%;
}
.w-3\/4 {
  width: 75%;
}
.w-1\/5 {
  width: 20%;
}
.w-2\/5 {
  width: 40%;
}
.w-3\/5 {
  width: 60%;
}
.w-4\/5 {
  width: 80%;
}
.w-1\/6 {
  width: 16.666667%;
}
.w-2\/6 {
  width: 33.333333%;
}
.w-3\/6 {
  width: 50%;
}
.w-4\/6 {
  width: 66.666667%;
}
.w-5\/6 {
  width: 83.333333%;
}
.w-1\/12 {
  width: 8.333333%;
}
.w-2\/12 {
  width: 16.666667%;
}
.w-3\/12 {
  width: 25%;
}
.w-4\/12 {
  width: 33.333333%;
}
.w-5\/12 {
  width: 41.666667%;
}
.w-6\/12 {
  width: 50%;
}
.w-7\/12 {
  width: 58.333333%;
}
.w-8\/12 {
  width: 66.666667%;
}
.w-9\/12 {
  width: 75%;
}
.w-10\/12 {
  width: 83.333333%;
}
.w-11\/12 {
  width: 91.666667%;
}
.w-full,
.modal form .amount-selection label:last-child #amount-other,
.newsletter-form input,
.markup-page hr {
  width: 100%;
}
.w-screen {
  width: 100vw;
}
.max-w-xs {
  max-width: 20rem;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.h-3,
.social-link span {
  height: 0.75rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6,
.social-link-large span,
.header-section span {
  height: 1.5rem;
}
.h-8 {
  height: 2rem;
}
.h-10,
.modal form .amount-selection label {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-16 {
  height: 4rem;
}
.h-auto {
  height: auto;
}
.h-full,
.modal form .amount-selection label:last-child #amount-other,
.no-js .retry-download {
  height: 100%;
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .platform-img {
    height: 100%;
  }
}
@media (min-width: 640px) {
  .sm\:w-auto {
    width: auto;
  }
  .sm\:w-2 {
    width: 0.5rem;
  }
  .sm\:w-3 {
    width: 0.75rem;
  }
  .sm\:w-4 {
    width: 1rem;
  }
  .sm\:w-5 {
    width: 1.25rem;
  }
  .sm\:w-6 {
    width: 1.5rem;
  }
  .sm\:w-16 {
    width: 4rem;
  }
  .sm\:w-24 {
    width: 6rem;
  }
  .sm\:w-40 {
    width: 10rem;
  }
  .sm\:w-48 {
    width: 12rem;
  }
  .sm\:w-56 {
    width: 14rem;
  }
  .sm\:w-64 {
    width: 16rem;
  }
  .sm\:w-1\/2 {
    width: 50%;
  }
  .sm\:w-1\/3 {
    width: 33.333333%;
  }
  .sm\:w-2\/3 {
    width: 66.666667%;
  }
  .sm\:w-1\/4 {
    width: 25%;
  }
  .sm\:w-2\/4 {
    width: 50%;
  }
  .sm\:w-3\/4 {
    width: 75%;
  }
  .sm\:w-1\/5 {
    width: 20%;
  }
  .sm\:w-2\/5 {
    width: 40%;
  }
  .sm\:w-3\/5 {
    width: 60%;
  }
  .sm\:w-4\/5 {
    width: 80%;
  }
  .sm\:w-1\/6 {
    width: 16.666667%;
  }
  .sm\:w-2\/6 {
    width: 33.333333%;
  }
  .sm\:w-3\/6 {
    width: 50%;
  }
  .sm\:w-4\/6 {
    width: 66.666667%;
  }
  .sm\:w-5\/6 {
    width: 83.333333%;
  }
  .sm\:w-1\/12 {
    width: 8.333333%;
  }
  .sm\:w-2\/12 {
    width: 16.666667%;
  }
  .sm\:w-3\/12 {
    width: 25%;
  }
  .sm\:w-4\/12 {
    width: 33.333333%;
  }
  .sm\:w-5\/12 {
    width: 41.666667%;
  }
  .sm\:w-6\/12 {
    width: 50%;
  }
  .sm\:w-7\/12 {
    width: 58.333333%;
  }
  .sm\:w-8\/12 {
    width: 66.666667%;
  }
  .sm\:w-9\/12 {
    width: 75%;
  }
  .sm\:w-10\/12 {
    width: 83.333333%;
  }
  .sm\:w-11\/12 {
    width: 91.666667%;
  }
  .sm\:w-full {
    width: 100%;
  }
  .sm\:w-screen {
    width: 100vw;
  }
  .sm\:max-w-xs {
    max-width: 20rem;
  }
  .sm\:max-w-sm {
    max-width: 24rem;
  }
  .sm\:max-w-md {
    max-width: 28rem;
  }
  .sm\:max-w-lg {
    max-width: 32rem;
  }
  .sm\:max-w-xl {
    max-width: 36rem;
  }
  .sm\:max-w-2xl {
    max-width: 42rem;
  }
  .sm\:max-w-3xl {
    max-width: 48rem;
  }
  .sm\:max-w-4xl {
    max-width: 56rem;
  }
  .sm\:max-w-5xl {
    max-width: 64rem;
  }
  .sm\:max-w-6xl {
    max-width: 72rem;
  }
}
@media (min-width: 768px) {
  .md\:w-auto {
    width: auto;
  }
  .md\:w-2 {
    width: 0.5rem;
  }
  .md\:w-3 {
    width: 0.75rem;
  }
  .md\:w-4 {
    width: 1rem;
  }
  .md\:w-5 {
    width: 1.25rem;
  }
  .md\:w-6 {
    width: 1.5rem;
  }
  .md\:w-16 {
    width: 4rem;
  }
  .md\:w-24 {
    width: 6rem;
  }
  .md\:w-40,
  .btn-newsletter {
    width: 10rem;
  }
  .md\:w-48 {
    width: 12rem;
  }
  .md\:w-56 {
    width: 14rem;
  }
  .md\:w-64 {
    width: 16rem;
  }
  .md\:w-1\/2 {
    width: 50%;
  }
  .md\:w-1\/3 {
    width: 33.333333%;
  }
  .md\:w-2\/3 {
    width: 66.666667%;
  }
  .md\:w-1\/4 {
    width: 25%;
  }
  .md\:w-2\/4 {
    width: 50%;
  }
  .md\:w-3\/4 {
    width: 75%;
  }
  .md\:w-1\/5 {
    width: 20%;
  }
  .md\:w-2\/5 {
    width: 40%;
  }
  .md\:w-3\/5 {
    width: 60%;
  }
  .md\:w-4\/5 {
    width: 80%;
  }
  .md\:w-1\/6 {
    width: 16.666667%;
  }
  .md\:w-2\/6 {
    width: 33.333333%;
  }
  .md\:w-3\/6 {
    width: 50%;
  }
  .md\:w-4\/6 {
    width: 66.666667%;
  }
  .md\:w-5\/6 {
    width: 83.333333%;
  }
  .md\:w-1\/12 {
    width: 8.333333%;
  }
  .md\:w-2\/12 {
    width: 16.666667%;
  }
  .md\:w-3\/12 {
    width: 25%;
  }
  .md\:w-4\/12 {
    width: 33.333333%;
  }
  .md\:w-5\/12 {
    width: 41.666667%;
  }
  .md\:w-6\/12 {
    width: 50%;
  }
  .md\:w-7\/12 {
    width: 58.333333%;
  }
  .md\:w-8\/12 {
    width: 66.666667%;
  }
  .md\:w-9\/12 {
    width: 75%;
  }
  .md\:w-10\/12 {
    width: 83.333333%;
  }
  .md\:w-11\/12 {
    width: 91.666667%;
  }
  .md\:w-full {
    width: 100%;
  }
  .md\:w-screen {
    width: 100vw;
  }
  .md\:max-w-xs {
    max-width: 20rem;
  }
  .md\:max-w-sm {
    max-width: 24rem;
  }
  .md\:max-w-md {
    max-width: 28rem;
  }
  .md\:max-w-lg {
    max-width: 32rem;
  }
  .md\:max-w-xl {
    max-width: 36rem;
  }
  .md\:max-w-2xl {
    max-width: 42rem;
  }
  .md\:max-w-3xl {
    max-width: 48rem;
  }
  .md\:max-w-4xl {
    max-width: 56rem;
  }
  .md\:max-w-5xl {
    max-width: 64rem;
  }
  .md\:max-w-6xl {
    max-width: 72rem;
  }
}
@media (min-width: 1024px) {
  .lg\:w-auto {
    width: auto;
  }
  .lg\:w-2 {
    width: 0.5rem;
  }
  .lg\:w-3 {
    width: 0.75rem;
  }
  .lg\:w-4 {
    width: 1rem;
  }
  .lg\:w-5 {
    width: 1.25rem;
  }
  .lg\:w-6 {
    width: 1.5rem;
  }
  .lg\:w-16 {
    width: 4rem;
  }
  .lg\:w-24 {
    width: 6rem;
  }
  .lg\:w-40 {
    width: 10rem;
  }
  .lg\:w-48 {
    width: 12rem;
  }
  .lg\:w-56 {
    width: 14rem;
  }
  .lg\:w-64 {
    width: 16rem;
  }
  .lg\:w-1\/2 {
    width: 50%;
  }
  .lg\:w-1\/3 {
    width: 33.333333%;
  }
  .lg\:w-2\/3 {
    width: 66.666667%;
  }
  .lg\:w-1\/4 {
    width: 25%;
  }
  .lg\:w-2\/4 {
    width: 50%;
  }
  .lg\:w-3\/4 {
    width: 75%;
  }
  .lg\:w-1\/5 {
    width: 20%;
  }
  .lg\:w-2\/5 {
    width: 40%;
  }
  .lg\:w-3\/5 {
    width: 60%;
  }
  .lg\:w-4\/5 {
    width: 80%;
  }
  .lg\:w-1\/6 {
    width: 16.666667%;
  }
  .lg\:w-2\/6 {
    width: 33.333333%;
  }
  .lg\:w-3\/6 {
    width: 50%;
  }
  .lg\:w-4\/6 {
    width: 66.666667%;
  }
  .lg\:w-5\/6 {
    width: 83.333333%;
  }
  .lg\:w-1\/12 {
    width: 8.333333%;
  }
  .lg\:w-2\/12 {
    width: 16.666667%;
  }
  .lg\:w-3\/12 {
    width: 25%;
  }
  .lg\:w-4\/12 {
    width: 33.333333%;
  }
  .lg\:w-5\/12 {
    width: 41.666667%;
  }
  .lg\:w-6\/12 {
    width: 50%;
  }
  .lg\:w-7\/12 {
    width: 58.333333%;
  }
  .lg\:w-8\/12 {
    width: 66.666667%;
  }
  .lg\:w-9\/12 {
    width: 75%;
  }
  .lg\:w-10\/12 {
    width: 83.333333%;
  }
  .lg\:w-11\/12 {
    width: 91.666667%;
  }
  .lg\:w-full {
    width: 100%;
  }
  .lg\:w-screen {
    width: 100vw;
  }
  .lg\:max-w-xs {
    max-width: 20rem;
  }
  .lg\:max-w-sm {
    max-width: 24rem;
  }
  .lg\:max-w-md {
    max-width: 28rem;
  }
  .lg\:max-w-lg {
    max-width: 32rem;
  }
  .lg\:max-w-xl {
    max-width: 36rem;
  }
  .lg\:max-w-2xl {
    max-width: 42rem;
  }
  .lg\:max-w-3xl {
    max-width: 48rem;
  }
  .lg\:max-w-4xl {
    max-width: 56rem;
  }
  .lg\:max-w-5xl {
    max-width: 64rem;
  }
  .lg\:max-w-6xl {
    max-width: 72rem;
  }
}
@media (min-width: 1280px) {
  .xl\:w-auto {
    width: auto;
  }
  .xl\:w-2 {
    width: 0.5rem;
  }
  .xl\:w-3 {
    width: 0.75rem;
  }
  .xl\:w-4 {
    width: 1rem;
  }
  .xl\:w-5 {
    width: 1.25rem;
  }
  .xl\:w-6 {
    width: 1.5rem;
  }
  .xl\:w-16 {
    width: 4rem;
  }
  .xl\:w-24 {
    width: 6rem;
  }
  .xl\:w-40 {
    width: 10rem;
  }
  .xl\:w-48 {
    width: 12rem;
  }
  .xl\:w-56 {
    width: 14rem;
  }
  .xl\:w-64 {
    width: 16rem;
  }
  .xl\:w-1\/2 {
    width: 50%;
  }
  .xl\:w-1\/3 {
    width: 33.333333%;
  }
  .xl\:w-2\/3 {
    width: 66.666667%;
  }
  .xl\:w-1\/4 {
    width: 25%;
  }
  .xl\:w-2\/4 {
    width: 50%;
  }
  .xl\:w-3\/4 {
    width: 75%;
  }
  .xl\:w-1\/5 {
    width: 20%;
  }
  .xl\:w-2\/5 {
    width: 40%;
  }
  .xl\:w-3\/5 {
    width: 60%;
  }
  .xl\:w-4\/5 {
    width: 80%;
  }
  .xl\:w-1\/6 {
    width: 16.666667%;
  }
  .xl\:w-2\/6 {
    width: 33.333333%;
  }
  .xl\:w-3\/6 {
    width: 50%;
  }
  .xl\:w-4\/6 {
    width: 66.666667%;
  }
  .xl\:w-5\/6 {
    width: 83.333333%;
  }
  .xl\:w-1\/12 {
    width: 8.333333%;
  }
  .xl\:w-2\/12 {
    width: 16.666667%;
  }
  .xl\:w-3\/12 {
    width: 25%;
  }
  .xl\:w-4\/12 {
    width: 33.333333%;
  }
  .xl\:w-5\/12 {
    width: 41.666667%;
  }
  .xl\:w-6\/12 {
    width: 50%;
  }
  .xl\:w-7\/12 {
    width: 58.333333%;
  }
  .xl\:w-8\/12 {
    width: 66.666667%;
  }
  .xl\:w-9\/12 {
    width: 75%;
  }
  .xl\:w-10\/12 {
    width: 83.333333%;
  }
  .xl\:w-11\/12 {
    width: 91.666667%;
  }
  .xl\:w-full {
    width: 100%;
  }
  .xl\:w-screen {
    width: 100vw;
  }
  .xl\:max-w-xs {
    max-width: 20rem;
  }
  .xl\:max-w-sm {
    max-width: 24rem;
  }
  .xl\:max-w-md {
    max-width: 28rem;
  }
  .xl\:max-w-lg {
    max-width: 32rem;
  }
  .xl\:max-w-xl {
    max-width: 36rem;
  }
  .xl\:max-w-2xl {
    max-width: 42rem;
  }
  .xl\:max-w-3xl {
    max-width: 48rem;
  }
  .xl\:max-w-4xl {
    max-width: 56rem;
  }
  .xl\:max-w-5xl {
    max-width: 64rem;
  }
  .xl\:max-w-6xl {
    max-width: 72rem;
  }
}
.p-20 {
  padding: 5rem;
}
.p-19 {
  padding: 4.75rem;
}
.p-18 {
  padding: 4.5rem;
}
.p-17 {
  padding: 4.25rem;
}
.p-16 {
  padding: 4rem;
}
.p-15 {
  padding: 3.75rem;
}
.p-14 {
  padding: 3.5rem;
}
.p-13 {
  padding: 3.25rem;
}
.p-12 {
  padding: 3rem;
}
.p-11 {
  padding: 2.75rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-9 {
  padding: 2.25rem;
}
.p-8 {
  padding: 2rem;
}
.p-7 {
  padding: 1.75rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-4 {
  padding: 1rem;
}
.p-3,
.newsletter-form input,
.header-section span {
  padding: 0.75rem;
}
.p-2,
.btn-download-inline,
.btn-body.btn-release,
.btn-block-white,
.btn-newsletter {
  padding: 0.5rem;
}
.p-1,
.social-link-large span,
.social-link span,
.btn-inline,
.form-select,
.newsletter-form,
.tabs-nav label,
.tabs-nav #channel-header {
  padding: 0.25rem;
}
.p-0,
.no-padding p {
  padding: 0rem;
}
.pt-20 {
  padding-top: 5rem;
}
.pt-19 {
  padding-top: 4.75rem;
}
.pt-18 {
  padding-top: 4.5rem;
}
.pt-17 {
  padding-top: 4.25rem;
}
.pt-16 {
  padding-top: 4rem;
}
.pt-15 {
  padding-top: 3.75rem;
}
.pt-14 {
  padding-top: 3.5rem;
}
.pt-13 {
  padding-top: 3.25rem;
}
.pt-12 {
  padding-top: 3rem;
}
.pt-11 {
  padding-top: 2.75rem;
}
.pt-10 {
  padding-top: 2.5rem;
}
.pt-9 {
  padding-top: 2.25rem;
}
.pt-8 {
  padding-top: 2rem;
}
.pt-7 {
  padding-top: 1.75rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.pt-5 {
  padding-top: 1.25rem;
}
.pt-4,
.btn-download,
.btn-body,
.btn-donate-and-download {
  padding-top: 1rem;
}
.pt-3,
.btn-donate.btn-donate-whatsnew,
.btn-donate-lg,
.btn-join,
.btn-beta,
.btn-daily,
.btn-link,
.btn-banner {
  padding-top: 0.75rem;
}
.pt-2,
.nav-link,
.btn-donate {
  padding-top: 0.5rem;
}
.pt-1,
.nav-footer .nav-link,
.social-link,
.btn-beta.btn-beta-pill,
.modal form .amount-selection label,
.social-link-large {
  padding-top: 0.25rem;
}
.pt-0,
.modal form .amount-selection label:last-child {
  padding-top: 0rem;
}
.pr-20 {
  padding-right: 5rem;
}
.pr-19 {
  padding-right: 4.75rem;
}
.pr-18 {
  padding-right: 4.5rem;
}
.pr-17 {
  padding-right: 4.25rem;
}
.pr-16 {
  padding-right: 4rem;
}
.pr-15 {
  padding-right: 3.75rem;
}
.pr-14 {
  padding-right: 3.5rem;
}
.pr-13 {
  padding-right: 3.25rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pr-11 {
  padding-right: 2.75rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-9,
.btn-body {
  padding-right: 2.25rem;
}
.pr-8 {
  padding-right: 2rem;
}
.pr-7,
.btn-download {
  padding-right: 1.75rem;
}
.pr-6,
.btn-join,
.btn-beta,
.btn-daily,
.btn-donate-and-download,
.form-select {
  padding-right: 1.5rem;
}
.pr-5,
.btn-donate.btn-donate-whatsnew,
.btn-donate-lg {
  padding-right: 1.25rem;
}
.pr-4,
.btn-body span,
.btn-link,
.btn-banner {
  padding-right: 1rem;
}
.pr-3,
.btn-donate,
.btn-newsletter,
.modal form .amount-selection label {
  padding-right: 0.75rem;
}
.pr-2,
.social-link,
.btn-inline,
.btn-beta.btn-beta-pill,
.social-link-large {
  padding-right: 0.5rem;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pr-0,
.modal form .amount-selection label:last-child {
  padding-right: 0rem;
}
.pb-20 {
  padding-bottom: 5rem;
}
.pb-19 {
  padding-bottom: 4.75rem;
}
.pb-18 {
  padding-bottom: 4.5rem;
}
.pb-17 {
  padding-bottom: 4.25rem;
}
.pb-16 {
  padding-bottom: 4rem;
}
.pb-15 {
  padding-bottom: 3.75rem;
}
.pb-14 {
  padding-bottom: 3.5rem;
}
.pb-13 {
  padding-bottom: 3.25rem;
}
.pb-12 {
  padding-bottom: 3rem;
}
.pb-11 {
  padding-bottom: 2.75rem;
}
.pb-10 {
  padding-bottom: 2.5rem;
}
.pb-9 {
  padding-bottom: 2.25rem;
}
.pb-8 {
  padding-bottom: 2rem;
}
.pb-7 {
  padding-bottom: 1.75rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pb-5 {
  padding-bottom: 1.25rem;
}
.pb-4,
.btn-download,
.btn-body,
.btn-donate-and-download {
  padding-bottom: 1rem;
}
.pb-3,
.btn-donate.btn-donate-whatsnew,
.btn-donate-lg,
.btn-join,
.btn-beta,
.btn-daily,
.btn-link,
.btn-banner {
  padding-bottom: 0.75rem;
}
.pb-2,
.nav-link,
.btn-donate,
.ways-to-give-list-info li {
  padding-bottom: 0.5rem;
}
.pb-1,
.nav-footer .nav-link,
.social-link,
.btn-beta.btn-beta-pill,
.modal form .amount-selection label,
.social-link-large {
  padding-bottom: 0.25rem;
}
.pb-0,
.modal form .amount-selection label:last-child {
  padding-bottom: 0rem;
}
.pl-20 {
  padding-left: 5rem;
}
.pl-19 {
  padding-left: 4.75rem;
}
.pl-18 {
  padding-left: 4.5rem;
}
.pl-17 {
  padding-left: 4.25rem;
}
.pl-16 {
  padding-left: 4rem;
}
.pl-15 {
  padding-left: 3.75rem;
}
.pl-14 {
  padding-left: 3.5rem;
}
.pl-13 {
  padding-left: 3.25rem;
}
.pl-12 {
  padding-left: 3rem;
}
.pl-11 {
  padding-left: 2.75rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-9 {
  padding-left: 2.25rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pl-7,
.btn-download {
  padding-left: 1.75rem;
}
.pl-6,
.btn-join,
.btn-beta,
.btn-daily {
  padding-left: 1.5rem;
}
.pl-5,
.btn-donate.btn-donate-whatsnew,
.btn-donate-lg,
.btn-body,
.btn-donate-and-download {
  padding-left: 1.25rem;
}
.pl-4,
.btn-link,
.btn-banner {
  padding-left: 1rem;
}
.pl-3,
.btn-donate,
.btn-newsletter,
.modal form .amount-selection label {
  padding-left: 0.75rem;
}
.pl-2,
.btn-inline,
.btn-beta.btn-beta-pill,
.modal form .amount-selection label:last-child #amount-other {
  padding-left: 0.5rem;
}
.pl-1,
.social-link,
.social-link-large {
  padding-left: 0.25rem;
}
.pl-0,
.ways-to-give-list-info {
  padding-left: 0rem;
}
.m-20 {
  margin: 5rem;
}
.m-19 {
  margin: 4.75rem;
}
.m-18 {
  margin: 4.5rem;
}
.m-17 {
  margin: 4.25rem;
}
.m-16 {
  margin: 4rem;
}
.m-15 {
  margin: 3.75rem;
}
.m-14 {
  margin: 3.5rem;
}
.m-13 {
  margin: 3.25rem;
}
.m-12 {
  margin: 3rem;
}
.m-11 {
  margin: 2.75rem;
}
.m-10 {
  margin: 2.5rem;
}
.m-9 {
  margin: 2.25rem;
}
.m-8 {
  margin: 2rem;
}
.m-7 {
  margin: 1.75rem;
}
.m-6 {
  margin: 1.5rem;
}
.m-5 {
  margin: 1.25rem;
}
.m-4 {
  margin: 1rem;
}
.m-3 {
  margin: 0.75rem;
}
.m-2 {
  margin: 0.5rem;
}
.m-1,
.btn-download-inline,
.btn-download-link,
.btn-link,
.btn-banner {
  margin: 0.25rem;
}
.m-0,
.no-padding p {
  margin: 0rem;
}
.-m-20 {
  margin: -5rem;
}
.-m-19 {
  margin: -4.75rem;
}
.-m-18 {
  margin: -4.5rem;
}
.-m-17 {
  margin: -4.25rem;
}
.-m-16 {
  margin: -4rem;
}
.-m-15 {
  margin: -3.75rem;
}
.-m-14 {
  margin: -3.5rem;
}
.-m-13 {
  margin: -3.25rem;
}
.-m-12 {
  margin: -3rem;
}
.-m-11 {
  margin: -2.75rem;
}
.-m-10 {
  margin: -2.5rem;
}
.-m-9 {
  margin: -2.25rem;
}
.-m-8 {
  margin: -2rem;
}
.-m-7 {
  margin: -1.75rem;
}
.-m-6 {
  margin: -1.5rem;
}
.-m-5 {
  margin: -1.25rem;
}
.-m-4 {
  margin: -1rem;
}
.-m-3 {
  margin: -0.75rem;
}
.-m-2 {
  margin: -0.5rem;
}
.-m-1 {
  margin: -0.25rem;
}
.-m-0 {
  margin: 0rem;
}
.mt-20 {
  margin-top: 5rem;
}
.mt-19 {
  margin-top: 4.75rem;
}
.mt-18 {
  margin-top: 4.5rem;
}
.mt-17 {
  margin-top: 4.25rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-15 {
  margin-top: 3.75rem;
}
.mt-14 {
  margin-top: 3.5rem;
}
.mt-13 {
  margin-top: 3.25rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mt-11 {
  margin-top: 2.75rem;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-9 {
  margin-top: 2.25rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-7 {
  margin-top: 1.75rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-4,
.no-js .retry-download .retry-text {
  margin-top: 1rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-2,
.nav-link,
.markup-page hr {
  margin-top: 0.5rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-0,
.blog-link,
.blog-body,
.faq-entry .faq-question,
.header-section,
.subheader-section,
.markup-page ul,
.markup-page h2,
.markup-page h3 {
  margin-top: 0rem;
}
.-mt-20 {
  margin-top: -5rem;
}
.-mt-19 {
  margin-top: -4.75rem;
}
.-mt-18 {
  margin-top: -4.5rem;
}
.-mt-17 {
  margin-top: -4.25rem;
}
.-mt-16 {
  margin-top: -4rem;
}
.-mt-15 {
  margin-top: -3.75rem;
}
.-mt-14 {
  margin-top: -3.5rem;
}
.-mt-13 {
  margin-top: -3.25rem;
}
.-mt-12 {
  margin-top: -3rem;
}
.-mt-11 {
  margin-top: -2.75rem;
}
.-mt-10 {
  margin-top: -2.5rem;
}
.-mt-9 {
  margin-top: -2.25rem;
}
.-mt-8 {
  margin-top: -2rem;
}
.-mt-7 {
  margin-top: -1.75rem;
}
.-mt-6 {
  margin-top: -1.5rem;
}
.-mt-5 {
  margin-top: -1.25rem;
}
.-mt-4 {
  margin-top: -1rem;
}
.-mt-3 {
  margin-top: -0.75rem;
}
.-mt-2 {
  margin-top: -0.5rem;
}
.-mt-1 {
  margin-top: -0.25rem;
}
.-mt-0 {
  margin-top: 0rem;
}
.mr-20 {
  margin-right: 5rem;
}
.mr-19 {
  margin-right: 4.75rem;
}
.mr-18 {
  margin-right: 4.5rem;
}
.mr-17 {
  margin-right: 4.25rem;
}
.mr-16 {
  margin-right: 4rem;
}
.mr-15 {
  margin-right: 3.75rem;
}
.mr-14 {
  margin-right: 3.5rem;
}
.mr-13 {
  margin-right: 3.25rem;
}
.mr-12 {
  margin-right: 3rem;
}
.mr-11 {
  margin-right: 2.75rem;
}
.mr-10 {
  margin-right: 2.5rem;
}
.mr-9 {
  margin-right: 2.25rem;
}
.mr-8,
.faq-entry .faq-answer {
  margin-right: 2rem;
}
.mr-7 {
  margin-right: 1.75rem;
}
.mr-6,
.no-js .retry-download .retry-text p,
.header-section span {
  margin-right: 1.5rem;
}
.mr-5 {
  margin-right: 1.25rem;
}
.mr-4,
.nav-link,
.btn-donate-and-download {
  margin-right: 1rem;
}
.mr-3,
.nav-footer .nav-link,
.btn-donate-and-download .donate-heart {
  margin-right: 0.75rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-1,
.small-link,
.social-link-large span,
.social-link,
.social-link span,
.social-link-large {
  margin-right: 0.25rem;
}
.mr-0,
.btn-donate.btn-donate-whatsnew,
.btn-donate-lg {
  margin-right: 0rem;
}
.-mr-20 {
  margin-right: -5rem;
}
.-mr-19 {
  margin-right: -4.75rem;
}
.-mr-18 {
  margin-right: -4.5rem;
}
.-mr-17 {
  margin-right: -4.25rem;
}
.-mr-16 {
  margin-right: -4rem;
}
.-mr-15 {
  margin-right: -3.75rem;
}
.-mr-14 {
  margin-right: -3.5rem;
}
.-mr-13 {
  margin-right: -3.25rem;
}
.-mr-12 {
  margin-right: -3rem;
}
.-mr-11 {
  margin-right: -2.75rem;
}
.-mr-10 {
  margin-right: -2.5rem;
}
.-mr-9 {
  margin-right: -2.25rem;
}
.-mr-8 {
  margin-right: -2rem;
}
.-mr-7 {
  margin-right: -1.75rem;
}
.-mr-6 {
  margin-right: -1.5rem;
}
.-mr-5 {
  margin-right: -1.25rem;
}
.-mr-4 {
  margin-right: -1rem;
}
.-mr-3 {
  margin-right: -0.75rem;
}
.-mr-2 {
  margin-right: -0.5rem;
}
.-mr-1 {
  margin-right: -0.25rem;
}
.-mr-0 {
  margin-right: 0rem;
}
.mb-20 {
  margin-bottom: 5rem;
}
.mb-19 {
  margin-bottom: 4.75rem;
}
.mb-18 {
  margin-bottom: 4.5rem;
}
.mb-17 {
  margin-bottom: 4.25rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-15 {
  margin-bottom: 3.75rem;
}
.mb-14 {
  margin-bottom: 3.5rem;
}
.mb-13 {
  margin-bottom: 3.25rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-11 {
  margin-bottom: 2.75rem;
}
.mb-10,
.markup-page hr {
  margin-bottom: 2.5rem;
}
.mb-9 {
  margin-bottom: 2.25rem;
}
.mb-8,
.markup-page ul {
  margin-bottom: 2rem;
}
.mb-7 {
  margin-bottom: 1.75rem;
}
.mb-6,
.blog-body {
  margin-bottom: 1.5rem;
}
.mb-5,
.btn-download,
.btn-join,
.btn-beta,
.btn-daily {
  margin-bottom: 1.25rem;
}
.mb-4,
.social-link-large,
.blog-link,
.faq-entry .faq-question,
.header-section,
.subheader-section,
.markup-page h2,
.markup-page h3 {
  margin-bottom: 1rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-2,
.nav-link {
  margin-bottom: 0.5rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-0,
.btn-beta.btn-beta-pill {
  margin-bottom: 0rem;
}
.-mb-20 {
  margin-bottom: -5rem;
}
.-mb-19 {
  margin-bottom: -4.75rem;
}
.-mb-18 {
  margin-bottom: -4.5rem;
}
.-mb-17 {
  margin-bottom: -4.25rem;
}
.-mb-16 {
  margin-bottom: -4rem;
}
.-mb-15 {
  margin-bottom: -3.75rem;
}
.-mb-14 {
  margin-bottom: -3.5rem;
}
.-mb-13 {
  margin-bottom: -3.25rem;
}
.-mb-12 {
  margin-bottom: -3rem;
}
.-mb-11 {
  margin-bottom: -2.75rem;
}
.-mb-10 {
  margin-bottom: -2.5rem;
}
.-mb-9 {
  margin-bottom: -2.25rem;
}
.-mb-8 {
  margin-bottom: -2rem;
}
.-mb-7 {
  margin-bottom: -1.75rem;
}
.-mb-6 {
  margin-bottom: -1.5rem;
}
.-mb-5 {
  margin-bottom: -1.25rem;
}
.-mb-4 {
  margin-bottom: -1rem;
}
.-mb-3 {
  margin-bottom: -0.75rem;
}
.-mb-2 {
  margin-bottom: -0.5rem;
}
.-mb-1 {
  margin-bottom: -0.25rem;
}
.-mb-0 {
  margin-bottom: 0rem;
}
.ml-20 {
  margin-left: 5rem;
}
.ml-19 {
  margin-left: 4.75rem;
}
.ml-18 {
  margin-left: 4.5rem;
}
.ml-17 {
  margin-left: 4.25rem;
}
.ml-16 {
  margin-left: 4rem;
}
.ml-15 {
  margin-left: 3.75rem;
}
.ml-14 {
  margin-left: 3.5rem;
}
.ml-13 {
  margin-left: 3.25rem;
}
.ml-12 {
  margin-left: 3rem;
}
.ml-11 {
  margin-left: 2.75rem;
}
.ml-10 {
  margin-left: 2.5rem;
}
.ml-9 {
  margin-left: 2.25rem;
}
.ml-8,
.faq-entry .faq-answer {
  margin-left: 2rem;
}
.ml-7 {
  margin-left: 1.75rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.ml-5 {
  margin-left: 1.25rem;
}
.ml-4,
.nav-link {
  margin-left: 1rem;
}
.ml-3,
.nav-footer .nav-link,
.modal footer ul > li:not(:last-child)::after {
  margin-left: 0.75rem;
}
.ml-2,
.btn-inline,
.tabs-nav li ~ li {
  margin-left: 0.5rem;
}
.ml-1,
.small-link,
.social-link,
.social-link-large {
  margin-left: 0.25rem;
}
.ml-0,
.btn-donate.btn-donate-whatsnew,
.btn-donate-lg {
  margin-left: 0rem;
}
.-ml-20 {
  margin-left: -5rem;
}
.-ml-19 {
  margin-left: -4.75rem;
}
.-ml-18 {
  margin-left: -4.5rem;
}
.-ml-17 {
  margin-left: -4.25rem;
}
.-ml-16 {
  margin-left: -4rem;
}
.-ml-15 {
  margin-left: -3.75rem;
}
.-ml-14 {
  margin-left: -3.5rem;
}
.-ml-13 {
  margin-left: -3.25rem;
}
.-ml-12 {
  margin-left: -3rem;
}
.-ml-11 {
  margin-left: -2.75rem;
}
.-ml-10 {
  margin-left: -2.5rem;
}
.-ml-9 {
  margin-left: -2.25rem;
}
.-ml-8 {
  margin-left: -2rem;
}
.-ml-7 {
  margin-left: -1.75rem;
}
.-ml-6 {
  margin-left: -1.5rem;
}
.-ml-5 {
  margin-left: -1.25rem;
}
.-ml-4 {
  margin-left: -1rem;
}
.-ml-3 {
  margin-left: -0.75rem;
}
.-ml-2 {
  margin-left: -0.5rem;
}
.-ml-1 {
  margin-left: -0.25rem;
}
.-ml-0 {
  margin-left: 0rem;
}
.mx-auto,
.no-js .retry-download .retry-text,
.no-js .retry-download .retry-button {
  margin-left: auto;
  margin-right: auto;
}
.my-auto,
#amount-cancel p,
.btn-donate-and-download p {
  margin-top: auto;
  margin-bottom: auto;
}
@media (min-width: 640px) {
  .sm\:p-20 {
    padding: 5rem;
  }
  .sm\:p-19 {
    padding: 4.75rem;
  }
  .sm\:p-18 {
    padding: 4.5rem;
  }
  .sm\:p-17 {
    padding: 4.25rem;
  }
  .sm\:p-16 {
    padding: 4rem;
  }
  .sm\:p-15 {
    padding: 3.75rem;
  }
  .sm\:p-14 {
    padding: 3.5rem;
  }
  .sm\:p-13 {
    padding: 3.25rem;
  }
  .sm\:p-12 {
    padding: 3rem;
  }
  .sm\:p-11 {
    padding: 2.75rem;
  }
  .sm\:p-10 {
    padding: 2.5rem;
  }
  .sm\:p-9 {
    padding: 2.25rem;
  }
  .sm\:p-8 {
    padding: 2rem;
  }
  .sm\:p-7 {
    padding: 1.75rem;
  }
  .sm\:p-6 {
    padding: 1.5rem;
  }
  .sm\:p-5 {
    padding: 1.25rem;
  }
  .sm\:p-4 {
    padding: 1rem;
  }
  .sm\:p-3 {
    padding: 0.75rem;
  }
  .sm\:p-2 {
    padding: 0.5rem;
  }
  .sm\:p-1 {
    padding: 0.25rem;
  }
  .sm\:p-0 {
    padding: 0rem;
  }
  .sm\:pt-20 {
    padding-top: 5rem;
  }
  .sm\:pt-19 {
    padding-top: 4.75rem;
  }
  .sm\:pt-18 {
    padding-top: 4.5rem;
  }
  .sm\:pt-17 {
    padding-top: 4.25rem;
  }
  .sm\:pt-16 {
    padding-top: 4rem;
  }
  .sm\:pt-15 {
    padding-top: 3.75rem;
  }
  .sm\:pt-14 {
    padding-top: 3.5rem;
  }
  .sm\:pt-13 {
    padding-top: 3.25rem;
  }
  .sm\:pt-12 {
    padding-top: 3rem;
  }
  .sm\:pt-11 {
    padding-top: 2.75rem;
  }
  .sm\:pt-10 {
    padding-top: 2.5rem;
  }
  .sm\:pt-9 {
    padding-top: 2.25rem;
  }
  .sm\:pt-8 {
    padding-top: 2rem;
  }
  .sm\:pt-7 {
    padding-top: 1.75rem;
  }
  .sm\:pt-6 {
    padding-top: 1.5rem;
  }
  .sm\:pt-5 {
    padding-top: 1.25rem;
  }
  .sm\:pt-4 {
    padding-top: 1rem;
  }
  .sm\:pt-3 {
    padding-top: 0.75rem;
  }
  .sm\:pt-2 {
    padding-top: 0.5rem;
  }
  .sm\:pt-1 {
    padding-top: 0.25rem;
  }
  .sm\:pt-0 {
    padding-top: 0rem;
  }
  .sm\:pr-20 {
    padding-right: 5rem;
  }
  .sm\:pr-19 {
    padding-right: 4.75rem;
  }
  .sm\:pr-18 {
    padding-right: 4.5rem;
  }
  .sm\:pr-17 {
    padding-right: 4.25rem;
  }
  .sm\:pr-16 {
    padding-right: 4rem;
  }
  .sm\:pr-15 {
    padding-right: 3.75rem;
  }
  .sm\:pr-14 {
    padding-right: 3.5rem;
  }
  .sm\:pr-13 {
    padding-right: 3.25rem;
  }
  .sm\:pr-12 {
    padding-right: 3rem;
  }
  .sm\:pr-11 {
    padding-right: 2.75rem;
  }
  .sm\:pr-10 {
    padding-right: 2.5rem;
  }
  .sm\:pr-9 {
    padding-right: 2.25rem;
  }
  .sm\:pr-8 {
    padding-right: 2rem;
  }
  .sm\:pr-7 {
    padding-right: 1.75rem;
  }
  .sm\:pr-6 {
    padding-right: 1.5rem;
  }
  .sm\:pr-5 {
    padding-right: 1.25rem;
  }
  .sm\:pr-4 {
    padding-right: 1rem;
  }
  .sm\:pr-3 {
    padding-right: 0.75rem;
  }
  .sm\:pr-2 {
    padding-right: 0.5rem;
  }
  .sm\:pr-1 {
    padding-right: 0.25rem;
  }
  .sm\:pr-0 {
    padding-right: 0rem;
  }
  .sm\:pb-20 {
    padding-bottom: 5rem;
  }
  .sm\:pb-19 {
    padding-bottom: 4.75rem;
  }
  .sm\:pb-18 {
    padding-bottom: 4.5rem;
  }
  .sm\:pb-17 {
    padding-bottom: 4.25rem;
  }
  .sm\:pb-16 {
    padding-bottom: 4rem;
  }
  .sm\:pb-15 {
    padding-bottom: 3.75rem;
  }
  .sm\:pb-14 {
    padding-bottom: 3.5rem;
  }
  .sm\:pb-13 {
    padding-bottom: 3.25rem;
  }
  .sm\:pb-12 {
    padding-bottom: 3rem;
  }
  .sm\:pb-11 {
    padding-bottom: 2.75rem;
  }
  .sm\:pb-10 {
    padding-bottom: 2.5rem;
  }
  .sm\:pb-9 {
    padding-bottom: 2.25rem;
  }
  .sm\:pb-8 {
    padding-bottom: 2rem;
  }
  .sm\:pb-7 {
    padding-bottom: 1.75rem;
  }
  .sm\:pb-6 {
    padding-bottom: 1.5rem;
  }
  .sm\:pb-5 {
    padding-bottom: 1.25rem;
  }
  .sm\:pb-4 {
    padding-bottom: 1rem;
  }
  .sm\:pb-3 {
    padding-bottom: 0.75rem;
  }
  .sm\:pb-2 {
    padding-bottom: 0.5rem;
  }
  .sm\:pb-1 {
    padding-bottom: 0.25rem;
  }
  .sm\:pb-0 {
    padding-bottom: 0rem;
  }
  .sm\:pl-20 {
    padding-left: 5rem;
  }
  .sm\:pl-19 {
    padding-left: 4.75rem;
  }
  .sm\:pl-18 {
    padding-left: 4.5rem;
  }
  .sm\:pl-17 {
    padding-left: 4.25rem;
  }
  .sm\:pl-16 {
    padding-left: 4rem;
  }
  .sm\:pl-15 {
    padding-left: 3.75rem;
  }
  .sm\:pl-14 {
    padding-left: 3.5rem;
  }
  .sm\:pl-13 {
    padding-left: 3.25rem;
  }
  .sm\:pl-12 {
    padding-left: 3rem;
  }
  .sm\:pl-11 {
    padding-left: 2.75rem;
  }
  .sm\:pl-10 {
    padding-left: 2.5rem;
  }
  .sm\:pl-9 {
    padding-left: 2.25rem;
  }
  .sm\:pl-8 {
    padding-left: 2rem;
  }
  .sm\:pl-7 {
    padding-left: 1.75rem;
  }
  .sm\:pl-6 {
    padding-left: 1.5rem;
  }
  .sm\:pl-5 {
    padding-left: 1.25rem;
  }
  .sm\:pl-4 {
    padding-left: 1rem;
  }
  .sm\:pl-3 {
    padding-left: 0.75rem;
  }
  .sm\:pl-2 {
    padding-left: 0.5rem;
  }
  .sm\:pl-1 {
    padding-left: 0.25rem;
  }
  .sm\:pl-0 {
    padding-left: 0rem;
  }
  .sm\:m-20 {
    margin: 5rem;
  }
  .sm\:m-19 {
    margin: 4.75rem;
  }
  .sm\:m-18 {
    margin: 4.5rem;
  }
  .sm\:m-17 {
    margin: 4.25rem;
  }
  .sm\:m-16 {
    margin: 4rem;
  }
  .sm\:m-15 {
    margin: 3.75rem;
  }
  .sm\:m-14 {
    margin: 3.5rem;
  }
  .sm\:m-13 {
    margin: 3.25rem;
  }
  .sm\:m-12 {
    margin: 3rem;
  }
  .sm\:m-11 {
    margin: 2.75rem;
  }
  .sm\:m-10 {
    margin: 2.5rem;
  }
  .sm\:m-9 {
    margin: 2.25rem;
  }
  .sm\:m-8 {
    margin: 2rem;
  }
  .sm\:m-7 {
    margin: 1.75rem;
  }
  .sm\:m-6 {
    margin: 1.5rem;
  }
  .sm\:m-5 {
    margin: 1.25rem;
  }
  .sm\:m-4 {
    margin: 1rem;
  }
  .sm\:m-3 {
    margin: 0.75rem;
  }
  .sm\:m-2 {
    margin: 0.5rem;
  }
  .sm\:m-1 {
    margin: 0.25rem;
  }
  .sm\:m-0 {
    margin: 0rem;
  }
  .sm\:-m-20 {
    margin: -5rem;
  }
  .sm\:-m-19 {
    margin: -4.75rem;
  }
  .sm\:-m-18 {
    margin: -4.5rem;
  }
  .sm\:-m-17 {
    margin: -4.25rem;
  }
  .sm\:-m-16 {
    margin: -4rem;
  }
  .sm\:-m-15 {
    margin: -3.75rem;
  }
  .sm\:-m-14 {
    margin: -3.5rem;
  }
  .sm\:-m-13 {
    margin: -3.25rem;
  }
  .sm\:-m-12 {
    margin: -3rem;
  }
  .sm\:-m-11 {
    margin: -2.75rem;
  }
  .sm\:-m-10 {
    margin: -2.5rem;
  }
  .sm\:-m-9 {
    margin: -2.25rem;
  }
  .sm\:-m-8 {
    margin: -2rem;
  }
  .sm\:-m-7 {
    margin: -1.75rem;
  }
  .sm\:-m-6 {
    margin: -1.5rem;
  }
  .sm\:-m-5 {
    margin: -1.25rem;
  }
  .sm\:-m-4 {
    margin: -1rem;
  }
  .sm\:-m-3 {
    margin: -0.75rem;
  }
  .sm\:-m-2 {
    margin: -0.5rem;
  }
  .sm\:-m-1 {
    margin: -0.25rem;
  }
  .sm\:-m-0 {
    margin: 0rem;
  }
  .sm\:mt-20 {
    margin-top: 5rem;
  }
  .sm\:mt-19 {
    margin-top: 4.75rem;
  }
  .sm\:mt-18 {
    margin-top: 4.5rem;
  }
  .sm\:mt-17 {
    margin-top: 4.25rem;
  }
  .sm\:mt-16 {
    margin-top: 4rem;
  }
  .sm\:mt-15 {
    margin-top: 3.75rem;
  }
  .sm\:mt-14 {
    margin-top: 3.5rem;
  }
  .sm\:mt-13 {
    margin-top: 3.25rem;
  }
  .sm\:mt-12 {
    margin-top: 3rem;
  }
  .sm\:mt-11 {
    margin-top: 2.75rem;
  }
  .sm\:mt-10 {
    margin-top: 2.5rem;
  }
  .sm\:mt-9 {
    margin-top: 2.25rem;
  }
  .sm\:mt-8 {
    margin-top: 2rem;
  }
  .sm\:mt-7 {
    margin-top: 1.75rem;
  }
  .sm\:mt-6 {
    margin-top: 1.5rem;
  }
  .sm\:mt-5 {
    margin-top: 1.25rem;
  }
  .sm\:mt-4 {
    margin-top: 1rem;
  }
  .sm\:mt-3 {
    margin-top: 0.75rem;
  }
  .sm\:mt-2 {
    margin-top: 0.5rem;
  }
  .sm\:mt-1 {
    margin-top: 0.25rem;
  }
  .sm\:mt-0 {
    margin-top: 0rem;
  }
  .sm\:-mt-20 {
    margin-top: -5rem;
  }
  .sm\:-mt-19 {
    margin-top: -4.75rem;
  }
  .sm\:-mt-18 {
    margin-top: -4.5rem;
  }
  .sm\:-mt-17 {
    margin-top: -4.25rem;
  }
  .sm\:-mt-16 {
    margin-top: -4rem;
  }
  .sm\:-mt-15 {
    margin-top: -3.75rem;
  }
  .sm\:-mt-14 {
    margin-top: -3.5rem;
  }
  .sm\:-mt-13 {
    margin-top: -3.25rem;
  }
  .sm\:-mt-12 {
    margin-top: -3rem;
  }
  .sm\:-mt-11 {
    margin-top: -2.75rem;
  }
  .sm\:-mt-10 {
    margin-top: -2.5rem;
  }
  .sm\:-mt-9 {
    margin-top: -2.25rem;
  }
  .sm\:-mt-8 {
    margin-top: -2rem;
  }
  .sm\:-mt-7 {
    margin-top: -1.75rem;
  }
  .sm\:-mt-6 {
    margin-top: -1.5rem;
  }
  .sm\:-mt-5 {
    margin-top: -1.25rem;
  }
  .sm\:-mt-4 {
    margin-top: -1rem;
  }
  .sm\:-mt-3 {
    margin-top: -0.75rem;
  }
  .sm\:-mt-2 {
    margin-top: -0.5rem;
  }
  .sm\:-mt-1 {
    margin-top: -0.25rem;
  }
  .sm\:-mt-0 {
    margin-top: 0rem;
  }
  .sm\:mr-20 {
    margin-right: 5rem;
  }
  .sm\:mr-19 {
    margin-right: 4.75rem;
  }
  .sm\:mr-18 {
    margin-right: 4.5rem;
  }
  .sm\:mr-17 {
    margin-right: 4.25rem;
  }
  .sm\:mr-16 {
    margin-right: 4rem;
  }
  .sm\:mr-15 {
    margin-right: 3.75rem;
  }
  .sm\:mr-14 {
    margin-right: 3.5rem;
  }
  .sm\:mr-13 {
    margin-right: 3.25rem;
  }
  .sm\:mr-12 {
    margin-right: 3rem;
  }
  .sm\:mr-11 {
    margin-right: 2.75rem;
  }
  .sm\:mr-10 {
    margin-right: 2.5rem;
  }
  .sm\:mr-9 {
    margin-right: 2.25rem;
  }
  .sm\:mr-8 {
    margin-right: 2rem;
  }
  .sm\:mr-7 {
    margin-right: 1.75rem;
  }
  .sm\:mr-6 {
    margin-right: 1.5rem;
  }
  .sm\:mr-5 {
    margin-right: 1.25rem;
  }
  .sm\:mr-4 {
    margin-right: 1rem;
  }
  .sm\:mr-3 {
    margin-right: 0.75rem;
  }
  .sm\:mr-2 {
    margin-right: 0.5rem;
  }
  .sm\:mr-1 {
    margin-right: 0.25rem;
  }
  .sm\:mr-0 {
    margin-right: 0rem;
  }
  .sm\:-mr-20 {
    margin-right: -5rem;
  }
  .sm\:-mr-19 {
    margin-right: -4.75rem;
  }
  .sm\:-mr-18 {
    margin-right: -4.5rem;
  }
  .sm\:-mr-17 {
    margin-right: -4.25rem;
  }
  .sm\:-mr-16 {
    margin-right: -4rem;
  }
  .sm\:-mr-15 {
    margin-right: -3.75rem;
  }
  .sm\:-mr-14 {
    margin-right: -3.5rem;
  }
  .sm\:-mr-13 {
    margin-right: -3.25rem;
  }
  .sm\:-mr-12 {
    margin-right: -3rem;
  }
  .sm\:-mr-11 {
    margin-right: -2.75rem;
  }
  .sm\:-mr-10 {
    margin-right: -2.5rem;
  }
  .sm\:-mr-9 {
    margin-right: -2.25rem;
  }
  .sm\:-mr-8 {
    margin-right: -2rem;
  }
  .sm\:-mr-7 {
    margin-right: -1.75rem;
  }
  .sm\:-mr-6 {
    margin-right: -1.5rem;
  }
  .sm\:-mr-5 {
    margin-right: -1.25rem;
  }
  .sm\:-mr-4 {
    margin-right: -1rem;
  }
  .sm\:-mr-3 {
    margin-right: -0.75rem;
  }
  .sm\:-mr-2 {
    margin-right: -0.5rem;
  }
  .sm\:-mr-1 {
    margin-right: -0.25rem;
  }
  .sm\:-mr-0 {
    margin-right: 0rem;
  }
  .sm\:mb-20 {
    margin-bottom: 5rem;
  }
  .sm\:mb-19 {
    margin-bottom: 4.75rem;
  }
  .sm\:mb-18 {
    margin-bottom: 4.5rem;
  }
  .sm\:mb-17 {
    margin-bottom: 4.25rem;
  }
  .sm\:mb-16 {
    margin-bottom: 4rem;
  }
  .sm\:mb-15 {
    margin-bottom: 3.75rem;
  }
  .sm\:mb-14 {
    margin-bottom: 3.5rem;
  }
  .sm\:mb-13 {
    margin-bottom: 3.25rem;
  }
  .sm\:mb-12 {
    margin-bottom: 3rem;
  }
  .sm\:mb-11 {
    margin-bottom: 2.75rem;
  }
  .sm\:mb-10 {
    margin-bottom: 2.5rem;
  }
  .sm\:mb-9 {
    margin-bottom: 2.25rem;
  }
  .sm\:mb-8 {
    margin-bottom: 2rem;
  }
  .sm\:mb-7 {
    margin-bottom: 1.75rem;
  }
  .sm\:mb-6 {
    margin-bottom: 1.5rem;
  }
  .sm\:mb-5 {
    margin-bottom: 1.25rem;
  }
  .sm\:mb-4 {
    margin-bottom: 1rem;
  }
  .sm\:mb-3 {
    margin-bottom: 0.75rem;
  }
  .sm\:mb-2 {
    margin-bottom: 0.5rem;
  }
  .sm\:mb-1 {
    margin-bottom: 0.25rem;
  }
  .sm\:mb-0 {
    margin-bottom: 0rem;
  }
  .sm\:-mb-20 {
    margin-bottom: -5rem;
  }
  .sm\:-mb-19 {
    margin-bottom: -4.75rem;
  }
  .sm\:-mb-18 {
    margin-bottom: -4.5rem;
  }
  .sm\:-mb-17 {
    margin-bottom: -4.25rem;
  }
  .sm\:-mb-16 {
    margin-bottom: -4rem;
  }
  .sm\:-mb-15 {
    margin-bottom: -3.75rem;
  }
  .sm\:-mb-14 {
    margin-bottom: -3.5rem;
  }
  .sm\:-mb-13 {
    margin-bottom: -3.25rem;
  }
  .sm\:-mb-12 {
    margin-bottom: -3rem;
  }
  .sm\:-mb-11 {
    margin-bottom: -2.75rem;
  }
  .sm\:-mb-10 {
    margin-bottom: -2.5rem;
  }
  .sm\:-mb-9 {
    margin-bottom: -2.25rem;
  }
  .sm\:-mb-8 {
    margin-bottom: -2rem;
  }
  .sm\:-mb-7 {
    margin-bottom: -1.75rem;
  }
  .sm\:-mb-6 {
    margin-bottom: -1.5rem;
  }
  .sm\:-mb-5 {
    margin-bottom: -1.25rem;
  }
  .sm\:-mb-4 {
    margin-bottom: -1rem;
  }
  .sm\:-mb-3 {
    margin-bottom: -0.75rem;
  }
  .sm\:-mb-2 {
    margin-bottom: -0.5rem;
  }
  .sm\:-mb-1 {
    margin-bottom: -0.25rem;
  }
  .sm\:-mb-0 {
    margin-bottom: 0rem;
  }
  .sm\:ml-20 {
    margin-left: 5rem;
  }
  .sm\:ml-19 {
    margin-left: 4.75rem;
  }
  .sm\:ml-18 {
    margin-left: 4.5rem;
  }
  .sm\:ml-17 {
    margin-left: 4.25rem;
  }
  .sm\:ml-16 {
    margin-left: 4rem;
  }
  .sm\:ml-15 {
    margin-left: 3.75rem;
  }
  .sm\:ml-14 {
    margin-left: 3.5rem;
  }
  .sm\:ml-13 {
    margin-left: 3.25rem;
  }
  .sm\:ml-12 {
    margin-left: 3rem;
  }
  .sm\:ml-11 {
    margin-left: 2.75rem;
  }
  .sm\:ml-10 {
    margin-left: 2.5rem;
  }
  .sm\:ml-9 {
    margin-left: 2.25rem;
  }
  .sm\:ml-8 {
    margin-left: 2rem;
  }
  .sm\:ml-7 {
    margin-left: 1.75rem;
  }
  .sm\:ml-6 {
    margin-left: 1.5rem;
  }
  .sm\:ml-5 {
    margin-left: 1.25rem;
  }
  .sm\:ml-4 {
    margin-left: 1rem;
  }
  .sm\:ml-3 {
    margin-left: 0.75rem;
  }
  .sm\:ml-2 {
    margin-left: 0.5rem;
  }
  .sm\:ml-1 {
    margin-left: 0.25rem;
  }
  .sm\:ml-0 {
    margin-left: 0rem;
  }
  .sm\:-ml-20 {
    margin-left: -5rem;
  }
  .sm\:-ml-19 {
    margin-left: -4.75rem;
  }
  .sm\:-ml-18 {
    margin-left: -4.5rem;
  }
  .sm\:-ml-17 {
    margin-left: -4.25rem;
  }
  .sm\:-ml-16 {
    margin-left: -4rem;
  }
  .sm\:-ml-15 {
    margin-left: -3.75rem;
  }
  .sm\:-ml-14 {
    margin-left: -3.5rem;
  }
  .sm\:-ml-13 {
    margin-left: -3.25rem;
  }
  .sm\:-ml-12 {
    margin-left: -3rem;
  }
  .sm\:-ml-11 {
    margin-left: -2.75rem;
  }
  .sm\:-ml-10 {
    margin-left: -2.5rem;
  }
  .sm\:-ml-9 {
    margin-left: -2.25rem;
  }
  .sm\:-ml-8 {
    margin-left: -2rem;
  }
  .sm\:-ml-7 {
    margin-left: -1.75rem;
  }
  .sm\:-ml-6 {
    margin-left: -1.5rem;
  }
  .sm\:-ml-5 {
    margin-left: -1.25rem;
  }
  .sm\:-ml-4 {
    margin-left: -1rem;
  }
  .sm\:-ml-3 {
    margin-left: -0.75rem;
  }
  .sm\:-ml-2 {
    margin-left: -0.5rem;
  }
  .sm\:-ml-1 {
    margin-left: -0.25rem;
  }
  .sm\:-ml-0 {
    margin-left: 0rem;
  }
}
@media (min-width: 768px) {
  .md\:p-20 {
    padding: 5rem;
  }
  .md\:p-19 {
    padding: 4.75rem;
  }
  .md\:p-18 {
    padding: 4.5rem;
  }
  .md\:p-17 {
    padding: 4.25rem;
  }
  .md\:p-16 {
    padding: 4rem;
  }
  .md\:p-15 {
    padding: 3.75rem;
  }
  .md\:p-14 {
    padding: 3.5rem;
  }
  .md\:p-13 {
    padding: 3.25rem;
  }
  .md\:p-12 {
    padding: 3rem;
  }
  .md\:p-11 {
    padding: 2.75rem;
  }
  .md\:p-10 {
    padding: 2.5rem;
  }
  .md\:p-9 {
    padding: 2.25rem;
  }
  .md\:p-8 {
    padding: 2rem;
  }
  .md\:p-7 {
    padding: 1.75rem;
  }
  .md\:p-6 {
    padding: 1.5rem;
  }
  .md\:p-5 {
    padding: 1.25rem;
  }
  .md\:p-4 {
    padding: 1rem;
  }
  .md\:p-3 {
    padding: 0.75rem;
  }
  .md\:p-2,
  .newsletter-form input {
    padding: 0.5rem;
  }
  .md\:p-1 {
    padding: 0.25rem;
  }
  .md\:p-0 {
    padding: 0rem;
  }
  .md\:pt-20 {
    padding-top: 5rem;
  }
  .md\:pt-19 {
    padding-top: 4.75rem;
  }
  .md\:pt-18 {
    padding-top: 4.5rem;
  }
  .md\:pt-17 {
    padding-top: 4.25rem;
  }
  .md\:pt-16 {
    padding-top: 4rem;
  }
  .md\:pt-15 {
    padding-top: 3.75rem;
  }
  .md\:pt-14 {
    padding-top: 3.5rem;
  }
  .md\:pt-13 {
    padding-top: 3.25rem;
  }
  .md\:pt-12 {
    padding-top: 3rem;
  }
  .md\:pt-11 {
    padding-top: 2.75rem;
  }
  .md\:pt-10 {
    padding-top: 2.5rem;
  }
  .md\:pt-9 {
    padding-top: 2.25rem;
  }
  .md\:pt-8 {
    padding-top: 2rem;
  }
  .md\:pt-7 {
    padding-top: 1.75rem;
  }
  .md\:pt-6 {
    padding-top: 1.5rem;
  }
  .md\:pt-5 {
    padding-top: 1.25rem;
  }
  .md\:pt-4 {
    padding-top: 1rem;
  }
  .md\:pt-3 {
    padding-top: 0.75rem;
  }
  .md\:pt-2 {
    padding-top: 0.5rem;
  }
  .md\:pt-1 {
    padding-top: 0.25rem;
  }
  .md\:pt-0 {
    padding-top: 0rem;
  }
  .md\:pr-20 {
    padding-right: 5rem;
  }
  .md\:pr-19 {
    padding-right: 4.75rem;
  }
  .md\:pr-18 {
    padding-right: 4.5rem;
  }
  .md\:pr-17 {
    padding-right: 4.25rem;
  }
  .md\:pr-16 {
    padding-right: 4rem;
  }
  .md\:pr-15 {
    padding-right: 3.75rem;
  }
  .md\:pr-14 {
    padding-right: 3.5rem;
  }
  .md\:pr-13 {
    padding-right: 3.25rem;
  }
  .md\:pr-12 {
    padding-right: 3rem;
  }
  .md\:pr-11 {
    padding-right: 2.75rem;
  }
  .md\:pr-10 {
    padding-right: 2.5rem;
  }
  .md\:pr-9 {
    padding-right: 2.25rem;
  }
  .md\:pr-8 {
    padding-right: 2rem;
  }
  .md\:pr-7 {
    padding-right: 1.75rem;
  }
  .md\:pr-6 {
    padding-right: 1.5rem;
  }
  .md\:pr-5 {
    padding-right: 1.25rem;
  }
  .md\:pr-4 {
    padding-right: 1rem;
  }
  .md\:pr-3 {
    padding-right: 0.75rem;
  }
  .md\:pr-2 {
    padding-right: 0.5rem;
  }
  .md\:pr-1 {
    padding-right: 0.25rem;
  }
  .md\:pr-0 {
    padding-right: 0rem;
  }
  .md\:pb-20 {
    padding-bottom: 5rem;
  }
  .md\:pb-19 {
    padding-bottom: 4.75rem;
  }
  .md\:pb-18 {
    padding-bottom: 4.5rem;
  }
  .md\:pb-17 {
    padding-bottom: 4.25rem;
  }
  .md\:pb-16 {
    padding-bottom: 4rem;
  }
  .md\:pb-15 {
    padding-bottom: 3.75rem;
  }
  .md\:pb-14 {
    padding-bottom: 3.5rem;
  }
  .md\:pb-13 {
    padding-bottom: 3.25rem;
  }
  .md\:pb-12 {
    padding-bottom: 3rem;
  }
  .md\:pb-11 {
    padding-bottom: 2.75rem;
  }
  .md\:pb-10 {
    padding-bottom: 2.5rem;
  }
  .md\:pb-9 {
    padding-bottom: 2.25rem;
  }
  .md\:pb-8 {
    padding-bottom: 2rem;
  }
  .md\:pb-7 {
    padding-bottom: 1.75rem;
  }
  .md\:pb-6 {
    padding-bottom: 1.5rem;
  }
  .md\:pb-5 {
    padding-bottom: 1.25rem;
  }
  .md\:pb-4 {
    padding-bottom: 1rem;
  }
  .md\:pb-3 {
    padding-bottom: 0.75rem;
  }
  .md\:pb-2 {
    padding-bottom: 0.5rem;
  }
  .md\:pb-1 {
    padding-bottom: 0.25rem;
  }
  .md\:pb-0 {
    padding-bottom: 0rem;
  }
  .md\:pl-20 {
    padding-left: 5rem;
  }
  .md\:pl-19 {
    padding-left: 4.75rem;
  }
  .md\:pl-18 {
    padding-left: 4.5rem;
  }
  .md\:pl-17 {
    padding-left: 4.25rem;
  }
  .md\:pl-16 {
    padding-left: 4rem;
  }
  .md\:pl-15 {
    padding-left: 3.75rem;
  }
  .md\:pl-14 {
    padding-left: 3.5rem;
  }
  .md\:pl-13 {
    padding-left: 3.25rem;
  }
  .md\:pl-12 {
    padding-left: 3rem;
  }
  .md\:pl-11 {
    padding-left: 2.75rem;
  }
  .md\:pl-10 {
    padding-left: 2.5rem;
  }
  .md\:pl-9 {
    padding-left: 2.25rem;
  }
  .md\:pl-8 {
    padding-left: 2rem;
  }
  .md\:pl-7 {
    padding-left: 1.75rem;
  }
  .md\:pl-6 {
    padding-left: 1.5rem;
  }
  .md\:pl-5 {
    padding-left: 1.25rem;
  }
  .md\:pl-4 {
    padding-left: 1rem;
  }
  .md\:pl-3 {
    padding-left: 0.75rem;
  }
  .md\:pl-2 {
    padding-left: 0.5rem;
  }
  .md\:pl-1 {
    padding-left: 0.25rem;
  }
  .md\:pl-0 {
    padding-left: 0rem;
  }
  .md\:m-20 {
    margin: 5rem;
  }
  .md\:m-19 {
    margin: 4.75rem;
  }
  .md\:m-18 {
    margin: 4.5rem;
  }
  .md\:m-17 {
    margin: 4.25rem;
  }
  .md\:m-16 {
    margin: 4rem;
  }
  .md\:m-15 {
    margin: 3.75rem;
  }
  .md\:m-14 {
    margin: 3.5rem;
  }
  .md\:m-13 {
    margin: 3.25rem;
  }
  .md\:m-12 {
    margin: 3rem;
  }
  .md\:m-11 {
    margin: 2.75rem;
  }
  .md\:m-10 {
    margin: 2.5rem;
  }
  .md\:m-9 {
    margin: 2.25rem;
  }
  .md\:m-8 {
    margin: 2rem;
  }
  .md\:m-7 {
    margin: 1.75rem;
  }
  .md\:m-6 {
    margin: 1.5rem;
  }
  .md\:m-5 {
    margin: 1.25rem;
  }
  .md\:m-4 {
    margin: 1rem;
  }
  .md\:m-3 {
    margin: 0.75rem;
  }
  .md\:m-2 {
    margin: 0.5rem;
  }
  .md\:m-1 {
    margin: 0.25rem;
  }
  .md\:m-0 {
    margin: 0rem;
  }
  .md\:-m-20 {
    margin: -5rem;
  }
  .md\:-m-19 {
    margin: -4.75rem;
  }
  .md\:-m-18 {
    margin: -4.5rem;
  }
  .md\:-m-17 {
    margin: -4.25rem;
  }
  .md\:-m-16 {
    margin: -4rem;
  }
  .md\:-m-15 {
    margin: -3.75rem;
  }
  .md\:-m-14 {
    margin: -3.5rem;
  }
  .md\:-m-13 {
    margin: -3.25rem;
  }
  .md\:-m-12 {
    margin: -3rem;
  }
  .md\:-m-11 {
    margin: -2.75rem;
  }
  .md\:-m-10 {
    margin: -2.5rem;
  }
  .md\:-m-9 {
    margin: -2.25rem;
  }
  .md\:-m-8 {
    margin: -2rem;
  }
  .md\:-m-7 {
    margin: -1.75rem;
  }
  .md\:-m-6 {
    margin: -1.5rem;
  }
  .md\:-m-5 {
    margin: -1.25rem;
  }
  .md\:-m-4 {
    margin: -1rem;
  }
  .md\:-m-3 {
    margin: -0.75rem;
  }
  .md\:-m-2 {
    margin: -0.5rem;
  }
  .md\:-m-1 {
    margin: -0.25rem;
  }
  .md\:-m-0 {
    margin: 0rem;
  }
  .md\:mt-20 {
    margin-top: 5rem;
  }
  .md\:mt-19 {
    margin-top: 4.75rem;
  }
  .md\:mt-18 {
    margin-top: 4.5rem;
  }
  .md\:mt-17 {
    margin-top: 4.25rem;
  }
  .md\:mt-16 {
    margin-top: 4rem;
  }
  .md\:mt-15 {
    margin-top: 3.75rem;
  }
  .md\:mt-14 {
    margin-top: 3.5rem;
  }
  .md\:mt-13 {
    margin-top: 3.25rem;
  }
  .md\:mt-12 {
    margin-top: 3rem;
  }
  .md\:mt-11 {
    margin-top: 2.75rem;
  }
  .md\:mt-10 {
    margin-top: 2.5rem;
  }
  .md\:mt-9 {
    margin-top: 2.25rem;
  }
  .md\:mt-8 {
    margin-top: 2rem;
  }
  .md\:mt-7 {
    margin-top: 1.75rem;
  }
  .md\:mt-6 {
    margin-top: 1.5rem;
  }
  .md\:mt-5 {
    margin-top: 1.25rem;
  }
  .md\:mt-4 {
    margin-top: 1rem;
  }
  .md\:mt-3 {
    margin-top: 0.75rem;
  }
  .md\:mt-2 {
    margin-top: 0.5rem;
  }
  .md\:mt-1 {
    margin-top: 0.25rem;
  }
  .md\:mt-0 {
    margin-top: 0rem;
  }
  .md\:-mt-20 {
    margin-top: -5rem;
  }
  .md\:-mt-19 {
    margin-top: -4.75rem;
  }
  .md\:-mt-18 {
    margin-top: -4.5rem;
  }
  .md\:-mt-17 {
    margin-top: -4.25rem;
  }
  .md\:-mt-16 {
    margin-top: -4rem;
  }
  .md\:-mt-15 {
    margin-top: -3.75rem;
  }
  .md\:-mt-14 {
    margin-top: -3.5rem;
  }
  .md\:-mt-13 {
    margin-top: -3.25rem;
  }
  .md\:-mt-12 {
    margin-top: -3rem;
  }
  .md\:-mt-11 {
    margin-top: -2.75rem;
  }
  .md\:-mt-10 {
    margin-top: -2.5rem;
  }
  .md\:-mt-9 {
    margin-top: -2.25rem;
  }
  .md\:-mt-8 {
    margin-top: -2rem;
  }
  .md\:-mt-7 {
    margin-top: -1.75rem;
  }
  .md\:-mt-6 {
    margin-top: -1.5rem;
  }
  .md\:-mt-5 {
    margin-top: -1.25rem;
  }
  .md\:-mt-4 {
    margin-top: -1rem;
  }
  .md\:-mt-3 {
    margin-top: -0.75rem;
  }
  .md\:-mt-2 {
    margin-top: -0.5rem;
  }
  .md\:-mt-1 {
    margin-top: -0.25rem;
  }
  .md\:-mt-0 {
    margin-top: 0rem;
  }
  .md\:mr-20 {
    margin-right: 5rem;
  }
  .md\:mr-19 {
    margin-right: 4.75rem;
  }
  .md\:mr-18 {
    margin-right: 4.5rem;
  }
  .md\:mr-17 {
    margin-right: 4.25rem;
  }
  .md\:mr-16 {
    margin-right: 4rem;
  }
  .md\:mr-15 {
    margin-right: 3.75rem;
  }
  .md\:mr-14 {
    margin-right: 3.5rem;
  }
  .md\:mr-13 {
    margin-right: 3.25rem;
  }
  .md\:mr-12 {
    margin-right: 3rem;
  }
  .md\:mr-11 {
    margin-right: 2.75rem;
  }
  .md\:mr-10 {
    margin-right: 2.5rem;
  }
  .md\:mr-9 {
    margin-right: 2.25rem;
  }
  .md\:mr-8 {
    margin-right: 2rem;
  }
  .md\:mr-7 {
    margin-right: 1.75rem;
  }
  .md\:mr-6 {
    margin-right: 1.5rem;
  }
  .md\:mr-5 {
    margin-right: 1.25rem;
  }
  .md\:mr-4,
  .btn-donate,
  .btn-donate-lg {
    margin-right: 1rem;
  }
  .md\:mr-3 {
    margin-right: 0.75rem;
  }
  .md\:mr-2,
  .newsletter-form input {
    margin-right: 0.5rem;
  }
  .md\:mr-1 {
    margin-right: 0.25rem;
  }
  .md\:mr-0 {
    margin-right: 0rem;
  }
  .md\:-mr-20 {
    margin-right: -5rem;
  }
  .md\:-mr-19 {
    margin-right: -4.75rem;
  }
  .md\:-mr-18 {
    margin-right: -4.5rem;
  }
  .md\:-mr-17 {
    margin-right: -4.25rem;
  }
  .md\:-mr-16 {
    margin-right: -4rem;
  }
  .md\:-mr-15 {
    margin-right: -3.75rem;
  }
  .md\:-mr-14 {
    margin-right: -3.5rem;
  }
  .md\:-mr-13 {
    margin-right: -3.25rem;
  }
  .md\:-mr-12 {
    margin-right: -3rem;
  }
  .md\:-mr-11 {
    margin-right: -2.75rem;
  }
  .md\:-mr-10 {
    margin-right: -2.5rem;
  }
  .md\:-mr-9 {
    margin-right: -2.25rem;
  }
  .md\:-mr-8 {
    margin-right: -2rem;
  }
  .md\:-mr-7 {
    margin-right: -1.75rem;
  }
  .md\:-mr-6 {
    margin-right: -1.5rem;
  }
  .md\:-mr-5 {
    margin-right: -1.25rem;
  }
  .md\:-mr-4 {
    margin-right: -1rem;
  }
  .md\:-mr-3 {
    margin-right: -0.75rem;
  }
  .md\:-mr-2 {
    margin-right: -0.5rem;
  }
  .md\:-mr-1 {
    margin-right: -0.25rem;
  }
  .md\:-mr-0 {
    margin-right: 0rem;
  }
  .md\:mb-20 {
    margin-bottom: 5rem;
  }
  .md\:mb-19 {
    margin-bottom: 4.75rem;
  }
  .md\:mb-18 {
    margin-bottom: 4.5rem;
  }
  .md\:mb-17 {
    margin-bottom: 4.25rem;
  }
  .md\:mb-16 {
    margin-bottom: 4rem;
  }
  .md\:mb-15 {
    margin-bottom: 3.75rem;
  }
  .md\:mb-14 {
    margin-bottom: 3.5rem;
  }
  .md\:mb-13 {
    margin-bottom: 3.25rem;
  }
  .md\:mb-12 {
    margin-bottom: 3rem;
  }
  .md\:mb-11 {
    margin-bottom: 2.75rem;
  }
  .md\:mb-10 {
    margin-bottom: 2.5rem;
  }
  .md\:mb-9 {
    margin-bottom: 2.25rem;
  }
  .md\:mb-8 {
    margin-bottom: 2rem;
  }
  .md\:mb-7 {
    margin-bottom: 1.75rem;
  }
  .md\:mb-6 {
    margin-bottom: 1.5rem;
  }
  .md\:mb-5 {
    margin-bottom: 1.25rem;
  }
  .md\:mb-4 {
    margin-bottom: 1rem;
  }
  .md\:mb-3 {
    margin-bottom: 0.75rem;
  }
  .md\:mb-2 {
    margin-bottom: 0.5rem;
  }
  .md\:mb-1 {
    margin-bottom: 0.25rem;
  }
  .md\:mb-0 {
    margin-bottom: 0rem;
  }
  .md\:-mb-20 {
    margin-bottom: -5rem;
  }
  .md\:-mb-19 {
    margin-bottom: -4.75rem;
  }
  .md\:-mb-18 {
    margin-bottom: -4.5rem;
  }
  .md\:-mb-17 {
    margin-bottom: -4.25rem;
  }
  .md\:-mb-16 {
    margin-bottom: -4rem;
  }
  .md\:-mb-15 {
    margin-bottom: -3.75rem;
  }
  .md\:-mb-14 {
    margin-bottom: -3.5rem;
  }
  .md\:-mb-13 {
    margin-bottom: -3.25rem;
  }
  .md\:-mb-12 {
    margin-bottom: -3rem;
  }
  .md\:-mb-11 {
    margin-bottom: -2.75rem;
  }
  .md\:-mb-10 {
    margin-bottom: -2.5rem;
  }
  .md\:-mb-9 {
    margin-bottom: -2.25rem;
  }
  .md\:-mb-8 {
    margin-bottom: -2rem;
  }
  .md\:-mb-7 {
    margin-bottom: -1.75rem;
  }
  .md\:-mb-6 {
    margin-bottom: -1.5rem;
  }
  .md\:-mb-5 {
    margin-bottom: -1.25rem;
  }
  .md\:-mb-4 {
    margin-bottom: -1rem;
  }
  .md\:-mb-3 {
    margin-bottom: -0.75rem;
  }
  .md\:-mb-2 {
    margin-bottom: -0.5rem;
  }
  .md\:-mb-1 {
    margin-bottom: -0.25rem;
  }
  .md\:-mb-0 {
    margin-bottom: 0rem;
  }
  .md\:ml-20 {
    margin-left: 5rem;
  }
  .md\:ml-19 {
    margin-left: 4.75rem;
  }
  .md\:ml-18 {
    margin-left: 4.5rem;
  }
  .md\:ml-17 {
    margin-left: 4.25rem;
  }
  .md\:ml-16 {
    margin-left: 4rem;
  }
  .md\:ml-15 {
    margin-left: 3.75rem;
  }
  .md\:ml-14 {
    margin-left: 3.5rem;
  }
  .md\:ml-13 {
    margin-left: 3.25rem;
  }
  .md\:ml-12 {
    margin-left: 3rem;
  }
  .md\:ml-11 {
    margin-left: 2.75rem;
  }
  .md\:ml-10 {
    margin-left: 2.5rem;
  }
  .md\:ml-9 {
    margin-left: 2.25rem;
  }
  .md\:ml-8 {
    margin-left: 2rem;
  }
  .md\:ml-7 {
    margin-left: 1.75rem;
  }
  .md\:ml-6 {
    margin-left: 1.5rem;
  }
  .md\:ml-5 {
    margin-left: 1.25rem;
  }
  .md\:ml-4,
  .btn-donate,
  .btn-donate-lg {
    margin-left: 1rem;
  }
  .md\:ml-3 {
    margin-left: 0.75rem;
  }
  .md\:ml-2 {
    margin-left: 0.5rem;
  }
  .md\:ml-1 {
    margin-left: 0.25rem;
  }
  .md\:ml-0 {
    margin-left: 0rem;
  }
  .md\:-ml-20 {
    margin-left: -5rem;
  }
  .md\:-ml-19 {
    margin-left: -4.75rem;
  }
  .md\:-ml-18 {
    margin-left: -4.5rem;
  }
  .md\:-ml-17 {
    margin-left: -4.25rem;
  }
  .md\:-ml-16 {
    margin-left: -4rem;
  }
  .md\:-ml-15 {
    margin-left: -3.75rem;
  }
  .md\:-ml-14 {
    margin-left: -3.5rem;
  }
  .md\:-ml-13 {
    margin-left: -3.25rem;
  }
  .md\:-ml-12 {
    margin-left: -3rem;
  }
  .md\:-ml-11 {
    margin-left: -2.75rem;
  }
  .md\:-ml-10 {
    margin-left: -2.5rem;
  }
  .md\:-ml-9 {
    margin-left: -2.25rem;
  }
  .md\:-ml-8 {
    margin-left: -2rem;
  }
  .md\:-ml-7 {
    margin-left: -1.75rem;
  }
  .md\:-ml-6 {
    margin-left: -1.5rem;
  }
  .md\:-ml-5 {
    margin-left: -1.25rem;
  }
  .md\:-ml-4 {
    margin-left: -1rem;
  }
  .md\:-ml-3 {
    margin-left: -0.75rem;
  }
  .md\:-ml-2 {
    margin-left: -0.5rem;
  }
  .md\:-ml-1 {
    margin-left: -0.25rem;
  }
  .md\:-ml-0 {
    margin-left: 0rem;
  }
}
@media (min-width: 1024px) {
  .lg\:p-20 {
    padding: 5rem;
  }
  .lg\:p-19 {
    padding: 4.75rem;
  }
  .lg\:p-18 {
    padding: 4.5rem;
  }
  .lg\:p-17 {
    padding: 4.25rem;
  }
  .lg\:p-16 {
    padding: 4rem;
  }
  .lg\:p-15 {
    padding: 3.75rem;
  }
  .lg\:p-14 {
    padding: 3.5rem;
  }
  .lg\:p-13 {
    padding: 3.25rem;
  }
  .lg\:p-12 {
    padding: 3rem;
  }
  .lg\:p-11 {
    padding: 2.75rem;
  }
  .lg\:p-10 {
    padding: 2.5rem;
  }
  .lg\:p-9 {
    padding: 2.25rem;
  }
  .lg\:p-8 {
    padding: 2rem;
  }
  .lg\:p-7 {
    padding: 1.75rem;
  }
  .lg\:p-6 {
    padding: 1.5rem;
  }
  .lg\:p-5 {
    padding: 1.25rem;
  }
  .lg\:p-4 {
    padding: 1rem;
  }
  .lg\:p-3 {
    padding: 0.75rem;
  }
  .lg\:p-2 {
    padding: 0.5rem;
  }
  .lg\:p-1 {
    padding: 0.25rem;
  }
  .lg\:p-0 {
    padding: 0rem;
  }
  .lg\:pt-20 {
    padding-top: 5rem;
  }
  .lg\:pt-19 {
    padding-top: 4.75rem;
  }
  .lg\:pt-18 {
    padding-top: 4.5rem;
  }
  .lg\:pt-17 {
    padding-top: 4.25rem;
  }
  .lg\:pt-16 {
    padding-top: 4rem;
  }
  .lg\:pt-15 {
    padding-top: 3.75rem;
  }
  .lg\:pt-14 {
    padding-top: 3.5rem;
  }
  .lg\:pt-13 {
    padding-top: 3.25rem;
  }
  .lg\:pt-12 {
    padding-top: 3rem;
  }
  .lg\:pt-11 {
    padding-top: 2.75rem;
  }
  .lg\:pt-10 {
    padding-top: 2.5rem;
  }
  .lg\:pt-9 {
    padding-top: 2.25rem;
  }
  .lg\:pt-8 {
    padding-top: 2rem;
  }
  .lg\:pt-7 {
    padding-top: 1.75rem;
  }
  .lg\:pt-6 {
    padding-top: 1.5rem;
  }
  .lg\:pt-5 {
    padding-top: 1.25rem;
  }
  .lg\:pt-4 {
    padding-top: 1rem;
  }
  .lg\:pt-3 {
    padding-top: 0.75rem;
  }
  .lg\:pt-2 {
    padding-top: 0.5rem;
  }
  .lg\:pt-1 {
    padding-top: 0.25rem;
  }
  .lg\:pt-0 {
    padding-top: 0rem;
  }
  .lg\:pr-20 {
    padding-right: 5rem;
  }
  .lg\:pr-19 {
    padding-right: 4.75rem;
  }
  .lg\:pr-18 {
    padding-right: 4.5rem;
  }
  .lg\:pr-17 {
    padding-right: 4.25rem;
  }
  .lg\:pr-16 {
    padding-right: 4rem;
  }
  .lg\:pr-15 {
    padding-right: 3.75rem;
  }
  .lg\:pr-14 {
    padding-right: 3.5rem;
  }
  .lg\:pr-13 {
    padding-right: 3.25rem;
  }
  .lg\:pr-12 {
    padding-right: 3rem;
  }
  .lg\:pr-11 {
    padding-right: 2.75rem;
  }
  .lg\:pr-10 {
    padding-right: 2.5rem;
  }
  .lg\:pr-9 {
    padding-right: 2.25rem;
  }
  .lg\:pr-8 {
    padding-right: 2rem;
  }
  .lg\:pr-7 {
    padding-right: 1.75rem;
  }
  .lg\:pr-6 {
    padding-right: 1.5rem;
  }
  .lg\:pr-5 {
    padding-right: 1.25rem;
  }
  .lg\:pr-4 {
    padding-right: 1rem;
  }
  .lg\:pr-3 {
    padding-right: 0.75rem;
  }
  .lg\:pr-2 {
    padding-right: 0.5rem;
  }
  .lg\:pr-1 {
    padding-right: 0.25rem;
  }
  .lg\:pr-0 {
    padding-right: 0rem;
  }
  .lg\:pb-20 {
    padding-bottom: 5rem;
  }
  .lg\:pb-19 {
    padding-bottom: 4.75rem;
  }
  .lg\:pb-18 {
    padding-bottom: 4.5rem;
  }
  .lg\:pb-17 {
    padding-bottom: 4.25rem;
  }
  .lg\:pb-16 {
    padding-bottom: 4rem;
  }
  .lg\:pb-15 {
    padding-bottom: 3.75rem;
  }
  .lg\:pb-14 {
    padding-bottom: 3.5rem;
  }
  .lg\:pb-13 {
    padding-bottom: 3.25rem;
  }
  .lg\:pb-12 {
    padding-bottom: 3rem;
  }
  .lg\:pb-11 {
    padding-bottom: 2.75rem;
  }
  .lg\:pb-10 {
    padding-bottom: 2.5rem;
  }
  .lg\:pb-9 {
    padding-bottom: 2.25rem;
  }
  .lg\:pb-8 {
    padding-bottom: 2rem;
  }
  .lg\:pb-7 {
    padding-bottom: 1.75rem;
  }
  .lg\:pb-6 {
    padding-bottom: 1.5rem;
  }
  .lg\:pb-5 {
    padding-bottom: 1.25rem;
  }
  .lg\:pb-4 {
    padding-bottom: 1rem;
  }
  .lg\:pb-3 {
    padding-bottom: 0.75rem;
  }
  .lg\:pb-2 {
    padding-bottom: 0.5rem;
  }
  .lg\:pb-1 {
    padding-bottom: 0.25rem;
  }
  .lg\:pb-0 {
    padding-bottom: 0rem;
  }
  .lg\:pl-20 {
    padding-left: 5rem;
  }
  .lg\:pl-19 {
    padding-left: 4.75rem;
  }
  .lg\:pl-18 {
    padding-left: 4.5rem;
  }
  .lg\:pl-17 {
    padding-left: 4.25rem;
  }
  .lg\:pl-16 {
    padding-left: 4rem;
  }
  .lg\:pl-15 {
    padding-left: 3.75rem;
  }
  .lg\:pl-14 {
    padding-left: 3.5rem;
  }
  .lg\:pl-13 {
    padding-left: 3.25rem;
  }
  .lg\:pl-12 {
    padding-left: 3rem;
  }
  .lg\:pl-11 {
    padding-left: 2.75rem;
  }
  .lg\:pl-10 {
    padding-left: 2.5rem;
  }
  .lg\:pl-9 {
    padding-left: 2.25rem;
  }
  .lg\:pl-8 {
    padding-left: 2rem;
  }
  .lg\:pl-7 {
    padding-left: 1.75rem;
  }
  .lg\:pl-6 {
    padding-left: 1.5rem;
  }
  .lg\:pl-5 {
    padding-left: 1.25rem;
  }
  .lg\:pl-4 {
    padding-left: 1rem;
  }
  .lg\:pl-3 {
    padding-left: 0.75rem;
  }
  .lg\:pl-2 {
    padding-left: 0.5rem;
  }
  .lg\:pl-1 {
    padding-left: 0.25rem;
  }
  .lg\:pl-0 {
    padding-left: 0rem;
  }
  .lg\:m-20 {
    margin: 5rem;
  }
  .lg\:m-19 {
    margin: 4.75rem;
  }
  .lg\:m-18 {
    margin: 4.5rem;
  }
  .lg\:m-17 {
    margin: 4.25rem;
  }
  .lg\:m-16 {
    margin: 4rem;
  }
  .lg\:m-15 {
    margin: 3.75rem;
  }
  .lg\:m-14 {
    margin: 3.5rem;
  }
  .lg\:m-13 {
    margin: 3.25rem;
  }
  .lg\:m-12 {
    margin: 3rem;
  }
  .lg\:m-11 {
    margin: 2.75rem;
  }
  .lg\:m-10 {
    margin: 2.5rem;
  }
  .lg\:m-9 {
    margin: 2.25rem;
  }
  .lg\:m-8 {
    margin: 2rem;
  }
  .lg\:m-7 {
    margin: 1.75rem;
  }
  .lg\:m-6 {
    margin: 1.5rem;
  }
  .lg\:m-5 {
    margin: 1.25rem;
  }
  .lg\:m-4 {
    margin: 1rem;
  }
  .lg\:m-3 {
    margin: 0.75rem;
  }
  .lg\:m-2 {
    margin: 0.5rem;
  }
  .lg\:m-1 {
    margin: 0.25rem;
  }
  .lg\:m-0 {
    margin: 0rem;
  }
  .lg\:-m-20 {
    margin: -5rem;
  }
  .lg\:-m-19 {
    margin: -4.75rem;
  }
  .lg\:-m-18 {
    margin: -4.5rem;
  }
  .lg\:-m-17 {
    margin: -4.25rem;
  }
  .lg\:-m-16 {
    margin: -4rem;
  }
  .lg\:-m-15 {
    margin: -3.75rem;
  }
  .lg\:-m-14 {
    margin: -3.5rem;
  }
  .lg\:-m-13 {
    margin: -3.25rem;
  }
  .lg\:-m-12 {
    margin: -3rem;
  }
  .lg\:-m-11 {
    margin: -2.75rem;
  }
  .lg\:-m-10 {
    margin: -2.5rem;
  }
  .lg\:-m-9 {
    margin: -2.25rem;
  }
  .lg\:-m-8 {
    margin: -2rem;
  }
  .lg\:-m-7 {
    margin: -1.75rem;
  }
  .lg\:-m-6 {
    margin: -1.5rem;
  }
  .lg\:-m-5 {
    margin: -1.25rem;
  }
  .lg\:-m-4 {
    margin: -1rem;
  }
  .lg\:-m-3 {
    margin: -0.75rem;
  }
  .lg\:-m-2 {
    margin: -0.5rem;
  }
  .lg\:-m-1 {
    margin: -0.25rem;
  }
  .lg\:-m-0 {
    margin: 0rem;
  }
  .lg\:mt-20 {
    margin-top: 5rem;
  }
  .lg\:mt-19 {
    margin-top: 4.75rem;
  }
  .lg\:mt-18 {
    margin-top: 4.5rem;
  }
  .lg\:mt-17 {
    margin-top: 4.25rem;
  }
  .lg\:mt-16 {
    margin-top: 4rem;
  }
  .lg\:mt-15 {
    margin-top: 3.75rem;
  }
  .lg\:mt-14 {
    margin-top: 3.5rem;
  }
  .lg\:mt-13 {
    margin-top: 3.25rem;
  }
  .lg\:mt-12 {
    margin-top: 3rem;
  }
  .lg\:mt-11 {
    margin-top: 2.75rem;
  }
  .lg\:mt-10 {
    margin-top: 2.5rem;
  }
  .lg\:mt-9 {
    margin-top: 2.25rem;
  }
  .lg\:mt-8 {
    margin-top: 2rem;
  }
  .lg\:mt-7 {
    margin-top: 1.75rem;
  }
  .lg\:mt-6 {
    margin-top: 1.5rem;
  }
  .lg\:mt-5 {
    margin-top: 1.25rem;
  }
  .lg\:mt-4 {
    margin-top: 1rem;
  }
  .lg\:mt-3 {
    margin-top: 0.75rem;
  }
  .lg\:mt-2 {
    margin-top: 0.5rem;
  }
  .lg\:mt-1 {
    margin-top: 0.25rem;
  }
  .lg\:mt-0 {
    margin-top: 0rem;
  }
  .lg\:-mt-20 {
    margin-top: -5rem;
  }
  .lg\:-mt-19 {
    margin-top: -4.75rem;
  }
  .lg\:-mt-18 {
    margin-top: -4.5rem;
  }
  .lg\:-mt-17 {
    margin-top: -4.25rem;
  }
  .lg\:-mt-16 {
    margin-top: -4rem;
  }
  .lg\:-mt-15 {
    margin-top: -3.75rem;
  }
  .lg\:-mt-14 {
    margin-top: -3.5rem;
  }
  .lg\:-mt-13 {
    margin-top: -3.25rem;
  }
  .lg\:-mt-12 {
    margin-top: -3rem;
  }
  .lg\:-mt-11 {
    margin-top: -2.75rem;
  }
  .lg\:-mt-10 {
    margin-top: -2.5rem;
  }
  .lg\:-mt-9 {
    margin-top: -2.25rem;
  }
  .lg\:-mt-8 {
    margin-top: -2rem;
  }
  .lg\:-mt-7 {
    margin-top: -1.75rem;
  }
  .lg\:-mt-6 {
    margin-top: -1.5rem;
  }
  .lg\:-mt-5 {
    margin-top: -1.25rem;
  }
  .lg\:-mt-4 {
    margin-top: -1rem;
  }
  .lg\:-mt-3 {
    margin-top: -0.75rem;
  }
  .lg\:-mt-2 {
    margin-top: -0.5rem;
  }
  .lg\:-mt-1 {
    margin-top: -0.25rem;
  }
  .lg\:-mt-0 {
    margin-top: 0rem;
  }
  .lg\:mr-20 {
    margin-right: 5rem;
  }
  .lg\:mr-19 {
    margin-right: 4.75rem;
  }
  .lg\:mr-18 {
    margin-right: 4.5rem;
  }
  .lg\:mr-17 {
    margin-right: 4.25rem;
  }
  .lg\:mr-16 {
    margin-right: 4rem;
  }
  .lg\:mr-15 {
    margin-right: 3.75rem;
  }
  .lg\:mr-14 {
    margin-right: 3.5rem;
  }
  .lg\:mr-13 {
    margin-right: 3.25rem;
  }
  .lg\:mr-12 {
    margin-right: 3rem;
  }
  .lg\:mr-11 {
    margin-right: 2.75rem;
  }
  .lg\:mr-10 {
    margin-right: 2.5rem;
  }
  .lg\:mr-9 {
    margin-right: 2.25rem;
  }
  .lg\:mr-8 {
    margin-right: 2rem;
  }
  .lg\:mr-7 {
    margin-right: 1.75rem;
  }
  .lg\:mr-6 {
    margin-right: 1.5rem;
  }
  .lg\:mr-5 {
    margin-right: 1.25rem;
  }
  .lg\:mr-4 {
    margin-right: 1rem;
  }
  .lg\:mr-3 {
    margin-right: 0.75rem;
  }
  .lg\:mr-2 {
    margin-right: 0.5rem;
  }
  .lg\:mr-1 {
    margin-right: 0.25rem;
  }
  .lg\:mr-0 {
    margin-right: 0rem;
  }
  .lg\:-mr-20 {
    margin-right: -5rem;
  }
  .lg\:-mr-19 {
    margin-right: -4.75rem;
  }
  .lg\:-mr-18 {
    margin-right: -4.5rem;
  }
  .lg\:-mr-17 {
    margin-right: -4.25rem;
  }
  .lg\:-mr-16 {
    margin-right: -4rem;
  }
  .lg\:-mr-15 {
    margin-right: -3.75rem;
  }
  .lg\:-mr-14 {
    margin-right: -3.5rem;
  }
  .lg\:-mr-13 {
    margin-right: -3.25rem;
  }
  .lg\:-mr-12 {
    margin-right: -3rem;
  }
  .lg\:-mr-11 {
    margin-right: -2.75rem;
  }
  .lg\:-mr-10 {
    margin-right: -2.5rem;
  }
  .lg\:-mr-9 {
    margin-right: -2.25rem;
  }
  .lg\:-mr-8 {
    margin-right: -2rem;
  }
  .lg\:-mr-7 {
    margin-right: -1.75rem;
  }
  .lg\:-mr-6 {
    margin-right: -1.5rem;
  }
  .lg\:-mr-5 {
    margin-right: -1.25rem;
  }
  .lg\:-mr-4 {
    margin-right: -1rem;
  }
  .lg\:-mr-3 {
    margin-right: -0.75rem;
  }
  .lg\:-mr-2 {
    margin-right: -0.5rem;
  }
  .lg\:-mr-1 {
    margin-right: -0.25rem;
  }
  .lg\:-mr-0 {
    margin-right: 0rem;
  }
  .lg\:mb-20 {
    margin-bottom: 5rem;
  }
  .lg\:mb-19 {
    margin-bottom: 4.75rem;
  }
  .lg\:mb-18 {
    margin-bottom: 4.5rem;
  }
  .lg\:mb-17 {
    margin-bottom: 4.25rem;
  }
  .lg\:mb-16 {
    margin-bottom: 4rem;
  }
  .lg\:mb-15 {
    margin-bottom: 3.75rem;
  }
  .lg\:mb-14 {
    margin-bottom: 3.5rem;
  }
  .lg\:mb-13 {
    margin-bottom: 3.25rem;
  }
  .lg\:mb-12 {
    margin-bottom: 3rem;
  }
  .lg\:mb-11 {
    margin-bottom: 2.75rem;
  }
  .lg\:mb-10 {
    margin-bottom: 2.5rem;
  }
  .lg\:mb-9 {
    margin-bottom: 2.25rem;
  }
  .lg\:mb-8 {
    margin-bottom: 2rem;
  }
  .lg\:mb-7 {
    margin-bottom: 1.75rem;
  }
  .lg\:mb-6 {
    margin-bottom: 1.5rem;
  }
  .lg\:mb-5 {
    margin-bottom: 1.25rem;
  }
  .lg\:mb-4 {
    margin-bottom: 1rem;
  }
  .lg\:mb-3 {
    margin-bottom: 0.75rem;
  }
  .lg\:mb-2 {
    margin-bottom: 0.5rem;
  }
  .lg\:mb-1 {
    margin-bottom: 0.25rem;
  }
  .lg\:mb-0 {
    margin-bottom: 0rem;
  }
  .lg\:-mb-20 {
    margin-bottom: -5rem;
  }
  .lg\:-mb-19 {
    margin-bottom: -4.75rem;
  }
  .lg\:-mb-18 {
    margin-bottom: -4.5rem;
  }
  .lg\:-mb-17 {
    margin-bottom: -4.25rem;
  }
  .lg\:-mb-16 {
    margin-bottom: -4rem;
  }
  .lg\:-mb-15 {
    margin-bottom: -3.75rem;
  }
  .lg\:-mb-14 {
    margin-bottom: -3.5rem;
  }
  .lg\:-mb-13 {
    margin-bottom: -3.25rem;
  }
  .lg\:-mb-12 {
    margin-bottom: -3rem;
  }
  .lg\:-mb-11 {
    margin-bottom: -2.75rem;
  }
  .lg\:-mb-10 {
    margin-bottom: -2.5rem;
  }
  .lg\:-mb-9 {
    margin-bottom: -2.25rem;
  }
  .lg\:-mb-8 {
    margin-bottom: -2rem;
  }
  .lg\:-mb-7 {
    margin-bottom: -1.75rem;
  }
  .lg\:-mb-6 {
    margin-bottom: -1.5rem;
  }
  .lg\:-mb-5 {
    margin-bottom: -1.25rem;
  }
  .lg\:-mb-4 {
    margin-bottom: -1rem;
  }
  .lg\:-mb-3 {
    margin-bottom: -0.75rem;
  }
  .lg\:-mb-2 {
    margin-bottom: -0.5rem;
  }
  .lg\:-mb-1 {
    margin-bottom: -0.25rem;
  }
  .lg\:-mb-0 {
    margin-bottom: 0rem;
  }
  .lg\:ml-20 {
    margin-left: 5rem;
  }
  .lg\:ml-19 {
    margin-left: 4.75rem;
  }
  .lg\:ml-18 {
    margin-left: 4.5rem;
  }
  .lg\:ml-17 {
    margin-left: 4.25rem;
  }
  .lg\:ml-16 {
    margin-left: 4rem;
  }
  .lg\:ml-15 {
    margin-left: 3.75rem;
  }
  .lg\:ml-14 {
    margin-left: 3.5rem;
  }
  .lg\:ml-13 {
    margin-left: 3.25rem;
  }
  .lg\:ml-12 {
    margin-left: 3rem;
  }
  .lg\:ml-11 {
    margin-left: 2.75rem;
  }
  .lg\:ml-10 {
    margin-left: 2.5rem;
  }
  .lg\:ml-9 {
    margin-left: 2.25rem;
  }
  .lg\:ml-8 {
    margin-left: 2rem;
  }
  .lg\:ml-7 {
    margin-left: 1.75rem;
  }
  .lg\:ml-6 {
    margin-left: 1.5rem;
  }
  .lg\:ml-5 {
    margin-left: 1.25rem;
  }
  .lg\:ml-4 {
    margin-left: 1rem;
  }
  .lg\:ml-3 {
    margin-left: 0.75rem;
  }
  .lg\:ml-2 {
    margin-left: 0.5rem;
  }
  .lg\:ml-1 {
    margin-left: 0.25rem;
  }
  .lg\:ml-0 {
    margin-left: 0rem;
  }
  .lg\:-ml-20 {
    margin-left: -5rem;
  }
  .lg\:-ml-19 {
    margin-left: -4.75rem;
  }
  .lg\:-ml-18 {
    margin-left: -4.5rem;
  }
  .lg\:-ml-17 {
    margin-left: -4.25rem;
  }
  .lg\:-ml-16 {
    margin-left: -4rem;
  }
  .lg\:-ml-15 {
    margin-left: -3.75rem;
  }
  .lg\:-ml-14 {
    margin-left: -3.5rem;
  }
  .lg\:-ml-13 {
    margin-left: -3.25rem;
  }
  .lg\:-ml-12 {
    margin-left: -3rem;
  }
  .lg\:-ml-11 {
    margin-left: -2.75rem;
  }
  .lg\:-ml-10 {
    margin-left: -2.5rem;
  }
  .lg\:-ml-9 {
    margin-left: -2.25rem;
  }
  .lg\:-ml-8 {
    margin-left: -2rem;
  }
  .lg\:-ml-7 {
    margin-left: -1.75rem;
  }
  .lg\:-ml-6 {
    margin-left: -1.5rem;
  }
  .lg\:-ml-5 {
    margin-left: -1.25rem;
  }
  .lg\:-ml-4 {
    margin-left: -1rem;
  }
  .lg\:-ml-3 {
    margin-left: -0.75rem;
  }
  .lg\:-ml-2 {
    margin-left: -0.5rem;
  }
  .lg\:-ml-1 {
    margin-left: -0.25rem;
  }
  .lg\:-ml-0 {
    margin-left: 0rem;
  }
}
@media (min-width: 1280px) {
  .xl\:p-20 {
    padding: 5rem;
  }
  .xl\:p-19 {
    padding: 4.75rem;
  }
  .xl\:p-18 {
    padding: 4.5rem;
  }
  .xl\:p-17 {
    padding: 4.25rem;
  }
  .xl\:p-16 {
    padding: 4rem;
  }
  .xl\:p-15 {
    padding: 3.75rem;
  }
  .xl\:p-14 {
    padding: 3.5rem;
  }
  .xl\:p-13 {
    padding: 3.25rem;
  }
  .xl\:p-12 {
    padding: 3rem;
  }
  .xl\:p-11 {
    padding: 2.75rem;
  }
  .xl\:p-10 {
    padding: 2.5rem;
  }
  .xl\:p-9 {
    padding: 2.25rem;
  }
  .xl\:p-8 {
    padding: 2rem;
  }
  .xl\:p-7 {
    padding: 1.75rem;
  }
  .xl\:p-6 {
    padding: 1.5rem;
  }
  .xl\:p-5 {
    padding: 1.25rem;
  }
  .xl\:p-4 {
    padding: 1rem;
  }
  .xl\:p-3 {
    padding: 0.75rem;
  }
  .xl\:p-2 {
    padding: 0.5rem;
  }
  .xl\:p-1 {
    padding: 0.25rem;
  }
  .xl\:p-0 {
    padding: 0rem;
  }
  .xl\:pt-20 {
    padding-top: 5rem;
  }
  .xl\:pt-19 {
    padding-top: 4.75rem;
  }
  .xl\:pt-18 {
    padding-top: 4.5rem;
  }
  .xl\:pt-17 {
    padding-top: 4.25rem;
  }
  .xl\:pt-16 {
    padding-top: 4rem;
  }
  .xl\:pt-15 {
    padding-top: 3.75rem;
  }
  .xl\:pt-14 {
    padding-top: 3.5rem;
  }
  .xl\:pt-13 {
    padding-top: 3.25rem;
  }
  .xl\:pt-12 {
    padding-top: 3rem;
  }
  .xl\:pt-11 {
    padding-top: 2.75rem;
  }
  .xl\:pt-10 {
    padding-top: 2.5rem;
  }
  .xl\:pt-9 {
    padding-top: 2.25rem;
  }
  .xl\:pt-8 {
    padding-top: 2rem;
  }
  .xl\:pt-7 {
    padding-top: 1.75rem;
  }
  .xl\:pt-6 {
    padding-top: 1.5rem;
  }
  .xl\:pt-5 {
    padding-top: 1.25rem;
  }
  .xl\:pt-4 {
    padding-top: 1rem;
  }
  .xl\:pt-3 {
    padding-top: 0.75rem;
  }
  .xl\:pt-2 {
    padding-top: 0.5rem;
  }
  .xl\:pt-1 {
    padding-top: 0.25rem;
  }
  .xl\:pt-0 {
    padding-top: 0rem;
  }
  .xl\:pr-20 {
    padding-right: 5rem;
  }
  .xl\:pr-19 {
    padding-right: 4.75rem;
  }
  .xl\:pr-18 {
    padding-right: 4.5rem;
  }
  .xl\:pr-17 {
    padding-right: 4.25rem;
  }
  .xl\:pr-16 {
    padding-right: 4rem;
  }
  .xl\:pr-15 {
    padding-right: 3.75rem;
  }
  .xl\:pr-14 {
    padding-right: 3.5rem;
  }
  .xl\:pr-13 {
    padding-right: 3.25rem;
  }
  .xl\:pr-12 {
    padding-right: 3rem;
  }
  .xl\:pr-11 {
    padding-right: 2.75rem;
  }
  .xl\:pr-10 {
    padding-right: 2.5rem;
  }
  .xl\:pr-9 {
    padding-right: 2.25rem;
  }
  .xl\:pr-8 {
    padding-right: 2rem;
  }
  .xl\:pr-7 {
    padding-right: 1.75rem;
  }
  .xl\:pr-6 {
    padding-right: 1.5rem;
  }
  .xl\:pr-5 {
    padding-right: 1.25rem;
  }
  .xl\:pr-4 {
    padding-right: 1rem;
  }
  .xl\:pr-3 {
    padding-right: 0.75rem;
  }
  .xl\:pr-2 {
    padding-right: 0.5rem;
  }
  .xl\:pr-1 {
    padding-right: 0.25rem;
  }
  .xl\:pr-0 {
    padding-right: 0rem;
  }
  .xl\:pb-20 {
    padding-bottom: 5rem;
  }
  .xl\:pb-19 {
    padding-bottom: 4.75rem;
  }
  .xl\:pb-18 {
    padding-bottom: 4.5rem;
  }
  .xl\:pb-17 {
    padding-bottom: 4.25rem;
  }
  .xl\:pb-16 {
    padding-bottom: 4rem;
  }
  .xl\:pb-15 {
    padding-bottom: 3.75rem;
  }
  .xl\:pb-14 {
    padding-bottom: 3.5rem;
  }
  .xl\:pb-13 {
    padding-bottom: 3.25rem;
  }
  .xl\:pb-12 {
    padding-bottom: 3rem;
  }
  .xl\:pb-11 {
    padding-bottom: 2.75rem;
  }
  .xl\:pb-10 {
    padding-bottom: 2.5rem;
  }
  .xl\:pb-9 {
    padding-bottom: 2.25rem;
  }
  .xl\:pb-8 {
    padding-bottom: 2rem;
  }
  .xl\:pb-7 {
    padding-bottom: 1.75rem;
  }
  .xl\:pb-6 {
    padding-bottom: 1.5rem;
  }
  .xl\:pb-5 {
    padding-bottom: 1.25rem;
  }
  .xl\:pb-4 {
    padding-bottom: 1rem;
  }
  .xl\:pb-3 {
    padding-bottom: 0.75rem;
  }
  .xl\:pb-2 {
    padding-bottom: 0.5rem;
  }
  .xl\:pb-1 {
    padding-bottom: 0.25rem;
  }
  .xl\:pb-0 {
    padding-bottom: 0rem;
  }
  .xl\:pl-20 {
    padding-left: 5rem;
  }
  .xl\:pl-19 {
    padding-left: 4.75rem;
  }
  .xl\:pl-18 {
    padding-left: 4.5rem;
  }
  .xl\:pl-17 {
    padding-left: 4.25rem;
  }
  .xl\:pl-16 {
    padding-left: 4rem;
  }
  .xl\:pl-15 {
    padding-left: 3.75rem;
  }
  .xl\:pl-14 {
    padding-left: 3.5rem;
  }
  .xl\:pl-13 {
    padding-left: 3.25rem;
  }
  .xl\:pl-12 {
    padding-left: 3rem;
  }
  .xl\:pl-11 {
    padding-left: 2.75rem;
  }
  .xl\:pl-10 {
    padding-left: 2.5rem;
  }
  .xl\:pl-9 {
    padding-left: 2.25rem;
  }
  .xl\:pl-8 {
    padding-left: 2rem;
  }
  .xl\:pl-7 {
    padding-left: 1.75rem;
  }
  .xl\:pl-6 {
    padding-left: 1.5rem;
  }
  .xl\:pl-5 {
    padding-left: 1.25rem;
  }
  .xl\:pl-4 {
    padding-left: 1rem;
  }
  .xl\:pl-3 {
    padding-left: 0.75rem;
  }
  .xl\:pl-2 {
    padding-left: 0.5rem;
  }
  .xl\:pl-1 {
    padding-left: 0.25rem;
  }
  .xl\:pl-0 {
    padding-left: 0rem;
  }
  .xl\:m-20 {
    margin: 5rem;
  }
  .xl\:m-19 {
    margin: 4.75rem;
  }
  .xl\:m-18 {
    margin: 4.5rem;
  }
  .xl\:m-17 {
    margin: 4.25rem;
  }
  .xl\:m-16 {
    margin: 4rem;
  }
  .xl\:m-15 {
    margin: 3.75rem;
  }
  .xl\:m-14 {
    margin: 3.5rem;
  }
  .xl\:m-13 {
    margin: 3.25rem;
  }
  .xl\:m-12 {
    margin: 3rem;
  }
  .xl\:m-11 {
    margin: 2.75rem;
  }
  .xl\:m-10 {
    margin: 2.5rem;
  }
  .xl\:m-9 {
    margin: 2.25rem;
  }
  .xl\:m-8 {
    margin: 2rem;
  }
  .xl\:m-7 {
    margin: 1.75rem;
  }
  .xl\:m-6 {
    margin: 1.5rem;
  }
  .xl\:m-5 {
    margin: 1.25rem;
  }
  .xl\:m-4 {
    margin: 1rem;
  }
  .xl\:m-3 {
    margin: 0.75rem;
  }
  .xl\:m-2 {
    margin: 0.5rem;
  }
  .xl\:m-1 {
    margin: 0.25rem;
  }
  .xl\:m-0 {
    margin: 0rem;
  }
  .xl\:-m-20 {
    margin: -5rem;
  }
  .xl\:-m-19 {
    margin: -4.75rem;
  }
  .xl\:-m-18 {
    margin: -4.5rem;
  }
  .xl\:-m-17 {
    margin: -4.25rem;
  }
  .xl\:-m-16 {
    margin: -4rem;
  }
  .xl\:-m-15 {
    margin: -3.75rem;
  }
  .xl\:-m-14 {
    margin: -3.5rem;
  }
  .xl\:-m-13 {
    margin: -3.25rem;
  }
  .xl\:-m-12 {
    margin: -3rem;
  }
  .xl\:-m-11 {
    margin: -2.75rem;
  }
  .xl\:-m-10 {
    margin: -2.5rem;
  }
  .xl\:-m-9 {
    margin: -2.25rem;
  }
  .xl\:-m-8 {
    margin: -2rem;
  }
  .xl\:-m-7 {
    margin: -1.75rem;
  }
  .xl\:-m-6 {
    margin: -1.5rem;
  }
  .xl\:-m-5 {
    margin: -1.25rem;
  }
  .xl\:-m-4 {
    margin: -1rem;
  }
  .xl\:-m-3 {
    margin: -0.75rem;
  }
  .xl\:-m-2 {
    margin: -0.5rem;
  }
  .xl\:-m-1 {
    margin: -0.25rem;
  }
  .xl\:-m-0 {
    margin: 0rem;
  }
  .xl\:mt-20 {
    margin-top: 5rem;
  }
  .xl\:mt-19 {
    margin-top: 4.75rem;
  }
  .xl\:mt-18 {
    margin-top: 4.5rem;
  }
  .xl\:mt-17 {
    margin-top: 4.25rem;
  }
  .xl\:mt-16 {
    margin-top: 4rem;
  }
  .xl\:mt-15 {
    margin-top: 3.75rem;
  }
  .xl\:mt-14 {
    margin-top: 3.5rem;
  }
  .xl\:mt-13 {
    margin-top: 3.25rem;
  }
  .xl\:mt-12 {
    margin-top: 3rem;
  }
  .xl\:mt-11 {
    margin-top: 2.75rem;
  }
  .xl\:mt-10 {
    margin-top: 2.5rem;
  }
  .xl\:mt-9 {
    margin-top: 2.25rem;
  }
  .xl\:mt-8 {
    margin-top: 2rem;
  }
  .xl\:mt-7 {
    margin-top: 1.75rem;
  }
  .xl\:mt-6 {
    margin-top: 1.5rem;
  }
  .xl\:mt-5 {
    margin-top: 1.25rem;
  }
  .xl\:mt-4 {
    margin-top: 1rem;
  }
  .xl\:mt-3 {
    margin-top: 0.75rem;
  }
  .xl\:mt-2 {
    margin-top: 0.5rem;
  }
  .xl\:mt-1 {
    margin-top: 0.25rem;
  }
  .xl\:mt-0 {
    margin-top: 0rem;
  }
  .xl\:-mt-20 {
    margin-top: -5rem;
  }
  .xl\:-mt-19 {
    margin-top: -4.75rem;
  }
  .xl\:-mt-18 {
    margin-top: -4.5rem;
  }
  .xl\:-mt-17 {
    margin-top: -4.25rem;
  }
  .xl\:-mt-16 {
    margin-top: -4rem;
  }
  .xl\:-mt-15 {
    margin-top: -3.75rem;
  }
  .xl\:-mt-14 {
    margin-top: -3.5rem;
  }
  .xl\:-mt-13 {
    margin-top: -3.25rem;
  }
  .xl\:-mt-12 {
    margin-top: -3rem;
  }
  .xl\:-mt-11 {
    margin-top: -2.75rem;
  }
  .xl\:-mt-10 {
    margin-top: -2.5rem;
  }
  .xl\:-mt-9 {
    margin-top: -2.25rem;
  }
  .xl\:-mt-8 {
    margin-top: -2rem;
  }
  .xl\:-mt-7 {
    margin-top: -1.75rem;
  }
  .xl\:-mt-6 {
    margin-top: -1.5rem;
  }
  .xl\:-mt-5 {
    margin-top: -1.25rem;
  }
  .xl\:-mt-4 {
    margin-top: -1rem;
  }
  .xl\:-mt-3 {
    margin-top: -0.75rem;
  }
  .xl\:-mt-2 {
    margin-top: -0.5rem;
  }
  .xl\:-mt-1 {
    margin-top: -0.25rem;
  }
  .xl\:-mt-0 {
    margin-top: 0rem;
  }
  .xl\:mr-20 {
    margin-right: 5rem;
  }
  .xl\:mr-19 {
    margin-right: 4.75rem;
  }
  .xl\:mr-18 {
    margin-right: 4.5rem;
  }
  .xl\:mr-17 {
    margin-right: 4.25rem;
  }
  .xl\:mr-16 {
    margin-right: 4rem;
  }
  .xl\:mr-15 {
    margin-right: 3.75rem;
  }
  .xl\:mr-14 {
    margin-right: 3.5rem;
  }
  .xl\:mr-13 {
    margin-right: 3.25rem;
  }
  .xl\:mr-12 {
    margin-right: 3rem;
  }
  .xl\:mr-11 {
    margin-right: 2.75rem;
  }
  .xl\:mr-10 {
    margin-right: 2.5rem;
  }
  .xl\:mr-9 {
    margin-right: 2.25rem;
  }
  .xl\:mr-8 {
    margin-right: 2rem;
  }
  .xl\:mr-7 {
    margin-right: 1.75rem;
  }
  .xl\:mr-6,
  .nav-link,
  .btn-donate,
  .btn-donate-lg {
    margin-right: 1.5rem;
  }
  .xl\:mr-5 {
    margin-right: 1.25rem;
  }
  .xl\:mr-4 {
    margin-right: 1rem;
  }
  .xl\:mr-3 {
    margin-right: 0.75rem;
  }
  .xl\:mr-2 {
    margin-right: 0.5rem;
  }
  .xl\:mr-1 {
    margin-right: 0.25rem;
  }
  .xl\:mr-0 {
    margin-right: 0rem;
  }
  .xl\:-mr-20 {
    margin-right: -5rem;
  }
  .xl\:-mr-19 {
    margin-right: -4.75rem;
  }
  .xl\:-mr-18 {
    margin-right: -4.5rem;
  }
  .xl\:-mr-17 {
    margin-right: -4.25rem;
  }
  .xl\:-mr-16 {
    margin-right: -4rem;
  }
  .xl\:-mr-15 {
    margin-right: -3.75rem;
  }
  .xl\:-mr-14 {
    margin-right: -3.5rem;
  }
  .xl\:-mr-13 {
    margin-right: -3.25rem;
  }
  .xl\:-mr-12 {
    margin-right: -3rem;
  }
  .xl\:-mr-11 {
    margin-right: -2.75rem;
  }
  .xl\:-mr-10 {
    margin-right: -2.5rem;
  }
  .xl\:-mr-9 {
    margin-right: -2.25rem;
  }
  .xl\:-mr-8 {
    margin-right: -2rem;
  }
  .xl\:-mr-7 {
    margin-right: -1.75rem;
  }
  .xl\:-mr-6 {
    margin-right: -1.5rem;
  }
  .xl\:-mr-5 {
    margin-right: -1.25rem;
  }
  .xl\:-mr-4 {
    margin-right: -1rem;
  }
  .xl\:-mr-3 {
    margin-right: -0.75rem;
  }
  .xl\:-mr-2 {
    margin-right: -0.5rem;
  }
  .xl\:-mr-1 {
    margin-right: -0.25rem;
  }
  .xl\:-mr-0 {
    margin-right: 0rem;
  }
  .xl\:mb-20 {
    margin-bottom: 5rem;
  }
  .xl\:mb-19 {
    margin-bottom: 4.75rem;
  }
  .xl\:mb-18 {
    margin-bottom: 4.5rem;
  }
  .xl\:mb-17 {
    margin-bottom: 4.25rem;
  }
  .xl\:mb-16 {
    margin-bottom: 4rem;
  }
  .xl\:mb-15 {
    margin-bottom: 3.75rem;
  }
  .xl\:mb-14 {
    margin-bottom: 3.5rem;
  }
  .xl\:mb-13 {
    margin-bottom: 3.25rem;
  }
  .xl\:mb-12 {
    margin-bottom: 3rem;
  }
  .xl\:mb-11 {
    margin-bottom: 2.75rem;
  }
  .xl\:mb-10 {
    margin-bottom: 2.5rem;
  }
  .xl\:mb-9 {
    margin-bottom: 2.25rem;
  }
  .xl\:mb-8 {
    margin-bottom: 2rem;
  }
  .xl\:mb-7 {
    margin-bottom: 1.75rem;
  }
  .xl\:mb-6 {
    margin-bottom: 1.5rem;
  }
  .xl\:mb-5 {
    margin-bottom: 1.25rem;
  }
  .xl\:mb-4 {
    margin-bottom: 1rem;
  }
  .xl\:mb-3 {
    margin-bottom: 0.75rem;
  }
  .xl\:mb-2 {
    margin-bottom: 0.5rem;
  }
  .xl\:mb-1 {
    margin-bottom: 0.25rem;
  }
  .xl\:mb-0 {
    margin-bottom: 0rem;
  }
  .xl\:-mb-20 {
    margin-bottom: -5rem;
  }
  .xl\:-mb-19 {
    margin-bottom: -4.75rem;
  }
  .xl\:-mb-18 {
    margin-bottom: -4.5rem;
  }
  .xl\:-mb-17 {
    margin-bottom: -4.25rem;
  }
  .xl\:-mb-16 {
    margin-bottom: -4rem;
  }
  .xl\:-mb-15 {
    margin-bottom: -3.75rem;
  }
  .xl\:-mb-14 {
    margin-bottom: -3.5rem;
  }
  .xl\:-mb-13 {
    margin-bottom: -3.25rem;
  }
  .xl\:-mb-12 {
    margin-bottom: -3rem;
  }
  .xl\:-mb-11 {
    margin-bottom: -2.75rem;
  }
  .xl\:-mb-10 {
    margin-bottom: -2.5rem;
  }
  .xl\:-mb-9 {
    margin-bottom: -2.25rem;
  }
  .xl\:-mb-8 {
    margin-bottom: -2rem;
  }
  .xl\:-mb-7 {
    margin-bottom: -1.75rem;
  }
  .xl\:-mb-6 {
    margin-bottom: -1.5rem;
  }
  .xl\:-mb-5 {
    margin-bottom: -1.25rem;
  }
  .xl\:-mb-4 {
    margin-bottom: -1rem;
  }
  .xl\:-mb-3 {
    margin-bottom: -0.75rem;
  }
  .xl\:-mb-2 {
    margin-bottom: -0.5rem;
  }
  .xl\:-mb-1 {
    margin-bottom: -0.25rem;
  }
  .xl\:-mb-0 {
    margin-bottom: 0rem;
  }
  .xl\:ml-20 {
    margin-left: 5rem;
  }
  .xl\:ml-19 {
    margin-left: 4.75rem;
  }
  .xl\:ml-18 {
    margin-left: 4.5rem;
  }
  .xl\:ml-17 {
    margin-left: 4.25rem;
  }
  .xl\:ml-16 {
    margin-left: 4rem;
  }
  .xl\:ml-15 {
    margin-left: 3.75rem;
  }
  .xl\:ml-14 {
    margin-left: 3.5rem;
  }
  .xl\:ml-13 {
    margin-left: 3.25rem;
  }
  .xl\:ml-12 {
    margin-left: 3rem;
  }
  .xl\:ml-11 {
    margin-left: 2.75rem;
  }
  .xl\:ml-10 {
    margin-left: 2.5rem;
  }
  .xl\:ml-9 {
    margin-left: 2.25rem;
  }
  .xl\:ml-8 {
    margin-left: 2rem;
  }
  .xl\:ml-7 {
    margin-left: 1.75rem;
  }
  .xl\:ml-6,
  .nav-link,
  .btn-donate,
  .btn-donate-lg {
    margin-left: 1.5rem;
  }
  .xl\:ml-5 {
    margin-left: 1.25rem;
  }
  .xl\:ml-4 {
    margin-left: 1rem;
  }
  .xl\:ml-3 {
    margin-left: 0.75rem;
  }
  .xl\:ml-2 {
    margin-left: 0.5rem;
  }
  .xl\:ml-1 {
    margin-left: 0.25rem;
  }
  .xl\:ml-0 {
    margin-left: 0rem;
  }
  .xl\:-ml-20 {
    margin-left: -5rem;
  }
  .xl\:-ml-19 {
    margin-left: -4.75rem;
  }
  .xl\:-ml-18 {
    margin-left: -4.5rem;
  }
  .xl\:-ml-17 {
    margin-left: -4.25rem;
  }
  .xl\:-ml-16 {
    margin-left: -4rem;
  }
  .xl\:-ml-15 {
    margin-left: -3.75rem;
  }
  .xl\:-ml-14 {
    margin-left: -3.5rem;
  }
  .xl\:-ml-13 {
    margin-left: -3.25rem;
  }
  .xl\:-ml-12 {
    margin-left: -3rem;
  }
  .xl\:-ml-11 {
    margin-left: -2.75rem;
  }
  .xl\:-ml-10 {
    margin-left: -2.5rem;
  }
  .xl\:-ml-9 {
    margin-left: -2.25rem;
  }
  .xl\:-ml-8 {
    margin-left: -2rem;
  }
  .xl\:-ml-7 {
    margin-left: -1.75rem;
  }
  .xl\:-ml-6 {
    margin-left: -1.5rem;
  }
  .xl\:-ml-5 {
    margin-left: -1.25rem;
  }
  .xl\:-ml-4 {
    margin-left: -1rem;
  }
  .xl\:-ml-3 {
    margin-left: -0.75rem;
  }
  .xl\:-ml-2 {
    margin-left: -0.5rem;
  }
  .xl\:-ml-1 {
    margin-left: -0.25rem;
  }
  .xl\:-ml-0 {
    margin-left: 0rem;
  }
}
.bg-fixed {
  background-attachment: fixed;
}
.bg-local {
  background-attachment: local;
}
.bg-scroll {
  background-attachment: scroll;
}
.bg-bottom {
  background-position: bottom;
}
.bg-center {
  background-position: center;
}
.bg-left {
  background-position: left;
}
.bg-left-bottom {
  background-position: left bottom;
}
.bg-left-top {
  background-position: left top;
}
.bg-right {
  background-position: right;
}
.bg-right-bottom,
.bg-logo {
  background-position: right bottom;
}
.bg-right-top {
  background-position: right top;
}
.bg-top,
.bg-body,
.bg-calendar,
.bg-prefooter {
  background-position: top;
}
.bg-repeat {
  background-repeat: repeat;
}
.bg-no-repeat,
.bg-body,
.bg-logo,
.bg-calendar,
.bg-prefooter {
  background-repeat: no-repeat;
}
.bg-repeat-x {
  background-repeat: repeat-x;
}
.bg-repeat-y {
  background-repeat: repeat-y;
}
.bg-repeat-round {
  background-repeat: round;
}
.bg-repeat-space {
  background-repeat: space;
}
.bg-auto {
  background-size: auto;
}
.bg-cover,
.bg-body,
.bg-calendar,
.bg-prefooter {
  background-size: cover;
}
.bg-contain,
.bg-logo {
  background-size: contain;
}
.bg-header {
  background-color: #1c2f56;
  background-image: url('../img/thunderbird/backgrounds/bg-header.png'), -moz-linear-gradient(top, #1c2f56 0%, #2c9afe 100%);
  background-image: url('../img/thunderbird/backgrounds/bg-header.png'), -webkit-linear-gradient(top, #1c2f56 0%, #2c9afe 100%);
  background-image: url('../img/thunderbird/backgrounds/bg-header.png'), linear-gradient(to bottom, #1c2f56 0%, #2c9afe 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1c2f56', endColorstr='#2c9afe', GradientType=0);
}
.bg-header-page {
  background-color: #1c2f56;
  background-image: url('../img/thunderbird/backgrounds/bg-header.png'), -moz-linear-gradient(top, #1c2f56 0%, #037ae6 100%);
  background-image: url('../img/thunderbird/backgrounds/bg-header.png'), -webkit-linear-gradient(top, #1c2f56 0%, #037ae6 100%);
  background-image: url('../img/thunderbird/backgrounds/bg-header.png'), linear-gradient(to bottom, #1c2f56 0%, #037ae6 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1c2f56', endColorstr='#037ae6', GradientType=0);
}
.bg-donate-button,
.btn-donate,
.btn-donate-lg {
  background: #b92f51;
  background: -moz-linear-gradient(-45deg, #f23d5c 0%, #b92f51 100%);
  background: -webkit-linear-gradient(-45deg, #f23d5c 0%, #b92f51 100%);
  background: linear-gradient(135deg, #f23d5c 0%, #b92f51 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f23d5c', endColorstr='#b92f51', GradientType=1);
}
.bg-download-button,
.btn-download {
  background: #058B00;
  background: -moz-linear-gradient(-45deg, #21B711 0%, #058B00 100%);
  background: -webkit-linear-gradient(-45deg, #21B711 0%, #058B00 100%);
  background: linear-gradient(135deg, #21B711 0%, #058B00 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#21B711', endColorstr='#058B00', GradientType=1);
}
.bg-primary-button,
.btn-newsletter,
.btn-join {
  background: #005CE3;
  background: -moz-linear-gradient(-45deg, #0080FF 0%, #005CE3 100%);
  background: -webkit-linear-gradient(-45deg, #0080FF 0%, #005CE3 100%);
  background: linear-gradient(135deg, #0080FF 0%, #005CE3 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0080FF', endColorstr='#005CE3', GradientType=1);
}
.bg-beta-button,
.btn-beta {
  background: #882395;
  background: -moz-linear-gradient(-45deg, #E74EB9 0%, #882395 100%);
  background: -webkit-linear-gradient(-45deg, #E74EB9 0%, #882395 100%);
  background: linear-gradient(135deg, #E74EB9 0%, #882395 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#E74EB9', endColorstr='#882395', GradientType=1);
}
.bg-daily-button,
.btn-daily {
  background: #890019;
  background: -moz-linear-gradient(-45deg, #EB002D 0%, #890019 100%);
  background: -webkit-linear-gradient(-45deg, #EB002D 0%, #890019 100%);
  background: linear-gradient(135deg, #EB002D 0%, #890019 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#EB002D', endColorstr='#890019', GradientType=1);
}
.bg-body {
  background-image: url('../img/thunderbird/backgrounds/bg-body.png');
}
.bg-logo {
  background-image: none;
  background-position-x: 95%;
}
@media (min-width: 1024px) {
  .bg-logo {
    background-image: url('../img/thunderbird/backgrounds/bg-logo.png');
  }
}
.bg-calendar {
  background-image: url('../img/thunderbird/backgrounds/bg-calendar.png');
}
.bg-prefooter {
  background-image: url('../img/thunderbird/backgrounds/bg-prefooter.png');
}
.pointer-events-none {
  pointer-events: none;
}
.pointer-events-auto {
  pointer-events: auto;
}
a {
  -webkit-transition: all 320ms ease;
  -moz-transition: all 320ms ease;
  -o-transition: all 320ms ease;
  transition: all 320ms ease;
  cursor: pointer;
}
.nav-link {
  white-space: nowrap;
}
.nav-link:after {
  -webkit-transition: all 320ms cubic-bezier(0.55, 0, 0.1, 1);
  -moz-transition: all 320ms cubic-bezier(0.55, 0, 0.1, 1);
  -o-transition: all 320ms cubic-bezier(0.55, 0, 0.1, 1);
  transition: all 320ms cubic-bezier(0.55, 0, 0.1, 1);
  -webkit-transform: translateY(-10px);
  -moz-transform: translateY(-10px);
  -o-transform: translateY(-10px);
  transform: translateY(-10px);
  content: "";
  height: 1px;
}
.nav-link:hover:after,
.nav-link:focus:after {
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}
.social-link-large {
  min-width: 164px;
}
.social-link-large span {
  -webkit-transition: all 320ms ease;
  -moz-transition: all 320ms ease;
  -o-transition: all 320ms ease;
  transition: all 320ms ease;
}
.social-link-large span svg {
  width: 80%;
  height: 80%;
  margin-top: 0.15rem;
}
.social-link span {
  -webkit-transition: all 320ms ease;
  -moz-transition: all 320ms ease;
  -o-transition: all 320ms ease;
  transition: all 320ms ease;
}
.social-link span svg {
  width: 100%;
  height: 100%;
}
.blog-body .go {
  display: none;
}
.blog-body .thumbnail {
  text-align: center;
}
.blog-body .thumbnail img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 200px;
}
button {
  -webkit-transition: all 320ms ease;
  -moz-transition: all 320ms ease;
  -o-transition: all 320ms ease;
  transition: all 320ms ease;
  cursor: pointer;
}
.btn-donate {
  white-space: nowrap;
}
.btn-donate:hover,
.btn-donate:focus {
  -webkit-transform: translateY(-7%);
  -moz-transform: translateY(-7%);
  -o-transform: translateY(-7%);
  transform: translateY(-7%);
}
.btn-donate-lg {
  white-space: nowrap;
}
.btn-donate-lg:hover,
.btn-donate-lg:focus {
  -webkit-transform: translateY(-7%);
  -moz-transform: translateY(-7%);
  -o-transform: translateY(-7%);
  transform: translateY(-7%);
}
.btn-download:hover,
.btn-download:focus {
  -webkit-transform: translateY(-7%);
  -moz-transform: translateY(-7%);
  -o-transform: translateY(-7%);
  transform: translateY(-7%);
}
.btn-download-inline:hover,
.btn-download-inline:focus {
  -webkit-transform: translateY(-7%);
  -moz-transform: translateY(-7%);
  -o-transform: translateY(-7%);
  transform: translateY(-7%);
}
.btn-body:hover,
.btn-body:focus {
  -webkit-transform: translateY(-7%);
  -moz-transform: translateY(-7%);
  -o-transform: translateY(-7%);
  transform: translateY(-7%);
}
.btn-block-white:hover,
.btn-block-white:focus {
  -webkit-transform: translateY(-7%);
  -moz-transform: translateY(-7%);
  -o-transform: translateY(-7%);
  transform: translateY(-7%);
}
.btn-newsletter {
  white-space: nowrap;
}
.btn-newsletter:hover,
.btn-newsletter:focus {
  -webkit-transform: translateY(-7%);
  -moz-transform: translateY(-7%);
  -o-transform: translateY(-7%);
  transform: translateY(-7%);
}
.no-js .btn-newsletter {
  line-height: 3rem;
}
.btn-join:hover,
.btn-join:focus {
  -webkit-transform: translateY(-7%);
  -moz-transform: translateY(-7%);
  -o-transform: translateY(-7%);
  transform: translateY(-7%);
}
.btn-beta:hover,
.btn-beta:focus {
  -webkit-transform: translateY(-7%);
  -moz-transform: translateY(-7%);
  -o-transform: translateY(-7%);
  transform: translateY(-7%);
}
.btn-daily:hover,
.btn-daily:focus {
  -webkit-transform: translateY(-7%);
  -moz-transform: translateY(-7%);
  -o-transform: translateY(-7%);
  transform: translateY(-7%);
}
.download-button .ios-download,
.download-button .linux-arm-download,
.download-button .unrecognized-download,
.download-button .unsupported-download,
.download-button .unsupported-download-osx,
.download-button .nojs-download {
  display: none;
}
.download-button .os_msi,
.download-button .os_winsha1,
.download-button .os_win64,
.download-button .os_linux,
.download-button .os_linux64,
.win7up.x86.x64 .download-button .os_win,
.android .download-button-desktop,
.windows.arm .download-button .os_win,
.linux.arm .download-button .os_linux,
.linux.x86.x64 .download-list .os_linux,
.download-button .os_win,
.download-button .os_osx,
.download-button .os_android,
.download-button .os_ios,
.no-js .download-list,
.other .download-list {
  display: none !important;
}
.win7up.x86.x64 .download-button .os_win64,
.linux .download-button .os_linux,
.linux.x86.x64 .download-button .os_linux64,
.windows .download-button .os_win,
.osx .download-button .os_osx,
.android .download-button .os_android,
.download-button-android .os_android,
.android .download-button-desktop .download-list,
.android .download-button-desktop small.os_win,
.download-button-ios .os_ios,
.ios .download-button .os_ios,
.ios .download-button .ios-download,
.ios .download-button-desktop .download-list,
.other .download-button-android .download-list,
.other .download-button small.os_win {
  display: block !important;
}
.windows.arm .download-button .unsupported-download,
.linux.arm .download-button .linux-arm-download,
.chromeos .download-button .unsupported-download,
.oldwin .download-button .unsupported-download,
.oldmac .download-button .unsupported-download {
  display: block;
  max-width: 250px;
}
.windows.arm .download-button .fx-privacy-link,
.linux.arm .download-button .fx-privacy-link,
.chromeos .download-button .fx-privacy-link,
.oldwin .download-button .fx-privacy-link,
.oldmac .download-button .fx-privacy-link {
  display: none;
}
.android .download-button-desktop .nojs-download,
.ios .download-button-desktop .nojs-download,
.no-js .download-button .nojs-download {
  display: block;
}
.other .download-button .unrecognized-download {
  display: block;
}
.download-button .download-list .os_android.x86,
.download-button .download-other.os_android .api-15,
.android.x86 .download-button .download-list .os_android.armv7up,
.android.x86 .download-button .download-other.os_android .x86 {
  display: none !important;
}
.android.x86 .download-button .download-list .os_android.x86 {
  display: block !important;
}
.android.x86 .download-button .download-other.os_android .armv7up {
  display: inline !important;
}
.windows.sha-1 .download-button .os_win {
  display: none !important;
}
.windows.sha-1 .download-button .os_winsha1 {
  display: block !important;
}
.no-hover:hover,
.no-hover:focus {
  box-shadow: unset;
  transform: unset;
}
#modal-overlay {
  z-index: 9;
}
.modal {
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}
.modal #close-modal {
  cursor: pointer;
}
.modal #close-modal svg {
  stroke: #f9f9f9;
}
.modal #close-modal:hover svg {
  stroke: #000;
}
.modal form .amount-selection label {
  cursor: pointer;
  box-sizing: border-box;
}
.modal form .amount-selection label:last-child {
  flex-basis: 10rem;
}
.modal form .amount-selection label:last-child #amount-other {
  box-sizing: border-box;
}
.modal footer ul > li:not(:last-child)::after {
  content: '•';
}
.ways-to-give-list-info {
  list-style: none;
}
.force-smooth-animation,
.btn-donate-and-download,
.btn-donate-and-download:hover,
.btn-donate-and-download:focus {
  rotate: 0.1deg;
}
#donate-buttons {
  max-height: 60px;
}
.btn-donate-and-download {
  contain: paint;
}
@media (prefers-reduced-motion: no-preference) {
  .btn-donate-and-download .donate-wave {
    animation: linear 1.2s heart-wave infinite;
  }
  .btn-donate-and-download .donate-heart svg {
    animation: linear 0.6s alternate heart-bounce infinite;
  }
}
.btn-donate-and-download .donate-wave {
  display: block;
  position: relative;
  margin: auto;
  left: -246px;
  top: -269.33333333px;
  width: 512px;
  height: 512px;
  border-radius: 50%;
}
.btn-donate-and-download .donate-heart {
  margin-top: 0.15rem;
  width: 20px;
  height: 20px;
}
.btn-donate-and-download:hover,
.btn-donate-and-download:focus {
  -webkit-transform: translateY(-7%);
  -moz-transform: translateY(-7%);
  -o-transform: translateY(-7%);
  transform: translateY(-7%);
  background-color: rgba(0, 128, 255, 0.1);
}
@keyframes heart-bounce {
  from {
    scale: 1;
  }
  to {
    scale: 1.3;
  }
}
@keyframes heart-wave {
  from {
    scale: 0;
  }
  25% {
    scale: 0;
    background-color: rgba(0, 128, 255, 0.4);
  }
  35% {
    background-color: rgba(0, 128, 255, 0.15);
  }
  to {
    scale: 2;
    background-color: rgba(0, 128, 255, 0);
  }
}
/* Retry Banner: "(i) Your download didn't start automatically? [Try Again]" */
.retry-download .retry-text {
  margin-left: auto;
  margin-right: 0;
}
.retry-download .retry-button {
  margin-right: 0;
}
/* Download buttons for the retry banner */
.no-js .download-hidden {
  display: block;
}
.faq-entry summary {
  list-style: none;
}
.faq-entry summary::-webkit-details-marker {
  display: none;
}
.faq-entry .faq-question {
  cursor: pointer;
  display: inline-list-item;
}
.faq-entry .faq-question::before {
  content: url("/media/svg/zoom-out.svg");
}
.faq-entry[open] .faq-question::before {
  content: url("/media/svg/zoom-in.svg");
}
.appearance-none,
.btn-inline,
.btn-newsletter,
.form-select,
.newsletter-form input {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  position: relative;
}
h1 .anchor,
h2 .anchor,
h3 .anchor,
h4 .anchor,
h5 .anchor,
h6 .anchor {
  float: left;
  padding-right: 4px;
  margin-left: -20px;
  line-height: 1;
  opacity: 0;
}
h1 .anchor svg,
h2 .anchor svg,
h3 .anchor svg,
h4 .anchor svg,
h5 .anchor svg,
h6 .anchor svg {
  width: 16px;
  height: 16px;
}
h1:hover .anchor,
h2:hover .anchor,
h3:hover .anchor,
h4:hover .anchor,
h5:hover .anchor,
h6:hover .anchor,
h1:focus .anchor,
h2:focus .anchor,
h3:focus .anchor,
h4:focus .anchor,
h5:focus .anchor,
h6:focus .anchor {
  opacity: 1;
}
.header-section span {
  border-width: 6px;
  border-style: solid;
}
.header-section span svg {
  width: 24px;
  height: 24px;
}
.header-line {
  height: 1px;
}
.tabs .tab {
  display: none;
}
#release:checked ~ section.tab.release,
#beta:checked ~ section.tab.beta,
#daily:checked ~ section.tab.daily {
  display: flex;
}
.tabs-nav label {
  cursor: pointer;
}
/*
 * The form assembly theming is located in a .css as it had to be built from a scss file...
 * This file is just to fix the form on mobile
 */
@media (max-width: 768px) {
  .wFormContainer form,
  .wFormContainer input {
    padding: 0 !important;
  }
  .wFormContainer label,
  .wFormContainer select,
  .wFormContainer input,
  .wFormContainer textarea,
  .wFormContainer .oneField,
  .wFormContainer .inputWrapper {
    width: 100% !important;
  }
}
