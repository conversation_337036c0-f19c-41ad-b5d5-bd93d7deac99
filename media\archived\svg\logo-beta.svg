<svg width="1024" height="1024" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_4383_20881)">
<mask id="mask0_4383_20881" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="1024" height="1024">
<path d="M1024 0H0V1024H1024V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_4383_20881)">
<path d="M863.859 637.6H491.007C480.028 637.6 471.123 646.505 471.123 657.484V717.136C471.123 805.003 542.341 876.221 630.208 876.221H836.518C902.403 876.221 955.823 822.801 955.823 756.917V704.708C955.823 674.882 925.996 637.6 863.859 637.6Z" fill="#008787"/>
<path d="M437.528 263.376H437.61C464.276 170.158 580.472 124.648 698.883 124.648C780.693 124.648 854.155 150.56 904.502 191.678C873.312 193.225 843.639 199.327 816.479 209.177C857.163 224.291 892.113 247.56 918.021 276.346C900.834 273.376 883.007 271.813 864.73 271.813C862.758 271.813 860.792 271.832 858.832 271.868C905.897 340.106 933.456 422.834 933.456 512C933.456 745.863 743.872 935.447 510.009 935.447C279.749 935.447 86.5605 742.654 86.5605 511.999C86.5605 475.571 91.4093 437.947 100.805 402.683C103.264 395.276 106.702 388.172 111.267 385.574C116.981 382.321 122.174 392.018 123.015 395.176C129.221 418.475 137.582 441.008 147.895 462.593C146.994 414.241 167.645 370.212 196.061 332.117C215.001 306.725 232.56 283.187 240.669 215.28C241.213 210.721 245.536 207.44 249.899 208.868C311.559 229.056 344.521 331.721 339.389 417.571C373.452 422.44 373.295 386.861 373.295 386.861C362.406 353.401 369.666 291.202 437.417 263.376H437.528Z" fill="url(#paint0_linear_4383_20881)"/>
<path opacity="0.9" d="M919.603 404.166C929.9 637.066 738.441 837.857 504.968 837.857C286.415 837.857 107.316 668.931 91.116 454.523C88.2617 474.331 86.7166 494.562 86.5713 515.126C88.2299 744.564 280.951 935.448 510.009 935.448C743.872 935.448 933.456 745.864 933.456 512.001C933.456 474.735 928.642 438.594 919.603 404.166Z" fill="url(#paint1_radial_4383_20881)"/>
<g style="mix-blend-mode:screen">
<path d="M499.692 294.24C495.127 286.164 474.061 274.227 464.858 272.144C499.692 160.539 677.208 126.274 785.854 146.02C831.06 154.237 887.375 178.886 904.501 191.678C854.154 150.56 780.693 124.648 698.883 124.648C580.472 124.648 464.275 170.158 437.61 263.376H437.528H437.417C369.666 291.202 362.414 353.42 373.303 386.88C383.75 346.968 433.492 297.922 499.692 294.24Z" fill="url(#paint2_radial_4383_20881)"/>
</g>
<path d="M622.743 217.163C527.626 235.878 496.541 241.994 464.705 272.257C500.459 177.603 591.725 158.425 700.468 201.566C670.538 207.759 644.903 212.803 622.743 217.163Z" fill="url(#paint3_linear_4383_20881)"/>
<path d="M109.546 389.721C83.5616 496.111 103.641 621.162 221.697 726.105C186.55 687.676 143.645 545.758 238.335 444.365C244.714 437.535 255.688 442.562 256.033 451.901C263.834 662.604 433.865 791.295 629.878 767.23C569.142 763.817 368.285 693.472 517.698 665.637C595.798 651.087 718.252 628.275 718.252 518.412C718.252 340.287 580.525 288.21 497.017 295.958C439.859 301.262 388.99 337.527 373.328 386.848C379.338 406.296 355.379 419.912 339.389 417.626C344.521 331.776 311.559 229.057 249.899 208.869C245.536 207.441 241.213 210.722 240.669 215.281C232.56 283.188 215.001 306.726 196.061 332.118C167.645 370.213 146.994 414.242 147.895 462.594C137.582 441.009 129.221 418.476 123.015 395.177C122.319 392.562 118.537 385.275 113.96 384.931C111.482 384.745 110.171 387.166 109.546 389.721Z" fill="url(#paint4_radial_4383_20881)"/>
<g style="mix-blend-mode:screen">
<path d="M465.232 676.419C580.211 769.779 811.456 699.785 811.456 472.773C718.095 614.288 599.192 711.893 465.232 676.419Z" fill="url(#paint5_linear_4383_20881)"/>
</g>
<g style="mix-blend-mode:screen">
<path d="M238.332 444.363C240.76 441.763 243.856 440.871 246.781 441.277C162.019 544.651 230.382 726.219 277.305 770.819C279.927 778.247 232.796 739.624 226.297 730.579C190.609 700.299 139.457 550.237 238.332 444.363Z" fill="url(#paint6_linear_4383_20881)"/>
</g>
<path d="M510.006 684.969C625.021 684.969 718.258 608.892 718.258 515.045C718.258 421.199 625.021 345.121 510.006 345.121C411.887 345.121 301.717 408.956 301.753 517.525C301.809 685.303 479.05 781.821 630.133 767.21C618.785 765.894 547.989 762.133 500.137 708.001C495.822 703.12 488.33 694.609 491.728 689.076C495.126 683.542 504.462 684.969 510.006 684.969Z" fill="url(#paint7_linear_4383_20881)"/>
<path opacity="0.6" d="M695.573 437.845L530.991 595.321C516.384 605.697 500.837 606.461 485.487 597.037L324.08 438.412C328.627 431.083 333.805 424.059 339.556 417.387C345.372 422.826 351.003 428.1 356.498 433.247C398.901 472.963 433.175 505.066 481.48 546.293C503.287 564.905 510.064 564.538 531.442 546.293C586.718 499.116 627.13 463.413 679.68 416.5C685.591 423.263 690.909 430.395 695.573 437.845Z" fill="white"/>
<mask id="mask1_4383_20881" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="301" y="435" width="418" height="334">
<path d="M718.258 515.043C718.258 608.89 625.02 684.967 510.005 684.967C504.462 684.967 495.126 683.54 491.728 689.074C488.33 694.607 495.822 703.118 500.137 707.999C545.098 758.861 610.316 765.254 627.443 766.932C628.544 767.04 629.447 767.129 630.132 767.208C479.05 781.819 301.809 685.301 301.753 517.523C301.743 487.667 310.067 461.193 324.184 438.5L486.398 585.83C497.94 596.314 517.383 596.314 528.925 585.83L694.214 435.707C709.565 459.389 718.258 486.388 718.258 515.043Z" fill="white"/>
</mask>
<g mask="url(#mask1_4383_20881)">
<path opacity="0.7" d="M779.896 319.062H253.524V796.313H779.896V319.062Z" fill="url(#paint8_linear_4383_20881)"/>
<g filter="url(#filter0_f_4383_20881)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M461.668 586.828C430.998 554.907 339.139 448.873 339.139 448.873L346.25 449.179L490.045 556.583C500.819 564.408 516.175 564.33 526.851 556.395L667.855 449.317L675.341 448.736C675.341 448.736 586.487 552.258 552.454 586.364C518.421 620.471 492.338 618.75 461.668 586.828Z" fill="#458FCD"/>
</g>
</g>
<path d="M539.224 260.126C561.51 253.108 559.56 231.045 559.56 231.045C559.56 231.045 548.409 217.927 526.33 225.197C505.661 232.004 502.458 246.72 502.458 246.72C502.458 246.72 513.749 268.148 539.224 260.126Z" fill="white"/>
<g filter="url(#filter1_d_4383_20881)">
<path d="M935.94 724.591H593.164C512.041 724.591 446.269 790.364 446.269 871.486V943.329C446.269 954.32 455.174 963.213 466.153 963.213H808.929C890.051 963.213 955.824 897.453 955.824 816.318V704.707C955.824 715.698 946.918 724.591 935.94 724.591Z" fill="#20123A"/>
</g>
<path opacity="0.9" d="M863.859 637.6H491.007C480.028 637.6 471.123 646.505 471.123 657.484V717.136C471.123 805.003 542.341 876.221 630.208 876.221H836.518C902.403 876.221 955.823 822.801 955.823 756.917V704.708C955.823 674.882 925.996 637.6 863.859 637.6Z" fill="url(#paint9_linear_4383_20881)"/>
<path d="M935.94 724.591H593.164C512.041 724.591 446.269 790.364 446.269 871.486V943.329C446.269 954.32 455.174 963.213 466.153 963.213H808.929C890.051 963.213 955.824 897.453 955.824 816.318V704.707C955.824 715.698 946.918 724.591 935.94 724.591Z" fill="url(#paint10_linear_4383_20881)"/>
<path d="M534.547 794.549H575.714C597.809 794.549 610.212 804.503 610.212 822.364C610.376 826.46 609.39 830.521 607.366 834.086C605.342 837.65 602.36 840.577 598.758 842.535C607.077 846.769 611.711 854.263 611.711 864.63C611.711 884.264 598.072 894.082 575.989 894.082H534.547V794.549ZM553.495 811.598V834.629H576.401C586.631 834.629 590.99 829.596 590.99 822.776C590.99 815.819 586.206 811.598 576.127 811.598H553.495ZM553.495 851.953V876.758H576.264C586.768 876.758 592.214 871.987 592.214 864.218C592.214 856.462 587.592 851.953 575.452 851.953H553.495ZM630.108 794.549H697.042V812.684H649.331V835.041H697.042V853.177H649.331V875.809H697.042V894.082H630.108V794.549ZM741.219 812.272H713.542V794.561H787.57V812.272H760.167V894.082H741.207L741.219 812.272ZM820.144 794.549H839.091L876.711 894.082H856.952L850.407 875.946H808.828L802.283 894.082H782.511L820.144 794.549ZM815.235 858.498H844L829.686 818.954L815.235 858.498Z" fill="#20123A"/>
</g>
</g>
<defs>
<filter id="filter0_f_4383_20881" x="300.423" y="410.021" width="413.633" height="240.053" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="19.3576" result="effect1_foregroundBlur_4383_20881"/>
</filter>
<filter id="filter1_d_4383_20881" x="346.349" y="579.807" width="709.395" height="458.346" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-24.98"/>
<feGaussianBlur stdDeviation="49.96"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0352941 0 0 0 0 0.12549 0 0 0 0 0.301961 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4383_20881"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4383_20881" result="shape"/>
</filter>
<linearGradient id="paint0_linear_4383_20881" x1="233.535" y1="264.218" x2="830.469" y2="821.022" gradientUnits="userSpaceOnUse">
<stop stop-color="#1B91F3"/>
<stop offset="1" stop-color="#0B68CB"/>
</linearGradient>
<radialGradient id="paint1_radial_4383_20881" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(232.436 455.807) rotate(66.5179) scale(358.064 343.016)">
<stop offset="0.525579" stop-color="#0B4186" stop-opacity="0"/>
<stop offset="1" stop-color="#0B4186" stop-opacity="0.45"/>
</radialGradient>
<radialGradient id="paint2_radial_4383_20881" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(520.895 308.745) rotate(-127.994) scale(76.5485 126.669)">
<stop stop-color="#EF3ACC" stop-opacity="0"/>
<stop offset="1" stop-color="#EF3ACC" stop-opacity="0.64"/>
</radialGradient>
<linearGradient id="paint3_linear_4383_20881" x1="399.632" y1="406.755" x2="614.429" y2="167.639" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F5DB0"/>
<stop offset="1" stop-color="#0F5DB0" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_4383_20881" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(350.307 742.717) rotate(-64.2627) scale(558.612 690.047)">
<stop offset="0.0160882" stop-color="#094188"/>
<stop offset="0.967387" stop-color="#0B4186" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint5_linear_4383_20881" x1="776.075" y1="579.891" x2="676.209" y2="825.111" gradientUnits="userSpaceOnUse">
<stop stop-color="#E247C4" stop-opacity="0"/>
<stop offset="1" stop-color="#E247C4" stop-opacity="0.64"/>
</linearGradient>
<linearGradient id="paint6_linear_4383_20881" x1="156.742" y1="359.965" x2="244.73" y2="714.113" gradientUnits="userSpaceOnUse">
<stop offset="0.104632" stop-color="#EF3ACC"/>
<stop offset="1" stop-color="#EF3ACC" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_4383_20881" x1="510.013" y1="407.35" x2="510.013" y2="765.465" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.90535" stop-color="#BEE1FE"/>
<stop offset="1" stop-color="#96CEFD"/>
</linearGradient>
<linearGradient id="paint8_linear_4383_20881" x1="516.71" y1="609.997" x2="516.71" y2="760.019" gradientUnits="userSpaceOnUse">
<stop stop-color="#BCE0FD"/>
<stop offset="1" stop-color="#88CCFC"/>
</linearGradient>
<linearGradient id="paint9_linear_4383_20881" x1="853.995" y1="866.504" x2="724.941" y2="744.323" gradientUnits="userSpaceOnUse">
<stop stop-color="#054096" stop-opacity="0.5"/>
<stop offset="0.054" stop-color="#0F3D9C" stop-opacity="0.441"/>
<stop offset="0.261" stop-color="#2F35B1" stop-opacity="0.249"/>
<stop offset="0.466" stop-color="#462FBF" stop-opacity="0.111"/>
<stop offset="0.669" stop-color="#542BC8" stop-opacity="0.028"/>
<stop offset="0.864" stop-color="#592ACB" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint10_linear_4383_20881" x1="502.237" y1="746.485" x2="968.126" y2="943.615" gradientUnits="userSpaceOnUse">
<stop offset="0.001" stop-color="#54FFBD"/>
<stop offset="1" stop-color="#00DDFF"/>
</linearGradient>
<clipPath id="clip0_4383_20881">
<rect width="1024" height="1024" fill="white"/>
</clipPath>
</defs>
</svg>
