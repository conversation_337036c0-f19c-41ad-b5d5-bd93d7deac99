BEGIN:VCALENDAR
PRODID:-//Mozilla.org/NONSGML Mozilla Calendar V1.1//EN
VERSION:2.0
BEGIN:VEVENT
CREATED:20201220T082421Z
LAST-MODIFIED:20201220T083715Z
DTSTAMP:20201220T083715Z
UID:51195128-4ac1-4b25-807b-208d98801735
SUMMARY:Easter Monday
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210405
DTEND;VALUE=DATE:20210406
TRANSP:TRANSPARENT
DESCRIPTION:Public Holiday as part of Easter.\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T062224Z
LAST-MODIFIED:20201220T083833Z
DTSTAMP:20201220T083833Z
UID:25bc90a3-dcfd-410f-b702-edb9f10d9634
SUMMARY:Good Friday
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210402
DTEND;VALUE=DATE:20210403
TRANSP:TRANSPARENT
DESCRIPTION:Easter is celebrated with Good Friday and Easter Monday creati
 ng a 4 day long weekend.\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T085329Z
LAST-MODIFIED:20201220T085446Z
DTSTAMP:20201220T085446Z
UID:4c624c60-15fa-4702-a205-bdf9d0ec3fa2
SUMMARY:Boxing Day (additional day)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20211228
DTEND;VALUE=DATE:20211229
TRANSP:TRANSPARENT
DESCRIPTION:As 26 December (Boxing Day) falls on a Sunday in 2021\, there 
 is an additional public holiday on the Tuesday.\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T082344Z
LAST-MODIFIED:20201220T100135Z
DTSTAMP:20201220T100135Z
UID:08c23174-0f5a-4447-b181-0419168f6140
SUMMARY:Easter Sunday (ACT\, NSW\, QLD\, VIC)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210404
DTEND;VALUE=DATE:20210405
TRANSP:TRANSPARENT
DESCRIPTION:Public Holiday in ACT\, NSW\, QLD\, and VIC
X-MOZ-GENERATION:3
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T100916Z
LAST-MODIFIED:20201220T101019Z
DTSTAMP:20201220T101019Z
UID:f1dfdc29-2ed2-4ee0-a18e-9500d3dca9ab
SUMMARY:Labour Day (WA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210301
DTEND;VALUE=DATE:20210302
TRANSP:TRANSPARENT
DESCRIPTION:Always on a Monday\, creating a long weekend. It celebrates th
 e eight-hour working day\, a victory for workers in the mid-late 19th cent
 ury. WA only
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T101050Z
LAST-MODIFIED:20201220T101247Z
DTSTAMP:20201220T101247Z
UID:1b1da3aa-a17c-48ee-97ca-59f06666c6f5
SUMMARY:Eight Hours Day (TAS)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210308
DTEND;VALUE=DATE:20210309
TRANSP:TRANSPARENT
DESCRIPTION:Always on a Monday\, creating a long weekend. It celebrates th
 e eight-hour working day\, a victory for workers in the mid-late 19th cent
 ury. TAS only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T101309Z
LAST-MODIFIED:20201220T101520Z
DTSTAMP:20201220T101520Z
UID:64e02030-0624-4c34-aca9-6f638f1d97c8
SUMMARY:Labour Day (VIC)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210308
DTEND;VALUE=DATE:20210309
TRANSP:TRANSPARENT
DESCRIPTION:Always on a Monday\, creating a long weekend. It celebrates th
 e eight-hour working day\, a victory for workers in the mid-late 19th cent
 ury. VIC only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T101654Z
LAST-MODIFIED:20201220T101730Z
DTSTAMP:20201220T101730Z
UID:15762d5d-19d4-4cc1-863a-baee185d09f4
SUMMARY:Adelaide Cup Day (SA)
STATUS:CONFIRMED
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210308
DTEND;VALUE=DATE:20210309
DESCRIPTION:The Holidays Act 1910 provides for the third Monday in May to 
 be a public holiday. ch through the issuing of a special Proclamation by t
 he Governor. SA only\n
X-LIC-ERROR:No value for LOCATION property. Removing entire property:
SEQUENCE:0
TRANSP:TRANSPARENT
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T101800Z
LAST-MODIFIED:20201220T101856Z
DTSTAMP:20201220T101856Z
UID:5018f863-be0e-4b5f-8d67-6826baddab89
SUMMARY:Canberra Day (ACT)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210308
DTEND;VALUE=DATE:20210309
TRANSP:TRANSPARENT
DESCRIPTION:Held on the second Monday of March each year in Canberra to ce
 lebrate the naming ceremony of Australia's capital which took place on 12 
 March 1913. ACT only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T095751Z
LAST-MODIFIED:20201220T101945Z
DTSTAMP:20201220T101945Z
UID:7703b228-b6c2-417c-bac4-732094eb08b2
SUMMARY:Easter Tuesday (TAS)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210406
DTEND;VALUE=DATE:20210407
TRANSP:TRANSPARENT
DESCRIPTION:Public Holiday currently observed by certain awards/agreements
  and the State Public Service. TAS only\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T082303Z
LAST-MODIFIED:20201220T102706Z
DTSTAMP:20201220T102706Z
UID:623dfb50-15f0-4441-89de-0349a4cfc76d
SUMMARY:Easter Saturday (ACT\, NT\, NSW\, QLD\, SA\, and VIC)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210403
DTEND;VALUE=DATE:20210404
TRANSP:TRANSPARENT
DESCRIPTION:Public Holiday in ACT\, NT\, NSW\, QLD\, SA\, and VIC 
X-MOZ-GENERATION:5
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T102720Z
LAST-MODIFIED:20201220T103748Z
DTSTAMP:20201220T103748Z
UID:9d894986-c068-47f9-8fd4-b9cc1c946e13
SUMMARY:Anzac Day (Public Holiday) (ACT\, NT\, QLD\, SA\, WA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210426
DTEND;VALUE=DATE:20210427
TRANSP:TRANSPARENT
DESCRIPTION:Celebrated on the 25 April each year. The 26 April is a Public
  Holiday in ACT\, NT\, QLD\, SA\, and WA.\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T105953Z
LAST-MODIFIED:20201220T110050Z
DTSTAMP:20201220T110050Z
UID:7b66d012-58d1-4c88-a21a-2caa198ec93a
SUMMARY:Labour Day (QLD)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210503
DTEND;VALUE=DATE:20210504
TRANSP:TRANSPARENT
DESCRIPTION:Always on a Monday\, creating a long weekend. It celebrates th
 e eight-hour working day\, a victory for workers in the mid-late 19th cent
 ury. QLD only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T110128Z
LAST-MODIFIED:20201220T110401Z
DTSTAMP:20201220T110401Z
UID:2a9a47bd-4068-4dc1-a5fb-293f93abda43
SUMMARY:May Day (NT)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210503
DTEND;VALUE=DATE:20210504
TRANSP:TRANSPARENT
DESCRIPTION:Always on a Monday\, creating a long weekend. It celebrates th
 e eight-hour working day\, a victory for workers in the mid-late 19th cent
 ury. NT only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T110417Z
LAST-MODIFIED:20201220T110514Z
DTSTAMP:20201220T110514Z
UID:a4f8f39a-6675-490c-a098-07b4629fb8e3
SUMMARY:Reconciliation Day (ACT)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210531
DTEND;VALUE=DATE:20210601
TRANSP:TRANSPARENT
DESCRIPTION:Takes place on the first Monday on or after 27 May each year t
 he anniversary of the 1967 referendum. ACT only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T110607Z
LAST-MODIFIED:20201220T110747Z
DTSTAMP:20201220T110747Z
UID:329c6567-02a5-49cd-a875-a273a372863e
SUMMARY:Western Australia Day (WA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210607
DTEND;VALUE=DATE:20210608
TRANSP:TRANSPARENT
DESCRIPTION:Held on the first Monday in June each year and is a state holi
 day only. WA only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T083338Z
LAST-MODIFIED:20201220T111124Z
DTSTAMP:20201220T111124Z
UID:13b1c4d3-016f-4545-8eeb-a3e278e3b431
SUMMARY:Queen's Birthday (ACT\, NT\, NSW\, SA\, TAS\, VIC)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210614
DTEND;VALUE=DATE:20210615
TRANSP:TRANSPARENT
DESCRIPTION:Celebrated on second Monday in June except in Western Australi
 a and Queensland.\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T111138Z
LAST-MODIFIED:20201220T111243Z
DTSTAMP:20201220T111243Z
UID:e43ee4fd-d9fa-4a1b-8986-d581a1c6ff99
SUMMARY:Picnic Day (NT)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:********
DTEND;VALUE=DATE:********
TRANSP:TRANSPARENT
DESCRIPTION:Observed on the first Monday of August each year and is celebr
 ated with a horse race\, railway picnic and other social outings. NT only\
 n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T111329Z
LAST-MODIFIED:20201220T111631Z
DTSTAMP:20201220T111631Z
UID:5d697389-8fed-4fae-ab56-38b282f9f3f4
SUMMARY:Bank Holiday (NSW banks and financial institutions)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:********
DTEND;VALUE=DATE:********
TRANSP:TRANSPARENT
DESCRIPTION:Applies to banks and certain financial institutions\, per the 
 Retail Trading Act 2008. NSW only
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T111722Z
LAST-MODIFIED:20201220T111810Z
DTSTAMP:20201220T111810Z
UID:fc572826-4d97-4a6f-8dc2-c0c01203d9ef
SUMMARY:Friday before AFL Grand Final (VIC)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:********
DTEND;VALUE=DATE:********
TRANSP:TRANSPARENT
DESCRIPTION:Friday before AFL Grand Final. VIC only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T111910Z
LAST-MODIFIED:20201220T112048Z
DTSTAMP:20201220T112048Z
UID:50fcc506-cf92-4252-95ea-ce05a648afb7
SUMMARY:Queen's Birthday (WA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:********
DTEND;VALUE=DATE:********
TRANSP:TRANSPARENT
DESCRIPTION:Celebrated on second Monday in June except in Western Australi
 a and Queensland. WA only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T112137Z
LAST-MODIFIED:20201220T112246Z
DTSTAMP:20201220T112246Z
UID:ec893a24-3fe1-42aa-b83e-9dea8ea8d6ee
SUMMARY:Queen's Birthday (QLD)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20211004
DTEND;VALUE=DATE:20211005
TRANSP:TRANSPARENT
DESCRIPTION:Celebrated on second Monday in June except in Western Australi
 a and Queensland. QLD only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T112411Z
LAST-MODIFIED:20201220T112506Z
DTSTAMP:20201220T112506Z
UID:9d8630b0-a2f5-4a39-968b-af130efca284
SUMMARY:Melbourne Cup (VIC)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20211102
DTEND;VALUE=DATE:20211103
TRANSP:TRANSPARENT
DESCRIPTION:All of Victoria unless alternate local holiday has been arrang
 ed by non-metro council. VIC only
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T112844Z
LAST-MODIFIED:20201220T113004Z
DTSTAMP:20201220T113004Z
UID:20d8599b-cade-4219-8c9a-540b63904889
SUMMARY:Christmas Eve 6pm - Midnight (QLD)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20211224
DTEND;VALUE=DATE:20211225
TRANSP:TRANSPARENT
DESCRIPTION:Part day public holiday from 6pm to midnight. QLD only\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T112605Z
LAST-MODIFIED:20201220T113014Z
DTSTAMP:20201220T113014Z
UID:67abce39-cb92-4154-a0b4-0fff943e5cac
SUMMARY:Christmas Eve 7pm - Midnight (NT\, SA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20211224
DTEND;VALUE=DATE:20211225
TRANSP:TRANSPARENT
DESCRIPTION:Part-day public holiday from 7pm-12 midnight. NT and SA only
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T114055Z
LAST-MODIFIED:20201220T114146Z
DTSTAMP:20201220T114146Z
UID:4f8c9b58-48b0-4b7b-8555-bfec590756fb
SUMMARY:Boxing Day / Proclamation Day (additional day) (SA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20211228
DTEND;VALUE=DATE:20211229
TRANSP:TRANSPARENT
DESCRIPTION:As 26 December (Proclamation/Boxing Day) falls on a Sunday in 
 2021\, there is an additional public holiday on the Tuesday. SA only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T085207Z
LAST-MODIFIED:20201220T114314Z
DTSTAMP:20201220T114314Z
UID:adcde1f9-7b19-4eec-ae5e-f4d254a99f46
SUMMARY:Christmas Day (additional day)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20211227
DTEND;VALUE=DATE:20211228
TRANSP:TRANSPARENT
DESCRIPTION:As 25 December (Christmas Day) falls on a Saturday in 2021\, t
 here is an additional public holiday on the Monday.\n
X-MOZ-GENERATION:2
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T114416Z
LAST-MODIFIED:20201220T114552Z
DTSTAMP:20201220T114552Z
UID:d9fc726a-d796-47ac-9e52-78e1f09ba958
SUMMARY:New Year's Eve 7pm - Midnight (NT\, SA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20211231
DTEND;VALUE=DATE:20220101
TRANSP:TRANSPARENT
DESCRIPTION:Part-day public holiday from 7pm-12 midnight. NT and SA only\n
 
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T120008Z
LAST-MODIFIED:20201220T120131Z
DTSTAMP:20201220T120131Z
UID:6b6aed69-6359-410d-a9bb-29846d535c7b
SUMMARY:New Year's Day (additional day)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220103
DTEND;VALUE=DATE:20220104
TRANSP:TRANSPARENT
DESCRIPTION:As 1 January 2022 falls on a Saturday in 2022\, the following 
 Monday is observed as an additional public holiday.
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T120321Z
LAST-MODIFIED:20201220T120425Z
DTSTAMP:20201220T120425Z
UID:3730a358-c9a3-4774-97dd-b8a49f003ee6
SUMMARY:Labour Day (WA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220307
DTEND;VALUE=DATE:20220308
TRANSP:TRANSPARENT
DESCRIPTION:Always on a Monday\, creating a long weekend. It celebrates th
 e eight-hour working day\, a victory for workers in the mid-late 19th cent
 ury. WA only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T120520Z
LAST-MODIFIED:20201220T120559Z
DTSTAMP:20201220T120559Z
UID:72daa69a-0fa3-41e5-84e6-0ccabe769c4b
SUMMARY:Labour Day (VIC)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220314
DTEND;VALUE=DATE:20220315
TRANSP:TRANSPARENT
DESCRIPTION:Always on a Monday\, creating a long weekend. It celebrates th
 e eight-hour working day\, a victory for workers in the mid-late 19th cent
 ury. VIC only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T120609Z
LAST-MODIFIED:20201220T120732Z
DTSTAMP:20201220T120732Z
UID:11690774-4a5e-479c-becc-9dd386b3d318
SUMMARY:Eight Hours Day (TAS)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220314
DTEND;VALUE=DATE:20220315
TRANSP:TRANSPARENT
DESCRIPTION:Always on a Monday\, creating a long weekend. It celebrates th
 e eight-hour working day\, a victory for workers in the mid-late 19th cent
 ury. TAS only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T101800Z
LAST-MODIFIED:20201220T120907Z
DTSTAMP:20201220T120907Z
UID:41baa6e8-fb99-4517-a9b8-f472ae9a75b2
SUMMARY:Canberra Day (ACT)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220314
DTEND;VALUE=DATE:20220315
TRANSP:TRANSPARENT
DESCRIPTION:Held on the second Monday of March each year in Canberra to ce
 lebrate the naming ceremony of Australia's capital which took place on 12 
 March 1913. ACT only\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T101654Z
LAST-MODIFIED:20201220T120953Z
DTSTAMP:20201220T120953Z
UID:17e03e89-fb07-4a74-89d5-a3c22e5da36f
SUMMARY:Adelaide Cup Day (SA)
STATUS:CONFIRMED
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220314
DTEND;VALUE=DATE:20220315
DESCRIPTION:The Holidays Act 1910 provides for the third Monday in May to 
 be a public holiday. ch through the issuing of a special Proclamation by t
 he Governor. SA only\n
X-LIC-ERROR:No value for LOCATION property. Removing entire property:
SEQUENCE:0
TRANSP:TRANSPARENT
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T062224Z
LAST-MODIFIED:20201220T121127Z
DTSTAMP:20201220T121127Z
UID:4fb5a4ba-819b-407e-89a5-4b3c7c6cb137
SUMMARY:Good Friday
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220415
DTEND;VALUE=DATE:20220416
TRANSP:TRANSPARENT
DESCRIPTION:Easter is celebrated with Good Friday and Easter Monday creati
 ng a 4 day long weekend.\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T082303Z
LAST-MODIFIED:20201220T121207Z
DTSTAMP:20201220T121207Z
UID:908d41df-d880-4c3c-a96c-90be00ca1dc9
SUMMARY:Easter Saturday (ACT\, NT\, NSW\, QLD\, SA\, and VIC)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220416
DTEND;VALUE=DATE:20220417
TRANSP:TRANSPARENT
DESCRIPTION:Public Holiday in ACT\, NT\, NSW\, QLD\, SA\, and VIC
X-MOZ-GENERATION:5
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T082344Z
LAST-MODIFIED:20201220T121451Z
DTSTAMP:20201220T121451Z
UID:97bc0f80-b6d8-4efd-8970-c96463bf37ce
SUMMARY:Easter Sunday (ACT\, NSW\, QLD\, VIC)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220417
DTEND;VALUE=DATE:20220418
TRANSP:TRANSPARENT
DESCRIPTION:Public Holiday in ACT\, NSW\, QLD\, and VIC
X-MOZ-GENERATION:3
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T082421Z
LAST-MODIFIED:20201220T121611Z
DTSTAMP:20201220T121611Z
UID:3a591eb5-1881-4c3c-92af-ee79e5e87c6f
SUMMARY:Easter Monday
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220418
DTEND;VALUE=DATE:20220419
TRANSP:TRANSPARENT
DESCRIPTION:Public Holiday as part of Easter.\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T095751Z
LAST-MODIFIED:20201220T121732Z
DTSTAMP:20201220T121732Z
UID:3f55c632-9e3b-4c0a-84d9-612bf2437d19
SUMMARY:Easter Tuesday (TAS)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220419
DTEND;VALUE=DATE:20220420
TRANSP:TRANSPARENT
DESCRIPTION:Public Holiday currently observed by certain awards/agreements
  and the State Public Service. TAS only\n
X-MOZ-GENERATION:3
SEQUENCE:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T105953Z
LAST-MODIFIED:20201220T121841Z
DTSTAMP:20201220T121841Z
UID:a4ccd577-d094-4c6a-b8c3-f5473191dbef
SUMMARY:Labour Day (QLD)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220502
DTEND;VALUE=DATE:20220503
TRANSP:TRANSPARENT
DESCRIPTION:Always on a Monday\, creating a long weekend. It celebrates th
 e eight-hour working day\, a victory for workers in the mid-late 19th cent
 ury. QLD only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T110128Z
LAST-MODIFIED:20201220T121917Z
DTSTAMP:20201220T121917Z
UID:f0b4f7a7-c7b7-485e-b440-1086f995f7c5
SUMMARY:May Day (NT)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220502
DTEND;VALUE=DATE:20220503
TRANSP:TRANSPARENT
DESCRIPTION:Always on a Monday\, creating a long weekend. It celebrates th
 e eight-hour working day\, a victory for workers in the mid-late 19th cent
 ury. NT only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T110417Z
LAST-MODIFIED:20201220T122035Z
DTSTAMP:20201220T122035Z
UID:75893c39-c0fd-4195-a39e-fe0ac2e5819f
SUMMARY:Reconciliation Day (ACT)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220530
DTEND;VALUE=DATE:20220531
TRANSP:TRANSPARENT
DESCRIPTION:Takes place on the first Monday on or after 27 May each year t
 he anniversary of the 1967 referendum. ACT only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T110607Z
LAST-MODIFIED:20201220T122117Z
DTSTAMP:20201220T122117Z
UID:7aaa8d70-2d08-4f8d-a6f6-36dab99dc2c6
SUMMARY:Western Australia Day (WA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220606
DTEND;VALUE=DATE:20220607
TRANSP:TRANSPARENT
DESCRIPTION:Held on the first Monday in June each year and is a state holi
 day only. WA only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T083338Z
LAST-MODIFIED:20201220T122312Z
DTSTAMP:20201220T122312Z
UID:74201e44-8b44-4220-bcfb-0ef43834a5ec
SUMMARY:Queen's Birthday (ACT\, NT\, NSW\, TAS\, VIC)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220613
DTEND;VALUE=DATE:20220614
TRANSP:TRANSPARENT
DESCRIPTION:Celebrated on second Monday in June except in Western Australi
 a and Queensland.\n
X-MOZ-GENERATION:2
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T083338Z
LAST-MODIFIED:20201220T122640Z
DTSTAMP:20201220T122640Z
UID:a61fabbf-e1b8-472c-b8cb-b3c15c5baaa8
SUMMARY:Queen's Birthday/Volunteer's Day (SA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20220613
DTEND;VALUE=DATE:20220614
TRANSP:TRANSPARENT
DESCRIPTION:Celebrated on second Monday in June. SA only\n
X-MOZ-GENERATION:3
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T111138Z
LAST-MODIFIED:20201220T122742Z
DTSTAMP:20201220T122742Z
UID:2c4f2a67-7b78-4f5a-b969-303667e33917
SUMMARY:Picnic Day (NT)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:********
DTEND;VALUE=DATE:********
TRANSP:TRANSPARENT
DESCRIPTION:Observed on the first Monday of August each year and is celebr
 ated with a horse race\, railway picnic and other social outings. NT only\
 n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T111329Z
LAST-MODIFIED:20201220T122839Z
DTSTAMP:20201220T122839Z
UID:********-3e2e-43bb-8f21-6b6a3ec9708e
SUMMARY:Bank Holiday (NSW banks and financial institutions)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:********
DTEND;VALUE=DATE:********
TRANSP:TRANSPARENT
DESCRIPTION:Applies to banks and certain financial institutions\, per the 
 Retail Trading Act 2008. NSW only
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T084007Z
LAST-MODIFIED:20201220T123126Z
DTSTAMP:20201220T123126Z
UID:466b68cf-eef5-409b-a469-40af2e370d94
SUMMARY:Labour Day (ACT\, NSW\, SA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:********
DTEND;VALUE=DATE:********
TRANSP:TRANSPARENT
DESCRIPTION:Always on a Monday creating a long weekend. It celebrates the 
 eight-hour working day a victory for workers in the mid-late 19th century.
  ACT\, NSW and SA\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T112137Z
LAST-MODIFIED:20201220T123211Z
DTSTAMP:20201220T123211Z
UID:916d2aed-531b-4ad8-bf5e-f7c383c4f2fb
SUMMARY:Queen's Birthday (QLD)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:********
DTEND;VALUE=DATE:********
TRANSP:TRANSPARENT
DESCRIPTION:Celebrated on second Monday in June except in Western Australi
 a and Queensland. QLD only\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T084007Z
LAST-MODIFIED:20201220T123600Z
DTSTAMP:20201220T123600Z
UID:e9f9e360-c235-4aca-ab3d-a72a5304a429
SUMMARY:Labour Day (ACT\, NSW\, SA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20211004
DTEND;VALUE=DATE:20211005
TRANSP:TRANSPARENT
DESCRIPTION:Always on a Monday creating a long weekend. It celebrates the 
 eight-hour working day a victory for workers in the mid-late 19th century.
  ACT\, NSW and SA\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T112411Z
LAST-MODIFIED:20201220T123731Z
DTSTAMP:20201220T123731Z
UID:7aa9fd3e-f7e0-4188-818c-5cae1b11d47d
SUMMARY:Melbourne Cup (VIC)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20221101
DTEND;VALUE=DATE:20221102
TRANSP:TRANSPARENT
DESCRIPTION:All of Victoria unless alternate local holiday has been arrang
 ed by non-metro council. VIC only
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T112844Z
LAST-MODIFIED:20201220T123827Z
DTSTAMP:20201220T123827Z
UID:431ab1c6-10db-4c86-b10d-9d9b3f611fcc
SUMMARY:Christmas Eve 6pm - Midnight (QLD)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20221224
DTEND;VALUE=DATE:20221225
TRANSP:TRANSPARENT
DESCRIPTION:Part day public holiday from 6pm to midnight. QLD only\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T112605Z
LAST-MODIFIED:20201220T123852Z
DTSTAMP:20201220T123852Z
UID:7b99468c-b2fb-4cfb-8943-787ca478f902
SUMMARY:Christmas Eve 7pm - Midnight (NT\, SA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20221224
DTEND;VALUE=DATE:20221225
TRANSP:TRANSPARENT
DESCRIPTION:Part-day public holiday from 7pm-12 midnight. NT and SA only
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T085207Z
LAST-MODIFIED:20201220T124443Z
DTSTAMP:20201220T124443Z
UID:e3264203-6248-4363-a866-56135e625931
SUMMARY:Christmas Day (additional day) (ACT\, NSW\, QLD\, SA\, TAS\, VIC\,
  WA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20221227
DTEND;VALUE=DATE:20221228
TRANSP:TRANSPARENT
DESCRIPTION:As 25 December (Christmas Day) falls on a Sunday in 2022\, the
 re is an additional public holiday on the Monday. ACT\, NSW\, QLD\, SA\, T
 AS\, VIC\, and WA\n
X-MOZ-GENERATION:3
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T114416Z
LAST-MODIFIED:20201220T124541Z
DTSTAMP:20201220T124541Z
UID:843e646b-31eb-40fb-aeb5-7ea65171ed7a
SUMMARY:New Year's Eve 7pm - Midnight (NT\, SA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20221231
DTEND;VALUE=DATE:20230101
TRANSP:TRANSPARENT
DESCRIPTION:Part-day public holiday from 7pm-12 midnight. NT and SA only\n
 
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T114416Z
LAST-MODIFIED:20201220T124758Z
DTSTAMP:20201220T124758Z
UID:731c0749-0b1e-4e43-be8e-ed76374ee628
SUMMARY:New Year's Eve 7pm - Midnight (NT\, SA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20201231
DTEND;VALUE=DATE:20210101
TRANSP:TRANSPARENT
DESCRIPTION:Part-day public holiday from 7pm-12 midnight. NT and SA only\n
 
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T114055Z
LAST-MODIFIED:20201220T125337Z
DTSTAMP:20201220T125337Z
UID:ca5c2957-a926-4d43-a89c-e7fd8d87e006
SUMMARY:Boxing Day / Proclamation Day (additional day) (SA)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20201228
DTEND;VALUE=DATE:20201229
TRANSP:TRANSPARENT
DESCRIPTION:As 26 December (Proclamation/Boxing Day) falls on a Saturday i
 n 2020\, there is an additional public holiday on Monday 28 December. SA o
 nly\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T085329Z
LAST-MODIFIED:20201220T125354Z
DTSTAMP:20201220T125354Z
UID:213fbd43-60b2-43a2-815b-f3272b774f46
SUMMARY:Boxing Day (additional day)
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20201228
DTEND;VALUE=DATE:20201229
TRANSP:TRANSPARENT
DESCRIPTION:As 26 December (Boxing Day) falls on a Saturday in 2020\, ther
 e is an additional public holiday on Monday 28 December.\n
X-MOZ-GENERATION:2
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T061352Z
LAST-MODIFIED:20201220T123457Z
DTSTAMP:20201220T123457Z
UID:2333e5a4-7c8b-4933-b3ec-d00d86eef52b
SUMMARY:Australia Day
RRULE:FREQ=YEARLY
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210126
DTEND;VALUE=DATE:20210127
TRANSP:TRANSPARENT
DESCRIPTION:Always celebrated on 26 January\n
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T060916Z
LAST-MODIFIED:20201220T123457Z
DTSTAMP:20201220T123457Z
UID:dc5e231d-9503-4785-bbf3-0c6763156fa0
SUMMARY:Christmas Day
RRULE:FREQ=YEARLY
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20201225
DTEND;VALUE=DATE:20201226
TRANSP:TRANSPARENT
DESCRIPTION:Christmas Day
X-MOZ-GENERATION:1
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T082737Z
LAST-MODIFIED:20201220T123457Z
DTSTAMP:20201220T123457Z
UID:fb68a47d-8381-4af9-9d21-a2882ac240c7
SUMMARY:Anzac Day
RRULE:FREQ=YEARLY
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210425
DTEND;VALUE=DATE:20210426
TRANSP:TRANSPARENT
DESCRIPTION:Celebrated on the 25 April each year.\n
X-MOZ-GENERATION:2
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T102305Z
LAST-MODIFIED:20201220T104418Z
DTSTAMP:20201220T104418Z
UID:fb68a47d-8381-4af9-9d21-a2882ac240c7
SUMMARY:Anzac Day (NSW\, SA\, TAS\, VIC\, WA)
RECURRENCE-ID;VALUE=DATE:20210425
DTSTART;VALUE=DATE:20210425
DTEND;VALUE=DATE:20210426
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T113201Z
LAST-MODIFIED:20201220T123457Z
DTSTAMP:20201220T123457Z
UID:a68fdf67-90de-4d03-b57a-8e135c13f298
SUMMARY:Boxing Day / Proclamation Day (SA)
RRULE:FREQ=YEARLY
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20211226
DTEND;VALUE=DATE:20211227
TRANSP:TRANSPARENT
DESCRIPTION:Proclamation/Boxing Day occurs the day after Christmas. SA onl
 y\n
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T061205Z
LAST-MODIFIED:20201220T123457Z
DTSTAMP:20201220T123457Z
UID:9b075a2f-e4f8-4b15-b4e1-3de40e1c31a2
SUMMARY:New Year's Day
RRULE:FREQ=YEARLY
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20210101
DTEND;VALUE=DATE:20210102
TRANSP:TRANSPARENT
DESCRIPTION:New Year's Day is the first day of the calendar year and is ce
 lebrated each January 1st\n
X-MOZ-GENERATION:2
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T115630Z
LAST-MODIFIED:20201220T115940Z
DTSTAMP:20201220T115940Z
UID:9b075a2f-e4f8-4b15-b4e1-3de40e1c31a2
SUMMARY:New Year's Day (ACT\, NSW\, QLD\, VIC\, WA)
RECURRENCE-ID;VALUE=DATE:20220101
DTSTART;VALUE=DATE:20220101
DTEND;VALUE=DATE:20220102
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T061106Z
LAST-MODIFIED:20201220T125641Z
DTSTAMP:20201220T125641Z
UID:47cfe029-c792-46ba-a1eb-cb894710f5da
SUMMARY:Boxing Day
RRULE:FREQ=YEARLY
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20201226
DTEND;VALUE=DATE:20201227
TRANSP:TRANSPARENT
DESCRIPTION:Boxing Day
X-MOZ-GENERATION:2
END:VEVENT
BEGIN:VEVENT
CREATED:20201220T125006Z
LAST-MODIFIED:20201220T125641Z
DTSTAMP:20201220T125641Z
UID:47cfe029-c792-46ba-a1eb-cb894710f5da
SUMMARY:Boxing Day (ACT\, NSW\, QLD\, VIC\, WA)
RECURRENCE-ID;VALUE=DATE:20201226
CATEGORIES:Public Holiday
DTSTART;VALUE=DATE:20201226
DTEND;VALUE=DATE:20201227
DESCRIPTION:Boxing Day. Public Holiday in ACT\, NSW\, QLD\, VIC\, and WA
TRANSP:TRANSPARENT
END:VEVENT
END:VCALENDAR
