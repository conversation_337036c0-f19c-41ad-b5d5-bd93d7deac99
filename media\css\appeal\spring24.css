/*
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 */

/* Open Sans */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/opensans-regular.woff2") format("woff2"),
  url("/media/fonts/opensans-regular.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("/media/fonts/opensans-italic.woff2") format("woff2"),
  url("/media/fonts/opensans-italic.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/opensans-light.woff2") format("woff2"),
  url("/media/fonts/opensans-light.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url("/media/fonts/opensans-lightitalic.woff2") format("woff2"),
  url("/media/fonts/opensans-lightitalic.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/opensans-semibold.woff2") format("woff2"),
  url("/media/fonts/opensans-semibold.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url("/media/fonts/opensans-semibolditalic.woff2") format("woff2"),
  url("/media/fonts/opensans-semibolditalic.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/opensans-bold.woff2") format("woff2"),
  url("/media/fonts/opensans-bold.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url("/media/fonts/opensans-bolditalic.woff2") format("woff2"),
  url("/media/fonts/opensans-bolditalic.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/opensans-extrabold.woff2") format("woff2"),
  url("/media/fonts/opensans-extrabold.woff") format("woff");
}

@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 800;
  font-display: swap;
  src: url("/media/fonts/opensans-extrabolditalic.woff2") format("woff2"),
  url("/media/fonts/opensans-extrabolditalic.woff") format("woff");
}

/* Nicer fallbacks */
@font-face {
  font-family: 'system-ui';
  font-style: normal;
  font-weight: 300;
  src: local(".SFNS-Light"), local(".SFNSText-Light"), local("Segoe UI Light"), local("Ubuntu Light");
}

@font-face {
  font-family: 'system-ui';
  font-style: italic;
  font-weight: 300;
  src: local(".SFNS-LightItalic"), local(".SFNSText-LightItalic"), local("Segoe UI Light Italic"), local("Ubuntu Light Italic");
}

@font-face {
  font-family: 'system-ui';
  font-style: normal;
  font-weight: 400;
  src: local(".SFNS-Regular"), local(".SFNSText-Regular"), local("Segoe UI"), local("Ubuntu");
}

@font-face {
  font-family: 'system-ui';
  font-style: italic;
  font-weight: 400;
  src: local(".SFNS-Italic"), local(".SFNSText-Italic"), local("Segoe UI Italic"), local("Ubuntu Italic");
}

@font-face {
  font-family: 'system-ui';
  font-style: normal;
  font-weight: 500;
  src: local(".SFNS-Medium"), local(".SFNSText-Medium"), local("Segoe UI Semibold"), local("Ubuntu Medium");
}

@font-face {
  font-family: 'system-ui';
  font-style: italic;
  font-weight: 500;
  src: local(".SFNS-MediumItalic"), local(".SFNSText-MediumItalic"), local("Segoe UI Semibold Italic"), local("Ubuntu Medium Italic");
}

@font-face {
  font-family: 'system-ui';
  font-style: normal;
  font-weight: 700;
  src: local(".SFNS-Bold"), local(".SFNSText-Bold"), local("Segoe UI Bold"), local("Ubuntu Bold");
}

@font-face {
  font-family: 'system-ui';
  font-style: italic;
  font-weight: 700;
  src: local(".SFNS-BoldItalic"), local(".SFNSText-BoldItalic"), local("Segoe UI Bold Italic"), local("Ubuntu Bold Italic");
}

:root {
  --color-red-10: #FEE2E2;
  --color-red-30: #FCA5A5;
  --color-red-40: #f87171;
  --color-red-50: #ef4444;
  --color-red-60: #dc2626;
  --color-red-70: #b91c1c;
  --color-red-80: #991b1b;

  --color-blue-30: #88ccfc;
  --color-blue-40: #4cb1f9;
  --color-blue-50: #2493ef;
  --color-blue-60: #1373d9;
  --color-blue-90: #15427c;

  --color-teal-50: #0db7bd;

  --color-magenta-50: #e247c4;

  --color-gray-10: #f4f4f5;
  --color-gray-80: #27272a;

  --color-ink-10: #f1f3fa;
  --color-ink-70: #3e3c67;

  --color-white: #ffffff;

  --color-appeal-txt: #175491;
  --color-appeal-bg: #82C3F5;
  --color-appeal-main-bg: var(--color-white);
  --color-footer-txt: #105BBC;
  --color-legal-txt: var(--color-ink-70);

  --bg-appeal-glow: linear-gradient(129.83deg, #00FFFF 0%, #DB00FF 100%) !important;
  --bg2: var(--color-appeal-main-bg);
  --bg: var(--color-appeal-bg);
  --txt: var(--color-appeal-txt);
  --shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  --accent-text: var(--color-appeal-txt);
  --closing-text: var(--color-footer-txt);
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-appeal-bg: #334B75;
    --color-appeal-txt: #F2F7FC;
    --color-appeal-main-bg: #2C343E;
    --color-footer-txt: #89BCFF;
    --color-legal-txt: var(--color-white);

    --shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}

.clouds-bg {
  --clouds-bg: url('/media/svg/appeal/spring24/clouds.svg');
  --clouds-size: 856px 820px;
  --clouds-position: right -2px top -50px;
  position: absolute;
  top: 0;
  display: flex;
  width: 100%;
  height: 1300px;
  justify-content: center;
  z-index: -1;
}

@media (prefers-color-scheme: dark) {
  .clouds-bg {
    --clouds-bg: url('/media/svg/appeal/spring24/clouds-dark.svg');
    /* Helps with contrast on hero copy */
    opacity: 0.9;
  }
}

.clouds-bg .left,
.clouds-bg .right {
  background: var(--clouds-bg);
  background-repeat: no-repeat;
  background-position: var(--clouds-position);
  background-size: var(--clouds-size);
  width: 100%;
}

.clouds-bg .right {
  transform: scaleX(-100%);
}

html {
  font-family: Open Sans, system-ui, sans-serif;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1rem;
}

body {
  background-color: var(--bg);
  color: var(--txt);
  margin: 3rem 0 0;
  padding: 0;
  min-height: 100vh;
  max-width: 100%;
  line-height: 1.6;
  letter-spacing: 0.025em;
  font-size: 13px;
}

/* Minor fixes with new design START */
h1, h2, h3, h4, h5, h6 {
  position: relative;
}

.self-end {
  align-self: flex-end;
}

.text-right {
  text-align: right;
}
/* Minor fixes with new design END */

.closing-text {
  text-align: center;
  color: var(--color-footer-txt);
}

section {
  background-color: var(--bg2);
}

#initAppeal {
  position: relative;
  left: -50px;

  display: flex;
  gap: 9px;
  margin: 0 auto 100px;
  padding: 0 24px 24px;

  max-width: 900px;
  align-items: center;
  font-size: 1.25rem;
}

#initAppeal h1 {
  font-size: 5rem;
  line-height: 1;
}

#initAppeal p:nth-of-type(2) > strong {
  /* Apply a new line for the bold text. This seems to play nice on the locales I've tested. */
  display: block;
}

#appeal-body {
  font-size: 1.31rem;
  font-weight: 400;
}

#appeal-body strong {
  font-weight: 600;
}

main {
  padding: 1em;
  margin: 0 auto;
  max-width: 800px;
  position: relative;
}

.accent-text {
  color: var(--accent-text);
}

#appeal-heading {
  width: 100%;
  text-align: left;
  display: flex;
  flex-direction: column;
  font-size: 5.35rem !important;
  font-weight: 700;
  margin: 0;
}

#appeal-heading span {
  width: 100%;
  font-size: 3.5rem;
  font-weight: 600;
  line-height: 115%;
  margin: 0;
}

#illustration {
  width: 60%;
  position: relative;
}

#illustration > div {
  position: absolute;
}

#illustration > #ill-body {
  left: -128px;
  top: -245px;
}

#illustration > #ill-hearts {
  left: 32px;
  top: -245px;
}

.donate-banner {
  --accent-text: var(--color-red-80);
  background: var(--color-red-50);
  background-image: linear-gradient(to right, var(--color-red-10), var(--color-gray-10));
  background-position: top left, center;
  background-size: contain;
  background-repeat: no-repeat;
  border-radius: 3px;
  box-shadow: 4px 4px 24px rgba(0, 0, 0, 0.15);
  color: white;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  font-size: 1.1rem;
  line-height: 1.1;
  margin: -114px auto 0;
  text-decoration: none;
  text-shadow: var(--shadow);
  transition: transform 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
  max-width: 430px;
  min-height: 120px;
  z-index: 10;
}

.donate-banner::after {
  content: '';
  position: absolute;
  top: -210px;
  left: -220px; /* -20px for l10n overlap */
  height: 420px;
  width: 420px;
  background-color: var(--color-red-60);
  background-image: linear-gradient(var(--color-red-50), var(--color-red-70));
  transform: rotate(36deg);
  transition: transform 0.3s ease-in-out;
}

.donate-banner:hover {
  transform: scale(1.05);
}

.donate-banner:hover::after {
  transform: scale(3)
}

.donate-banner:hover #decoration,
.donate-banner:hover #donate-banner-right {
  opacity: 0;
}

.donate-banner:hover #donate-banner-left {
  transform: translate(24px, 6px);
}

#donate-banner-left,
#donate-banner-right {
  flex: 1;
  padding: 15px;
  transition: all .5s ease-in-out;
  z-index: 2;
}

#donate-banner-right {
  padding-inline-start: 3px;
  text-shadow: 0 0 4px var(--color-gray-10), 0 1px 1px var(--color-gray-10);
}

#donate-banner-left > strong {
  font-size: 1.7rem;
}

#donate-banner-right > strong {
  font-size: 1.3rem;
  font-weight: 400;
}

#decoration > #pill-1 {
  animation: 20s infinite pill-float linear;
  animation-delay: 0.2s;
}

#decoration > #pill-2 {
  animation: 30s infinite pill-float linear;
}

#decoration > #pill-3 {
  animation: 35s infinite pill-float linear;
  animation-delay: 0.1s;
}

#decoration > #pill-4 {
  animation: 25s infinite pill-float linear;
  animation-delay: 0.3s;
}

#decoration > #pill-5 {
  animation: 40s infinite pill-float linear;
  animation-delay: 0.4s;
}

#decoration > #pill-6 {
  animation: 30s infinite pill-float linear;
  animation-delay: 0.5s;
}

#decoration > #pill-7 {
  animation: 25s infinite pill-float linear;
  animation-delay: 0.6s;
}

#decoration > #pill-8 {
  animation: 20s infinite pill-float linear;
  animation-delay: 0.7s;
}

#decoration > #pill-9 {
  animation: 40s infinite pill-float linear;
  animation-delay: 0.8s;
}

#decoration > #pill-10 {
  animation: 35s infinite pill-float linear;
  animation-delay: 0.4s;
}

#decoration {
  position: absolute;
  left: -59px;
  display: grid;
  gap: 3px;
  grid-template-columns: repeat(20, 1fr);
  grid-template-rows: repeat(10, 1fr);
  place-items: stretch;
  height: 90px;
  width: 500px;
  transform: rotate(-55deg);
  transition: opacity .5s ease-in-out;
  z-index: 1;
}

.pill {
  display: block;
  background: var(--color-red-40);
  opacity: 0.75;
  border-radius: 1000px;
  transform: translateX(-250px);
}

@keyframes pill-float {
  from {
    transform: translateX(-250px);
  }

  to {
    transform: translateX(250px);
  }
}

#pill-1 {
  grid-column: 4 / span 8;
  grid-row: 5 / span 3;

}

#pill-2 {
  grid-column: 10 / span 4;
  grid-row: 6 / span 1;
}

#pill-3 {
  grid-column: 8 / span 3;
  grid-row: 3 / span 1;
}

#pill-4 {
  grid-column: 4 / span 3;
  grid-row: 3 / span 1;
}

#pill-5 {
  grid-column: 6 / span 4;
  grid-row: 9 / span 1;
}

#pill-6 {
  grid-column: 11 / span 1;
  grid-row: 9 / span 1;
}

#pill-7 {
  grid-column: 13 / span 2;
  grid-row: 10 / span 1;
}

#pill-8 {
  grid-column: 13 / span 3;
  grid-row: 8 / span 1;
}

#pill-9 {
  grid-column: 14 / span 3;
  grid-row: 4 / span 1;
}

#pill-10 {
  grid-column: 12 / span 1;
  grid-row: 4 / span 1;
}

#hover-hearts {
  position: absolute;
  inset-block-end: 12px;
  inset-inline-end: 13px;
  display: none;
  gap: 3px;
  z-index: 1;
}

#hover-heart-1,
#hover-heart-2,
#hover-heart-3 {
  display: flex;
  align-items: end;
  opacity: 0;
}

#hover-heart-1 > svg {
  animation-delay: 0.6s;
}

#hover-heart-3 {
  animation-delay: 0.8s;
}

#hover-heart-1 > svg {
  transform: rotate(-45deg);
  width: 48px;
  height: 48px;
}

#hover-heart-2 > svg {
  width: 64px;
  height: 64px;
  transform: translateY(-9px);
}

#hover-heart-3 > svg {
  transform: rotate(45deg);
  width: 48px;
  height: 48px;
}

.donate-banner:hover #hover-hearts {
  display: flex;
}

.donate-banner:hover #hover-hearts > #hover-heart-1 {
  animation: 1s heart-fade forwards ease-in-out;
  animation-delay: 0.1s;
}

.donate-banner:hover #hover-hearts > #hover-heart-2 {
  animation: 1s heart-fade forwards ease-in-out;
}

.donate-banner:hover #hover-hearts > #hover-heart-3 {
  animation: 1s heart-fade forwards ease-in-out;
  animation-delay: 0.2s;
}

@keyframes heart-fade {
  from {
    opacity: 0;
    transform: translateY(12px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.letter-container {
  padding: 60px 60px 30px;
  background: var(--bg2);
  border-radius: 6px;
  box-shadow: var(--shadow);
  position: relative;
  margin-bottom: 121px;
}

.letter-container:before {
  content: '';
  position: absolute;
  background: var(--bg-appeal-glow);
  inset: -4px;
  border-radius: 12px;
  filter: blur(64px);
  opacity: 0.8;
  z-index: -1;
}

.letter-container p:nth-child(2) {
  margin-top: 30px;
}

.letter-container p:nth-child(4) {
  margin-bottom: 90px;
}

.heart-container {
  color: var(--color-red-50);
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 32px;
  align-items: start;
  margin-top: 2em;
}

.heart-svg {
  position: relative;
  top: -4px;
}

@media (prefers-color-scheme: dark) {
  .heart-container {
    color: var(--color-red-30);
  }
}

.line {
  display: block;
  height: 1px;
  width: 100%;
  border-top: 1px solid var(--color-blue-60);
}

.left-lines,
.right-lines {
  display: flex;
  flex-direction: column;
  gap: 6px;
  justify-self: stretch;
}

.left-lines {
  align-items: end;
}

.left-lines > .line:nth-child(2) {
  width: 50%;
  margin-right: 3px;
  border-top: 1px solid var(--color-blue-50);
}

.left-lines > .line:nth-child(1) {
  width: 30%;
  border-top: 1px solid var(--color-blue-40);
}

.right-lines > .line:nth-child(2) {
  width: 50%;
  margin-left: 3px;
  border-top: 1px solid var(--color-blue-50);
}

.right-lines > .line:nth-child(1) {
  width: 30%;
  border-top: 1px solid var(--color-blue-40);
}

#footer {
  font-size: 18px;
  line-height: 1.5;
  font-weight: 400;
  width: 100%;
}

/* Here until the new design is merged */
#footer.container {
  position: relative;
  display: grid;
  gap: 60px;
  padding-inline: 1rem;
  margin-inline: auto;
  overflow: hidden;
  box-sizing: border-box;
}

#footer.container.footer {
  place-items: center;
  padding-inline: 1rem;
  padding-block: 1.25rem;
}

#footer a {
  color: var(--color-legal-txt);
  text-decoration-color: var(--color-legal-txt);
  text-underline-offset: 0.25em;
  text-decoration-thickness: .12em;
  text-decoration-style: solid;
  transition: font-size .2s,
              text-decoration-color .2s;
}

#footer a.donate,
#footer a:hover,
#footer a:hover:visited {
  text-decoration-color: var(--accent);
}

#footer a:visited {
  text-decoration-color: var(--color-purple-50);
}

#footer .mzla {
  display: grid;
  place-items: center;
  gap: 15px;
  text-align: center;
  max-inline-size: 75ch;
}

#footer .mozilla-logo {
  --accent: white;
  max-width: 200px;
}

#footer .site-links,
#footer .legal-links {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
  justify-content: center;
}

#footer .legal-links {
  font-size: 1rem;
}

/*
 * Media Queries
 */

@media (max-width: 1000px) {
  #initAppeal {
    padding-top: 4rem;
    position: static;
    flex-direction: column-reverse;
    justify-items: center;
  }

  #illustration {
    display: flex;
    flex-direction: column-reverse;
    text-align: center;

    position: relative;
    width: 100%;
    height: 120px;
  }

  #illustration > div {
    position: relative;
    width: 100%;
  }

  #illustration > #ill-hearts > svg {
    max-width: 150px;
  }

  #illustration > #ill-body > svg {
    max-width: 300px;
  }

  #illustration > #ill-hearts {
    left: 0;
    top: 450px;
  }

  #illustration > #ill-body {
    left: 0;
    top: 175px;
  }
}


@media (max-width: 600px) {

  main {
    padding: 0;
  }

  #appeal-heading {
    font-size: 3.7rem !important;
  }

  #appeal-heading span {
    font-size: 2rem;
  }

  .letter-container {
    padding: 1rem;
  }

  .letter-container:before {
    inset: 0;
  }
}

@media (max-width: 512px) {
  #footer.container {
    padding-inline: 0.25rem;
    padding-block: 32px;
  }

  #initAppeal {
    padding: 4rem 1rem 1rem;
  }

  #appeal-heading {
    font-size: 3rem !important;
  }
}
