/* Bug 958321 */

@font-face {
  font-family: X-LocaleSpecific;
  font-weight: normal;
  src: url('/media/fonts/l10n/DroidNaskh-Regular.woff') format('woff');
}

@font-face {
  font-family: X-LocaleSpecific;
  font-weight: bold;
  src: url('/media/fonts/l10n/DroidNaskh-Bold.woff') format('woff');
}

/* Bug 944269 */

/* !important required for locale specific override */
/* stylelint-disable declaration-no-important  */
* {
  letter-spacing: normal !important;
}
/* Fixes line height for all the headers */
h1,
h2,
h3,
h4,
h5,
h6,
.huge,
.large,
legend {
  line-height: 1.5 !important;
}
/* stylelint-enable */
