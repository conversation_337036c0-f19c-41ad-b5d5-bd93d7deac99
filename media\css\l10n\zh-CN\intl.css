/* Bug 993709 */

@font-face {
    font-family: X-LocaleSpecific;
    font-weight: normal;
    src: /* OS X */
       local(FZLTXHK--GBK1-0), /* = Lantinghei SC Extralight */
       local(HiraginoSansGB-W3),
       /* Windows */
       local('Microsoft YaHei Light'),
       /* Linux */
       local(DroidSansFallbackFull);
}

@font-face {
    font-family: X-LocaleSpecific;
    font-weight: bold;
    src: /* OS X */
       local(FZLTZHK--GBK1-0), /* = Lantinghei SC Demibold */
       local(HiraginoSansGB-W6),
       /* Windows */
       local('Microsoft YaHei Bold'),
       /* Linux */
       local(DroidSansFallbackFull);
}

@font-face {
    font-family: X-LocaleSpecific-Extrabold;
    font-weight: 800;
    src: /* OS X */
       local(FZLTTHK--GBK1-0), /* = Lantinghei SC Heavy */
       local(HiraginoSansGB-W6),
       /* Windows */
       local('Microsoft YaHei Bold'),
       /* Linux */
       local(DroidSansFallbackFull);
}

/* Bug 973171 */

* {
    /* !important required for locale specific override */
    font-style: normal !important; /* stylelint-disable-line declaration-no-important */
}
