/*!!
 * Matomo - free/libre analytics platform
 *
 * JavaScript tracking client
 *
 * @link https://piwik.org
 * @source https://github.com/matomo-org/matomo/blob/master/js/piwik.js
 * @license https://piwik.org/free-software/bsd/ BSD-3 Clause (also in js/LICENSE.txt)
 * @license magnet:?xt=urn:btih:c80d50af7d3db9be66a4d0a86db0286e4fd33292&dn=bsd-3-clause.txt BSD-3-Clause
 */
;if(typeof _paq!=="object"){_paq=[]}if(typeof window.Matomo!=="object"){window.Matomo=window.Piwik=(function(){var s,b={},A={},K=document,g=navigator,ac=screen,X=window,h=X.performance||X.mozPerformance||X.msPerformance||X.webkitPerformance,u=X.encodeURIComponent,W=X.decodeURIComponent,k=unescape,M=[],I,v,am=[],z=0,ag=0,Y=0,m=false,q="";function p(au){try{return W(au)}catch(av){return unescape(au)}}function N(av){var au=typeof av;return au!=="undefined"}function D(au){return typeof au==="function"}function aa(au){return typeof au==="object"}function y(au){return typeof au==="string"||au instanceof String}function al(au){return typeof au==="number"||au instanceof Number
}function ad(au){return N(au)&&(al(au)||(y(au)&&au.length))}function E(av){if(!av){return true}var au;for(au in av){if(Object.prototype.hasOwnProperty.call(av,au)){return false}}return true}function ap(au){var av=typeof console;if(av!=="undefined"&&console&&console.error){console.error(au)}}function ak(){var az,ay,aB,av,au;for(az=0;az<arguments.length;az+=1){au=null;if(arguments[az]&&arguments[az].slice){au=arguments[az].slice()}av=arguments[az];aB=av.shift();var aA,aw;var ax=y(aB)&&aB.indexOf("::")>0;if(ax){aA=aB.split("::");aw=aA[0];aB=aA[1];if("object"===typeof v[aw]&&"function"===typeof v[aw][aB]){v[aw][aB].apply(v[aw],av)}else{if(au){am.push(au)}}}else{for(ay=0;ay<M.length;ay++){if(y(aB)){aw=M[ay];var aC=aB.indexOf(".")>0;if(aC){aA=aB.split(".");if(aw&&"object"===typeof aw[aA[0]]){aw=aw[aA[0]];aB=aA[1]}else{if(au){am.push(au);break}}}if(aw[aB]){aw[aB].apply(aw,av)}else{var aD="The method '"+aB+'\' was not found in "_paq" variable.  Please have a look at the Matomo tracker documentation: https://developer.matomo.org/api-reference/tracking-javascript';
ap(aD);if(!aC){throw new TypeError(aD)}}if(aB==="addTracker"){break}if(aB==="setTrackerUrl"||aB==="setSiteId"){break}}else{aB.apply(M[ay],av)}}}}}function at(ax,aw,av,au){if(ax.addEventListener){ax.addEventListener(aw,av,au);return true}if(ax.attachEvent){return ax.attachEvent("on"+aw,av)}ax["on"+aw]=av}function n(au){if(K.readyState==="complete"){au()}else{if(X.addEventListener){X.addEventListener("load",au,false)}else{if(X.attachEvent){X.attachEvent("onload",au)}}}}function r(ax){var au=false;if(K.attachEvent){au=K.readyState==="complete"}else{au=K.readyState!=="loading"}if(au){ax();return}var aw;if(K.addEventListener){at(K,"DOMContentLoaded",function av(){K.removeEventListener("DOMContentLoaded",av,false);if(!au){au=true;ax()}})}else{if(K.attachEvent){K.attachEvent("onreadystatechange",function av(){if(K.readyState==="complete"){K.detachEvent("onreadystatechange",av);if(!au){au=true;ax()}}});if(K.documentElement.doScroll&&X===X.top){(function av(){if(!au){try{K.documentElement.doScroll("left")
}catch(ay){setTimeout(av,0);return}au=true;ax()}}())}}}at(X,"load",function(){if(!au){au=true;ax()}},false)}function ah(av,aA,aB){if(!av){return""}var au="",ax,aw,ay,az;for(ax in b){if(Object.prototype.hasOwnProperty.call(b,ax)){az=b[ax]&&"function"===typeof b[ax][av];if(az){aw=b[ax][av];ay=aw(aA||{},aB);if(ay){au+=ay}}}}return au}function an(av){var au;m=true;ah("unload");au=new Date();var aw=au.getTimeAlias();if((s-aw)>3000){s=aw+3000}if(s){do{au=new Date()}while(au.getTimeAlias()<s)}}function o(aw,av){var au=K.createElement("script");au.type="text/javascript";au.src=aw;if(au.readyState){au.onreadystatechange=function(){var ax=this.readyState;if(ax==="loaded"||ax==="complete"){au.onreadystatechange=null;av()}}}else{au.onload=av}K.getElementsByTagName("head")[0].appendChild(au)}function O(){var au="";try{au=X.top.document.referrer}catch(aw){if(X.parent){try{au=X.parent.document.referrer}catch(av){au=""}}}if(au===""){au=K.referrer}return au}function t(au){var aw=new RegExp("^([a-z]+):"),av=aw.exec(au);
return av?av[1]:null}function d(au){var aw=new RegExp("^(?:(?:https?|ftp):)/*(?:[^@]+@)?([^:/#]+)"),av=aw.exec(au);return av?av[1]:au}function H(au){return(/^[0-9][0-9]*(\.[0-9]+)?$/).test(au)}function R(aw,ax){var au={},av;for(av in aw){if(aw.hasOwnProperty(av)&&ax(aw[av])){au[av]=aw[av]}}return au}function C(aw){var au={},av;for(av in aw){if(aw.hasOwnProperty(av)){if(H(aw[av])){au[av]=Math.round(aw[av])}else{throw new Error('Parameter "'+av+'" provided value "'+aw[av]+'" is not valid. Please provide a numeric value.')}}}return au}function l(av){var aw="",au;for(au in av){if(av.hasOwnProperty(au)){aw+="&"+u(au)+"="+u(av[au])}}return aw}function ao(av,au){av=String(av);return av.lastIndexOf(au,0)===0}function V(av,au){av=String(av);return av.indexOf(au,av.length-au.length)!==-1}function B(av,au){av=String(av);return av.indexOf(au)!==-1}function f(av,au){av=String(av);return av.substr(0,av.length-au)}function J(ax,aw,az){ax=String(ax);if(!az){az=""}var au=ax.indexOf("#");var aA=ax.length;
if(au===-1){au=aA}var ay=ax.substr(0,au);var av=ax.substr(au,aA-au);if(ay.indexOf("?")===-1){ay+="?"}else{if(!V(ay,"?")){ay+="&"}}return ay+u(aw)+"="+u(az)+av}function j(av,aw){av=String(av);if(av.indexOf("?"+aw+"=")===-1&&av.indexOf("&"+aw+"=")===-1){return av}var ax=av.indexOf("?");if(ax===-1){return av}var au=av.substr(ax+1);var aB=av.substr(0,ax);if(au){var aC="";var aE=au.indexOf("#");if(aE!==-1){aC=au.substr(aE+1);au=au.substr(0,aE)}var ay;var aA=au.split("&");var az=aA.length-1;for(az;az>=0;az--){ay=aA[az].split("=")[0];if(ay===aw){aA.splice(az,1)}}var aD=aA.join("&");if(aD){aB=aB+"?"+aD}if(aC){aB+="#"+aC}}return aB}function e(aw,av){var au="[\\?&#]"+av+"=([^&#]*)";var ay=new RegExp(au);var ax=ay.exec(aw);return ax?p(ax[1]):""}function a(au){if(au&&String(au)===au){return au.replace(/^\s+|\s+$/g,"")}return au}function G(au){return unescape(u(au))}function ar(aJ){var aw=function(aP,aO){return(aP<<aO)|(aP>>>(32-aO))},aK=function(aR){var aP="",aQ,aO;for(aQ=7;aQ>=0;aQ--){aO=(aR>>>(aQ*4))&15;
aP+=aO.toString(16)}return aP},az,aM,aL,av=[],aD=1732584193,aB=4023233417,aA=2562383102,ay=271733878,ax=3285377520,aI,aH,aG,aF,aE,aN,au,aC=[];aJ=G(aJ);au=aJ.length;for(aM=0;aM<au-3;aM+=4){aL=aJ.charCodeAt(aM)<<24|aJ.charCodeAt(aM+1)<<16|aJ.charCodeAt(aM+2)<<8|aJ.charCodeAt(aM+3);aC.push(aL)}switch(au&3){case 0:aM=2147483648;break;case 1:aM=aJ.charCodeAt(au-1)<<24|8388608;break;case 2:aM=aJ.charCodeAt(au-2)<<24|aJ.charCodeAt(au-1)<<16|32768;break;case 3:aM=aJ.charCodeAt(au-3)<<24|aJ.charCodeAt(au-2)<<16|aJ.charCodeAt(au-1)<<8|128;break}aC.push(aM);while((aC.length&15)!==14){aC.push(0)}aC.push(au>>>29);aC.push((au<<3)&4294967295);for(az=0;az<aC.length;az+=16){for(aM=0;aM<16;aM++){av[aM]=aC[az+aM]}for(aM=16;aM<=79;aM++){av[aM]=aw(av[aM-3]^av[aM-8]^av[aM-14]^av[aM-16],1)}aI=aD;aH=aB;aG=aA;aF=ay;aE=ax;for(aM=0;aM<=19;aM++){aN=(aw(aI,5)+((aH&aG)|(~aH&aF))+aE+av[aM]+1518500249)&4294967295;aE=aF;aF=aG;aG=aw(aH,30);aH=aI;aI=aN}for(aM=20;aM<=39;aM++){aN=(aw(aI,5)+(aH^aG^aF)+aE+av[aM]+1859775393)&4294967295;
aE=aF;aF=aG;aG=aw(aH,30);aH=aI;aI=aN}for(aM=40;aM<=59;aM++){aN=(aw(aI,5)+((aH&aG)|(aH&aF)|(aG&aF))+aE+av[aM]+2400959708)&4294967295;aE=aF;aF=aG;aG=aw(aH,30);aH=aI;aI=aN}for(aM=60;aM<=79;aM++){aN=(aw(aI,5)+(aH^aG^aF)+aE+av[aM]+3395469782)&4294967295;aE=aF;aF=aG;aG=aw(aH,30);aH=aI;aI=aN}aD=(aD+aI)&4294967295;aB=(aB+aH)&4294967295;aA=(aA+aG)&4294967295;ay=(ay+aF)&4294967295;ax=(ax+aE)&4294967295}aN=aK(aD)+aK(aB)+aK(aA)+aK(ay)+aK(ax);return aN.toLowerCase()}function af(aw,au,av){if(!aw){aw=""}if(!au){au=""}if(aw==="translate.googleusercontent.com"){if(av===""){av=au}au=e(au,"u");aw=d(au)}else{if(aw==="cc.bingj.com"||aw==="webcache.googleusercontent.com"||aw.slice(0,5)==="74.6."){au=K.links[0].href;aw=d(au)}}return[aw,au,av]}function P(av){var au=av.length;if(av.charAt(--au)==="."){av=av.slice(0,au)}if(av.slice(0,2)==="*."){av=av.slice(1)}if(av.indexOf("/")!==-1){av=av.substr(0,av.indexOf("/"))}return av}function aq(av){av=av&&av.text?av.text:av;if(!y(av)){var au=K.getElementsByTagName("title");
if(au&&N(au[0])){av=au[0].text}}return av}function T(au){if(!au){return[]}if(!N(au.children)&&N(au.childNodes)){return au.children}if(N(au.children)){return au.children}return[]}function Z(av,au){if(!av||!au){return false}if(av.contains){return av.contains(au)}if(av===au){return true}if(av.compareDocumentPosition){return !!(av.compareDocumentPosition(au)&16)}return false}function Q(aw,ax){if(aw&&aw.indexOf){return aw.indexOf(ax)}if(!N(aw)||aw===null){return -1}if(!aw.length){return -1}var au=aw.length;if(au===0){return -1}var av=0;while(av<au){if(aw[av]===ax){return av}av++}return -1}function i(aw){if(!aw){return false}function au(ay,az){if(X.getComputedStyle){return K.defaultView.getComputedStyle(ay,null)[az]}if(ay.currentStyle){return ay.currentStyle[az]}}function ax(ay){ay=ay.parentNode;while(ay){if(ay===K){return true}ay=ay.parentNode}return false}function av(aA,aG,ay,aD,aB,aE,aC){var az=aA.parentNode,aF=1;if(!ax(aA)){return false}if(9===az.nodeType){return true}if("0"===au(aA,"opacity")||"none"===au(aA,"display")||"hidden"===au(aA,"visibility")){return false
}if(!N(aG)||!N(ay)||!N(aD)||!N(aB)||!N(aE)||!N(aC)){aG=aA.offsetTop;aB=aA.offsetLeft;aD=aG+aA.offsetHeight;ay=aB+aA.offsetWidth;aE=aA.offsetWidth;aC=aA.offsetHeight}if(aw===aA&&(0===aC||0===aE)&&"hidden"===au(aA,"overflow")){return false}if(az){if(("hidden"===au(az,"overflow")||"scroll"===au(az,"overflow"))){if(aB+aF>az.offsetWidth+az.scrollLeft||aB+aE-aF<az.scrollLeft||aG+aF>az.offsetHeight+az.scrollTop||aG+aC-aF<az.scrollTop){return false}}if(aA.offsetParent===az){aB+=az.offsetLeft;aG+=az.offsetTop}return av(az,aG,ay,aD,aB,aE,aC)}return true}return av(aw)}var aj={htmlCollectionToArray:function(aw){var au=[],av;if(!aw||!aw.length){return au}for(av=0;av<aw.length;av++){au.push(aw[av])}return au},find:function(au){if(!document.querySelectorAll||!au){return[]}var av=document.querySelectorAll(au);return this.htmlCollectionToArray(av)},findMultiple:function(aw){if(!aw||!aw.length){return[]}var av,ax;var au=[];for(av=0;av<aw.length;av++){ax=this.find(aw[av]);au=au.concat(ax)}au=this.makeNodesUnique(au);
return au},findNodesByTagName:function(av,au){if(!av||!au||!av.getElementsByTagName){return[]}var aw=av.getElementsByTagName(au);return this.htmlCollectionToArray(aw)},makeNodesUnique:function(au){var az=[].concat(au);au.sort(function(aB,aA){if(aB===aA){return 0}var aD=Q(az,aB);var aC=Q(az,aA);if(aD===aC){return 0}return aD>aC?-1:1});if(au.length<=1){return au}var av=0;var ax=0;var ay=[];var aw;aw=au[av++];while(aw){if(aw===au[av]){ax=ay.push(av)}aw=au[av++]||null}while(ax--){au.splice(ay[ax],1)}return au},getAttributeValueFromNode:function(ay,aw){if(!this.hasNodeAttribute(ay,aw)){return}if(ay&&ay.getAttribute){return ay.getAttribute(aw)}if(!ay||!ay.attributes){return}var ax=(typeof ay.attributes[aw]);if("undefined"===ax){return}if(ay.attributes[aw].value){return ay.attributes[aw].value}if(ay.attributes[aw].nodeValue){return ay.attributes[aw].nodeValue}var av;var au=ay.attributes;if(!au){return}for(av=0;av<au.length;av++){if(au[av].nodeName===aw){return au[av].nodeValue}}return null},hasNodeAttributeWithValue:function(av,au){var aw=this.getAttributeValueFromNode(av,au);
return !!aw},hasNodeAttribute:function(aw,au){if(aw&&aw.hasAttribute){return aw.hasAttribute(au)}if(aw&&aw.attributes){var av=(typeof aw.attributes[au]);return"undefined"!==av}return false},hasNodeCssClass:function(aw,au){if(aw&&au&&aw.className){var av=typeof aw.className==="string"?aw.className.split(" "):[];if(-1!==Q(av,au)){return true}}return false},findNodesHavingAttribute:function(ay,aw,au){if(!au){au=[]}if(!ay||!aw){return au}var ax=T(ay);if(!ax||!ax.length){return au}var av,az;for(av=0;av<ax.length;av++){az=ax[av];if(this.hasNodeAttribute(az,aw)){au.push(az)}au=this.findNodesHavingAttribute(az,aw,au)}return au},findFirstNodeHavingAttribute:function(aw,av){if(!aw||!av){return}if(this.hasNodeAttribute(aw,av)){return aw}var au=this.findNodesHavingAttribute(aw,av);if(au&&au.length){return au[0]}},findFirstNodeHavingAttributeWithValue:function(ax,aw){if(!ax||!aw){return}if(this.hasNodeAttributeWithValue(ax,aw)){return ax}var au=this.findNodesHavingAttribute(ax,aw);if(!au||!au.length){return
}var av;for(av=0;av<au.length;av++){if(this.getAttributeValueFromNode(au[av],aw)){return au[av]}}},findNodesHavingCssClass:function(ay,ax,au){if(!au){au=[]}if(!ay||!ax){return au}if(ay.getElementsByClassName){var az=ay.getElementsByClassName(ax);return this.htmlCollectionToArray(az)}var aw=T(ay);if(!aw||!aw.length){return[]}var av,aA;for(av=0;av<aw.length;av++){aA=aw[av];if(this.hasNodeCssClass(aA,ax)){au.push(aA)}au=this.findNodesHavingCssClass(aA,ax,au)}return au},findFirstNodeHavingClass:function(aw,av){if(!aw||!av){return}if(this.hasNodeCssClass(aw,av)){return aw}var au=this.findNodesHavingCssClass(aw,av);if(au&&au.length){return au[0]}},isLinkElement:function(av){if(!av){return false}var au=String(av.nodeName).toLowerCase();var ax=["a","area"];var aw=Q(ax,au);return aw!==-1},setAnyAttribute:function(av,au,aw){if(!av||!au){return}if(av.setAttribute){av.setAttribute(au,aw)}else{av[au]=aw}}};var x={CONTENT_ATTR:"data-track-content",CONTENT_CLASS:"matomoTrackContent",LEGACY_CONTENT_CLASS:"piwikTrackContent",CONTENT_NAME_ATTR:"data-content-name",CONTENT_PIECE_ATTR:"data-content-piece",CONTENT_PIECE_CLASS:"matomoContentPiece",LEGACY_CONTENT_PIECE_CLASS:"piwikContentPiece",CONTENT_TARGET_ATTR:"data-content-target",CONTENT_TARGET_CLASS:"matomoContentTarget",LEGACY_CONTENT_TARGET_CLASS:"piwikContentTarget",CONTENT_IGNOREINTERACTION_ATTR:"data-content-ignoreinteraction",CONTENT_IGNOREINTERACTION_CLASS:"matomoContentIgnoreInteraction",LEGACY_CONTENT_IGNOREINTERACTION_CLASS:"piwikContentIgnoreInteraction",location:undefined,findContentNodes:function(){var av="."+this.CONTENT_CLASS;
var aw="."+this.LEGACY_CONTENT_CLASS;var au="["+this.CONTENT_ATTR+"]";var ax=aj.findMultiple([av,aw,au]);return ax},findContentNodesWithinNode:function(ax){if(!ax){return[]}var av=aj.findNodesHavingCssClass(ax,this.CONTENT_CLASS);av=aj.findNodesHavingCssClass(ax,this.LEGACY_CONTENT_CLASS,av);var au=aj.findNodesHavingAttribute(ax,this.CONTENT_ATTR);if(au&&au.length){var aw;for(aw=0;aw<au.length;aw++){av.push(au[aw])}}if(aj.hasNodeAttribute(ax,this.CONTENT_ATTR)){av.push(ax)}else{if(aj.hasNodeCssClass(ax,this.CONTENT_CLASS)){av.push(ax)}else{if(aj.hasNodeCssClass(ax,this.LEGACY_CONTENT_CLASS)){av.push(ax)}}}av=aj.makeNodesUnique(av);return av},findParentContentNode:function(av){if(!av){return}var aw=av;var au=0;while(aw&&aw!==K&&aw.parentNode){if(aj.hasNodeAttribute(aw,this.CONTENT_ATTR)){return aw}if(aj.hasNodeCssClass(aw,this.CONTENT_CLASS)){return aw}if(aj.hasNodeCssClass(aw,this.LEGACY_CONTENT_CLASS)){return aw}aw=aw.parentNode;if(au>1000){break}au++}},findPieceNode:function(av){var au;
au=aj.findFirstNodeHavingAttribute(av,this.CONTENT_PIECE_ATTR);if(!au){au=aj.findFirstNodeHavingClass(av,this.CONTENT_PIECE_CLASS)}if(!au){au=aj.findFirstNodeHavingClass(av,this.LEGACY_CONTENT_PIECE_CLASS)}if(au){return au}return av},findTargetNodeNoDefault:function(au){if(!au){return}var av=aj.findFirstNodeHavingAttributeWithValue(au,this.CONTENT_TARGET_ATTR);if(av){return av}av=aj.findFirstNodeHavingAttribute(au,this.CONTENT_TARGET_ATTR);if(av){return av}av=aj.findFirstNodeHavingClass(au,this.CONTENT_TARGET_CLASS);if(av){return av}av=aj.findFirstNodeHavingClass(au,this.LEGACY_CONTENT_TARGET_CLASS);if(av){return av}},findTargetNode:function(au){var av=this.findTargetNodeNoDefault(au);if(av){return av}return au},findContentName:function(av){if(!av){return}var ay=aj.findFirstNodeHavingAttributeWithValue(av,this.CONTENT_NAME_ATTR);if(ay){return aj.getAttributeValueFromNode(ay,this.CONTENT_NAME_ATTR)}var au=this.findContentPiece(av);if(au){return this.removeDomainIfIsInLink(au)}if(aj.hasNodeAttributeWithValue(av,"title")){return aj.getAttributeValueFromNode(av,"title")
}var aw=this.findPieceNode(av);if(aj.hasNodeAttributeWithValue(aw,"title")){return aj.getAttributeValueFromNode(aw,"title")}var ax=this.findTargetNode(av);if(aj.hasNodeAttributeWithValue(ax,"title")){return aj.getAttributeValueFromNode(ax,"title")}},findContentPiece:function(av){if(!av){return}var ax=aj.findFirstNodeHavingAttributeWithValue(av,this.CONTENT_PIECE_ATTR);if(ax){return aj.getAttributeValueFromNode(ax,this.CONTENT_PIECE_ATTR)}var au=this.findPieceNode(av);var aw=this.findMediaUrlInNode(au);if(aw){return this.toAbsoluteUrl(aw)}},findContentTarget:function(aw){if(!aw){return}var ax=this.findTargetNode(aw);if(aj.hasNodeAttributeWithValue(ax,this.CONTENT_TARGET_ATTR)){return aj.getAttributeValueFromNode(ax,this.CONTENT_TARGET_ATTR)}var av;if(aj.hasNodeAttributeWithValue(ax,"href")){av=aj.getAttributeValueFromNode(ax,"href");return this.toAbsoluteUrl(av)}var au=this.findPieceNode(aw);if(aj.hasNodeAttributeWithValue(au,"href")){av=aj.getAttributeValueFromNode(au,"href");return this.toAbsoluteUrl(av)
}},isSameDomain:function(au){if(!au||!au.indexOf){return false}if(0===au.indexOf(this.getLocation().origin)){return true}var av=au.indexOf(this.getLocation().host);if(8>=av&&0<=av){return true}return false},removeDomainIfIsInLink:function(aw){var av="^https?://[^/]+";var au="^.*//[^/]+";if(aw&&aw.search&&-1!==aw.search(new RegExp(av))&&this.isSameDomain(aw)){aw=aw.replace(new RegExp(au),"");if(!aw){aw="/"}}return aw},findMediaUrlInNode:function(ay){if(!ay){return}var aw=["img","embed","video","audio"];var au=ay.nodeName.toLowerCase();if(-1!==Q(aw,au)&&aj.findFirstNodeHavingAttributeWithValue(ay,"src")){var ax=aj.findFirstNodeHavingAttributeWithValue(ay,"src");return aj.getAttributeValueFromNode(ax,"src")}if(au==="object"&&aj.hasNodeAttributeWithValue(ay,"data")){return aj.getAttributeValueFromNode(ay,"data")}if(au==="object"){var az=aj.findNodesByTagName(ay,"param");if(az&&az.length){var av;for(av=0;av<az.length;av++){if("movie"===aj.getAttributeValueFromNode(az[av],"name")&&aj.hasNodeAttributeWithValue(az[av],"value")){return aj.getAttributeValueFromNode(az[av],"value")
}}}var aA=aj.findNodesByTagName(ay,"embed");if(aA&&aA.length){return this.findMediaUrlInNode(aA[0])}}},trim:function(au){return a(au)},isOrWasNodeInViewport:function(az){if(!az||!az.getBoundingClientRect||az.nodeType!==1){return true}var ay=az.getBoundingClientRect();var ax=K.documentElement||{};var aw=ay.top<0;if(aw&&az.offsetTop){aw=(az.offsetTop+ay.height)>0}var av=ax.clientWidth;if(X.innerWidth&&av>X.innerWidth){av=X.innerWidth}var au=ax.clientHeight;if(X.innerHeight&&au>X.innerHeight){au=X.innerHeight}return((ay.bottom>0||aw)&&ay.right>0&&ay.left<av&&((ay.top<au)||aw))},isNodeVisible:function(av){var au=i(av);var aw=this.isOrWasNodeInViewport(av);return au&&aw},buildInteractionRequestParams:function(au,av,aw,ax){var ay="";if(au){ay+="c_i="+u(au)}if(av){if(ay){ay+="&"}ay+="c_n="+u(av)}if(aw){if(ay){ay+="&"}ay+="c_p="+u(aw)}if(ax){if(ay){ay+="&"}ay+="c_t="+u(ax)}if(ay){ay+="&ca=1"}return ay},buildImpressionRequestParams:function(au,av,aw){var ax="c_n="+u(au)+"&c_p="+u(av);if(aw){ax+="&c_t="+u(aw)
}if(ax){ax+="&ca=1"}return ax},buildContentBlock:function(aw){if(!aw){return}var au=this.findContentName(aw);var av=this.findContentPiece(aw);var ax=this.findContentTarget(aw);au=this.trim(au);av=this.trim(av);ax=this.trim(ax);return{name:au||"Unknown",piece:av||"Unknown",target:ax||""}},collectContent:function(ax){if(!ax||!ax.length){return[]}var aw=[];var au,av;for(au=0;au<ax.length;au++){av=this.buildContentBlock(ax[au]);if(N(av)){aw.push(av)}}return aw},setLocation:function(au){this.location=au},getLocation:function(){var au=this.location||X.location;if(!au.origin){au.origin=au.protocol+"//"+au.hostname+(au.port?":"+au.port:"")}return au},toAbsoluteUrl:function(av){if((!av||String(av)!==av)&&av!==""){return av}if(""===av){return this.getLocation().href}if(av.search(/^\/\//)!==-1){return this.getLocation().protocol+av}if(av.search(/:\/\//)!==-1){return av}if(0===av.indexOf("#")){return this.getLocation().origin+this.getLocation().pathname+av}if(0===av.indexOf("?")){return this.getLocation().origin+this.getLocation().pathname+av
}if(0===av.search("^[a-zA-Z]{2,11}:")){return av}if(av.search(/^\//)!==-1){return this.getLocation().origin+av}var au="(.*/)";var aw=this.getLocation().origin+this.getLocation().pathname.match(new RegExp(au))[0];return aw+av},isUrlToCurrentDomain:function(av){var aw=this.toAbsoluteUrl(av);if(!aw){return false}var au=this.getLocation().origin;if(au===aw){return true}if(0===String(aw).indexOf(au)){if(":"===String(aw).substr(au.length,1)){return false}return true}return false},setHrefAttribute:function(av,au){if(!av||!au){return}aj.setAnyAttribute(av,"href",au)},shouldIgnoreInteraction:function(au){if(aj.hasNodeAttribute(au,this.CONTENT_IGNOREINTERACTION_ATTR)){return true}if(aj.hasNodeCssClass(au,this.CONTENT_IGNOREINTERACTION_CLASS)){return true}if(aj.hasNodeCssClass(au,this.LEGACY_CONTENT_IGNOREINTERACTION_CLASS)){return true}return false}};function ab(av,ay){if(ay){return ay}av=x.toAbsoluteUrl(av);if(B(av,"?")){var ax=av.indexOf("?");av=av.slice(0,ax)}if(V(av,"matomo.php")){av=f(av,"matomo.php".length)
}else{if(V(av,"piwik.php")){av=f(av,"piwik.php".length)}else{if(V(av,".php")){var au=av.lastIndexOf("/");var aw=1;av=av.slice(0,au+aw)}}}if(V(av,"/js/")){av=f(av,"js/".length)}return av}function S(aA){var aC="Matomo_Overlay";var av=new RegExp("index\\.php\\?module=Overlay&action=startOverlaySession&idSite=([0-9]+)&period=([^&]+)&date=([^&]+)(&segment=[^&]*)?");var aw=av.exec(K.referrer);if(aw){var ay=aw[1];if(ay!==String(aA)){return false}var az=aw[2],au=aw[3],ax=aw[4];if(!ax){ax=""}else{if(ax.indexOf("&segment=")===0){ax=ax.substr("&segment=".length)}}X.name=aC+"###"+az+"###"+au+"###"+ax}var aB=X.name.split("###");return aB.length===4&&aB[0]===aC}function ae(av,aA,aw){var az=X.name.split("###"),ay=az[1],au=az[2],ax=az[3],aB=ab(av,aA);o(aB+"plugins/Overlay/client/client.js?v=1",function(){Matomo_Overlay_Client.initialize(aB,aw,ay,au,ax)})}function w(){var aw;try{aw=X.frameElement}catch(av){return true}if(N(aw)){return(aw&&String(aw.nodeName).toLowerCase()==="iframe")?true:false}try{return X.self!==X.top
}catch(au){return true}}function U(ct,cn){var bV=this,bo="mtm_consent",c0="mtm_cookie_consent",c9="mtm_consent_removed",ch=af(K.domain,X.location.href,O()),dh=P(ch[0]),bZ=p(ch[1]),bA=p(ch[2]),df=false,cx="GET",dA=cx,aQ="application/x-www-form-urlencoded; charset=UTF-8",cR=aQ,aM=ct||"",bU="",dp="",cD="",cj=cn||"",bL="",b0="",bf,bu="",dw=["7z","aac","apk","arc","arj","asc","asf","asx","avi","azw3","bin","csv","deb","dmg","doc","docx","epub","exe","flv","gif","gz","gzip","hqx","ibooks","jar","jpg","jpeg","js","md5","mobi","mp2","mp3","mp4","mpg","mpeg","mov","movie","msi","msp","odb","odf","odg","ods","odt","ogg","ogv","pdf","phps","png","ppt","pptx","qt","qtm","ra","ram","rar","rpm","rtf","sea","sha","sha256","sha512","sig","sit","tar","tbz","tbz2","bz","bz2","tgz","torrent","txt","wav","wma","wmv","wpd","xls","xlsx","xml","xz","z","zip"],aG=[dh],bM=[],cS=[".paypal.com"],cy=[],bY=[],bj=[],bW=500,dk=true,c6,bg,b4,b1,aw,cH=["pk_campaign","mtm_campaign","piwik_campaign","matomo_campaign","utm_campaign","utm_source","utm_medium"],bT=["pk_kwd","mtm_kwd","piwik_kwd","matomo_kwd","utm_term"],bv="_pk_",aD="pk_vid",ba=180,dm,bC,b5=false,aR="Lax",bx=false,dd,bp,bI,c7=33955200000,cE=1800000,dv=15768000000,bd=true,bR=false,bs=false,b3=false,aZ=false,cq,b9={},cC={},bz={},bG=200,cN={},dq={},dx={},a3={},co=[],by=false,ck=false,cp=[],cu=false,cY=false,ax=false,dy=false,da=false,aW=false,bn=w(),cT=null,dn=null,a0,bO,cl=ar,bB,aU,bN=false,cK=0,bH=["id","ses","cvar","ref"],cX=false,bP=null,c8=[],cM=[],aF=Y++,aE=false,dl=true,cV=false;
try{bu=K.title}catch(cU){bu=""}function aL(dL){if(bx&&dL!==c9){return 0}var dJ=new RegExp("(^|;)[ ]*"+dL+"=([^;]*)"),dK=dJ.exec(K.cookie);return dK?W(dK[2]):0}bP=!aL(c9);function dE(dN,dO,dR,dQ,dL,dM,dP){if(bx&&dN!==c9){return}var dK;if(dR){dK=new Date();dK.setTime(dK.getTime()+dR)}if(!dP){dP="Lax"}K.cookie=dN+"="+u(dO)+(dR?";expires="+dK.toGMTString():"")+";path="+(dQ||"/")+(dL?";domain="+dL:"")+(dM?";secure":"")+";SameSite="+dP;if((!dR||dR>=0)&&aL(dN)!==String(dO)){var dJ="There was an error setting cookie `"+dN+"`. Please check domain and path.";ap(dJ)}}function cf(dJ){var dL,dK;dJ=j(dJ,aD);dJ=j(dJ,"ignore_referrer");dJ=j(dJ,"ignore_referer");for(dK=0;dK<cy.length;dK++){dJ=j(dJ,cy[dK])}if(b1){dL=new RegExp("#.*");return dJ.replace(dL,"")}return dJ}function b8(dL,dJ){var dM=t(dJ),dK;if(dM){return dJ}if(dJ.slice(0,1)==="/"){return t(dL)+"://"+d(dL)+dJ}dL=cf(dL);dK=dL.indexOf("?");if(dK>=0){dL=dL.slice(0,dK)}dK=dL.lastIndexOf("/");if(dK!==dL.length-1){dL=dL.slice(0,dK+1)}return dL+dJ}function c4(dL,dJ){var dK;
dL=String(dL).toLowerCase();dJ=String(dJ).toLowerCase();if(dL===dJ){return true}if(dJ.slice(0,1)==="."){if(dL===dJ.slice(1)){return true}dK=dL.length-dJ.length;if((dK>0)&&(dL.slice(dK)===dJ)){return true}}return false}function cB(dJ){var dK=document.createElement("a");if(dJ.indexOf("//")!==0&&dJ.indexOf("http")!==0){if(dJ.indexOf("*")===0){dJ=dJ.substr(1)}if(dJ.indexOf(".")===0){dJ=dJ.substr(1)}dJ="http://"+dJ}dK.href=x.toAbsoluteUrl(dJ);if(dK.pathname){return dK.pathname}return""}function be(dK,dJ){if(!ao(dJ,"/")){dJ="/"+dJ}if(!ao(dK,"/")){dK="/"+dK}var dL=(dJ==="/"||dJ==="/*");if(dL){return true}if(dK===dJ){return true}dJ=String(dJ).toLowerCase();dK=String(dK).toLowerCase();if(V(dJ,"*")){dJ=dJ.slice(0,-1);dL=(!dJ||dJ==="/");if(dL){return true}if(dK===dJ){return true}return dK.indexOf(dJ)===0}if(!V(dK,"/")){dK+="/"}if(!V(dJ,"/")){dJ+="/"}return dK.indexOf(dJ)===0}function aA(dN,dP){var dK,dJ,dL,dM,dO;for(dK=0;dK<aG.length;dK++){dM=P(aG[dK]);dO=cB(aG[dK]);if(c4(dN,dM)&&be(dP,dO)){return true
}}return false}function a6(dM){var dK,dJ,dL;for(dK=0;dK<aG.length;dK++){dJ=P(aG[dK].toLowerCase());if(dM===dJ){return true}if(dJ.slice(0,1)==="."){if(dM===dJ.slice(1)){return true}dL=dM.length-dJ.length;if((dL>0)&&(dM.slice(dL)===dJ)){return true}}}return false}function cJ(dJ){var dK,dM,dO,dL,dN;if(!dJ.length||!cS.length){return false}dM=d(dJ);dO=cB(dJ);if(dM.indexOf("www.")===0){dM=dM.substr(4)}for(dK=0;dK<cS.length;dK++){dL=P(cS[dK]);dN=cB(cS[dK]);if(dL.indexOf("www.")===0){dL=dL.substr(4)}if(c4(dM,dL)&&be(dO,dN)){return true}}return false}function au(){if(q&&q.length>0){return true}q=e(X.location.href,"tracker_install_check");return q&&q.length>0}function cI(){if(au()&&aa(X)){X.close()}}function cF(dJ,dL){dJ=dJ.replace("send_image=0","send_image=1");var dK=new Image(1,1);dK.onload=function(){I=0;if(typeof dL==="function"){dL({request:dJ,trackerUrl:aM,success:true})}};dK.onerror=function(){if(typeof dL==="function"){dL({request:dJ,trackerUrl:aM,success:false})}};dK.src=aM+(aM.indexOf("?")<0?"?":"&")+dJ;
cI()}function c1(dJ){if(dA==="POST"){return true}return dJ&&(dJ.length>2000||dJ.indexOf('{"requests"')===0)}function aT(){return"object"===typeof g&&"function"===typeof g.sendBeacon&&"function"===typeof Blob}function bh(dN,dQ,dP){var dL=aT();if(!dL){return false}var dM={type:"application/x-www-form-urlencoded; charset=UTF-8"};var dR=false;var dK=aM;try{var dJ=new Blob([dN],dM);if(dP&&!c1(dN)){dJ=new Blob([],dM);dK=dK+(dK.indexOf("?")<0?"?":"&")+dN}dR=g.sendBeacon(dK,dJ)}catch(dO){return false}if(dR&&typeof dQ==="function"){dQ({request:dN,trackerUrl:aM,success:true,isSendBeacon:true})}cI();return dR}function du(dK,dL,dJ){if(!N(dJ)||null===dJ){dJ=true}if(m&&bh(dK,dL,dJ)){return}setTimeout(function(){if(m&&bh(dK,dL,dJ)){return}var dO;try{var dN=X.XMLHttpRequest?new X.XMLHttpRequest():X.ActiveXObject?new ActiveXObject("Microsoft.XMLHTTP"):null;dN.open("POST",aM,true);dN.onreadystatechange=function(){if(this.readyState===4&&!(this.status>=200&&this.status<300)){var dP=m&&bh(dK,dL,dJ);if(!dP&&dJ){cF(dK,dL)
}else{if(typeof dL==="function"){dL({request:dK,trackerUrl:aM,success:false,xhr:this})}}}else{if(this.readyState===4&&(typeof dL==="function")){dL({request:dK,trackerUrl:aM,success:true,xhr:this})}}};dN.setRequestHeader("Content-Type",cR);dN.withCredentials=true;dN.send(dK)}catch(dM){dO=m&&bh(dK,dL,dJ);if(!dO&&dJ){cF(dK,dL)}else{if(typeof dL==="function"){dL({request:dK,trackerUrl:aM,success:false})}}}cI()},50)}function cv(dK){var dJ=new Date();var dL=dJ.getTime()+dK;if(!s||dL>s){s=dL}}function bl(){bn=true;cT=new Date().getTime()}function dD(){var dJ=new Date().getTime();return !cT||(dJ-cT)>bg}function aH(){if(dD()){b4()}}function a5(){if(K.visibilityState==="hidden"&&dD()){b4()}else{if(K.visibilityState==="visible"){cT=new Date().getTime()}}}function dH(){if(aW||!bg){return}aW=true;at(X,"focus",bl);at(X,"blur",aH);at(X,"visibilitychange",a5);ag++;v.addPlugin("HeartBeat"+ag,{unload:function(){if(aW&&dD()){b4()}}})}function cZ(dN){var dK=new Date();var dJ=dK.getTime();dn=dJ;if(cY&&dJ<cY){var dL=cY-dJ;
setTimeout(dN,dL);cv(dL+50);cY+=50;return}if(cY===false){var dM=800;cY=dJ+dM}dN()}function aX(){if(aL(c9)){bP=false}else{if(aL(bo)){bP=true}}}function b2(dM){var dL,dK="",dJ="";for(dL in dx){if(Object.prototype.hasOwnProperty.call(dx,dL)){dJ+="&"+dL+"="+dx[dL]}}if(a3){dK="&uadata="+u(X.JSON.stringify(a3))}if(dM instanceof Array){for(dL=0;dL<dM.length;dL++){dM[dL]+=dK+dJ}}else{dM+=dK+dJ}return dM}function av(){return N(g.userAgentData)&&D(g.userAgentData.getHighEntropyValues)}function cG(dJ){if(by||ck){return}ck=true;a3={brands:g.userAgentData.brands,platform:g.userAgentData.platform};g.userAgentData.getHighEntropyValues(["brands","model","platform","platformVersion","uaFullVersion","fullVersionList"]).then(function(dL){var dK;if(dL.fullVersionList){delete dL.brands;delete dL.uaFullVersion}a3=dL;by=true;ck=false;dJ()},function(dK){by=true;ck=false;dJ()})}function bS(dK,dJ,dL){aX();if(!bP){c8.push([dK,dL]);return}if(dl&&!by&&av()){co.push([dK,dL]);return}aE=true;if(!dd&&dK){if(cX&&bP){dK+="&consent=1"
}dK=b2(dK);cZ(function(){if(dk&&bh(dK,dL,true)){cv(100);return}if(c1(dK)){du(dK,dL)}else{cF(dK,dL)}cv(dJ)})}if(!aW){dH()}}function cA(dJ){if(dd){return false}return(dJ&&dJ.length)}function dt(dJ,dN){if(!dN||dN>=dJ.length){return[dJ]}var dK=0;var dL=dJ.length;var dM=[];for(dK;dK<dL;dK+=dN){dM.push(dJ.slice(dK,dK+dN))}return dM}function dF(dK,dJ){if(!cA(dK)){return}if(dl&&!by&&av()){co.push([dK,null]);return}if(!bP){c8.push([dK,null]);return}aE=true;cZ(function(){var dN=dt(dK,50);var dL=0,dM;for(dL;dL<dN.length;dL++){dM='{"requests":["?'+b2(dN[dL]).join('","?')+'"],"send_image":0}';if(dk&&bh(dM,null,false)){cv(100)}else{du(dM,null,false)}}cv(dJ)})}function a2(dJ){return bv+dJ+"."+cj+"."+bB}function cc(dL,dK,dJ){dE(dL,"",-129600000,dK,dJ)}function ci(){if(bx){return"0"}if(!N(X.showModalDialog)&&N(g.cookieEnabled)){return g.cookieEnabled?"1":"0"}var dJ=bv+"testcookie";dE(dJ,"1",undefined,bC,dm,b5,aR);var dK=aL(dJ)==="1"?"1":"0";cc(dJ);return dK}function bt(){bB=cl((dm||dh)+(bC||"/")).slice(0,4)
}function ay(){var dK,dJ;for(dK=0;dK<co.length;dK++){dJ=typeof co[dK][0];if(dJ==="string"){bS(co[dK][0],bW,co[dK][1])}else{if(dJ==="object"){dF(co[dK][0],bW)}}}co=[]}function c5(){if(!dl){return{}}if(av()){cG(ay)}if(N(dx.res)){return dx}var dK,dM,dN={pdf:"application/pdf",qt:"video/quicktime",realp:"audio/x-pn-realaudio-plugin",wma:"application/x-mplayer2",fla:"application/x-shockwave-flash",java:"application/x-java-vm",ag:"application/x-silverlight"};if(!((new RegExp("MSIE")).test(g.userAgent))){if(g.mimeTypes&&g.mimeTypes.length){for(dK in dN){if(Object.prototype.hasOwnProperty.call(dN,dK)){dM=g.mimeTypes[dN[dK]];dx[dK]=(dM&&dM.enabledPlugin)?"1":"0"}}}if(!((new RegExp("Edge[ /](\\d+[\\.\\d]+)")).test(g.userAgent))&&typeof navigator.javaEnabled!=="unknown"&&N(g.javaEnabled)&&g.javaEnabled()){dx.java="1"}if(!N(X.showModalDialog)&&N(g.cookieEnabled)){dx.cookie=g.cookieEnabled?"1":"0"}else{dx.cookie=ci()}}var dL=parseInt(ac.width,10);var dJ=parseInt(ac.height,10);dx.res=parseInt(dL,10)+"x"+parseInt(dJ,10);
return dx}function ca(){var dK=a2("cvar"),dJ=aL(dK);if(dJ&&dJ.length){dJ=X.JSON.parse(dJ);if(aa(dJ)){return dJ}}return{}}function c2(){if(aZ===false){aZ=ca()}}function de(){var dJ=c5();return cl((g.userAgent||"")+(g.platform||"")+X.JSON.stringify(dJ)+(new Date()).getTime()+Math.random()).slice(0,16)}function aJ(){var dJ=c5();return cl((g.userAgent||"")+(g.platform||"")+X.JSON.stringify(dJ)).slice(0,6)}function bq(){return Math.floor((new Date()).getTime()/1000)}function aS(){var dK=bq();var dL=aJ();var dJ=String(dK)+dL;return dJ}function ds(dL){dL=String(dL);var dO=aJ();var dM=dO.length;var dN=dL.substr(-1*dM,dM);var dK=parseInt(dL.substr(0,dL.length-dM),10);if(dK&&dN&&dN===dO){var dJ=bq();if(ba<=0){return true}if(dJ>=dK&&dJ<=(dK+ba)){return true}}return false}function dG(dJ){if(!da){return""}var dN=e(dJ,aD);if(!dN){return""}dN=String(dN);var dL=new RegExp("^[a-zA-Z0-9]+$");if(dN.length===32&&dL.test(dN)){var dK=dN.substr(16,32);if(ds(dK)){var dM=dN.substr(0,16);return dM}}return""}function db(){if(!b0){b0=dG(bZ)
}var dL=new Date(),dJ=Math.round(dL.getTime()/1000),dK=a2("id"),dO=aL(dK),dN,dM;if(dO){dN=dO.split(".");dN.unshift("0");if(b0.length){dN[1]=b0}return dN}if(b0.length){dM=b0}else{if("0"===ci()){dM=""}else{dM=de()}}dN=["1",dM,dJ];return dN}function a9(){var dM=db(),dK=dM[0],dL=dM[1],dJ=dM[2];return{newVisitor:dK,uuid:dL,createTs:dJ}}function aP(){var dM=new Date(),dK=dM.getTime(),dN=a9().createTs;var dJ=parseInt(dN,10);var dL=(dJ*1000)+c7-dK;return dL}function aV(dJ){if(!cj){return}var dL=new Date(),dK=Math.round(dL.getTime()/1000);if(!N(dJ)){dJ=a9()}var dM=dJ.uuid+"."+dJ.createTs+".";dE(a2("id"),dM,aP(),bC,dm,b5,aR)}function bX(){var dJ=aL(a2("ref"));if(dJ.length){try{dJ=X.JSON.parse(dJ);if(aa(dJ)){return dJ}}catch(dK){}}return["","",0,""]}function bJ(dL){var dK=bv+"testcookie_domain";var dJ="testvalue";dE(dK,dJ,10000,null,dL,b5,aR);if(aL(dK)===dJ){cc(dK,null,dL);return true}return false}function aN(){var dK=bx;bx=false;var dJ,dL;for(dJ=0;dJ<bH.length;dJ++){dL=a2(bH[dJ]);if(dL!==c9&&dL!==bo&&0!==aL(dL)){cc(dL,bC,dm)
}}bx=dK}function cg(dJ){cj=dJ}function dI(dN){if(!dN||!aa(dN)){return}var dM=[];var dL;for(dL in dN){if(Object.prototype.hasOwnProperty.call(dN,dL)){dM.push(dL)}}var dO={};dM.sort();var dJ=dM.length;var dK;for(dK=0;dK<dJ;dK++){dO[dM[dK]]=dN[dM[dK]]}return dO}function cs(){dE(a2("ses"),"1",cE,bC,dm,b5,aR)}function br(){var dM="";var dK="abcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";var dL=dK.length;var dJ;for(dJ=0;dJ<6;dJ++){dM+=dK.charAt(Math.floor(Math.random()*dL))}return dM}function aI(dK){if(cD!==""){dK+=cD;bs=true;return dK}if(!h){return dK}var dL=(typeof h.timing==="object")&&h.timing?h.timing:undefined;if(!dL){dL=(typeof h.getEntriesByType==="function")&&h.getEntriesByType("navigation")?h.getEntriesByType("navigation")[0]:undefined}if(!dL){return dK}var dJ="";if(dL.connectEnd&&dL.fetchStart){if(dL.connectEnd<dL.fetchStart){return dK}dJ+="&pf_net="+Math.round(dL.connectEnd-dL.fetchStart)}if(dL.responseStart&&dL.requestStart){if(dL.responseStart<dL.requestStart){return dK
}dJ+="&pf_srv="+Math.round(dL.responseStart-dL.requestStart)}if(dL.responseStart&&dL.responseEnd){if(dL.responseEnd<dL.responseStart){return dK}dJ+="&pf_tfr="+Math.round(dL.responseEnd-dL.responseStart)}if(N(dL.domLoading)){if(dL.domInteractive&&dL.domLoading){if(dL.domInteractive<dL.domLoading){return dK}dJ+="&pf_dm1="+Math.round(dL.domInteractive-dL.domLoading)}}else{if(dL.domInteractive&&dL.responseEnd){if(dL.domInteractive<dL.responseEnd){return dK}dJ+="&pf_dm1="+Math.round(dL.domInteractive-dL.responseEnd)}}if(dL.domComplete&&dL.domInteractive){if(dL.domComplete<dL.domInteractive){return dK}dJ+="&pf_dm2="+Math.round(dL.domComplete-dL.domInteractive)}if(dL.loadEventEnd&&dL.loadEventStart){if(dL.loadEventEnd<dL.loadEventStart){return dK}dJ+="&pf_onl="+Math.round(dL.loadEventEnd-dL.loadEventStart)}return dK+dJ}function cr(dJ){return e(dJ,"ignore_referrer")==="1"||e(dJ,"ignore_referer")==="1"}function dz(){var dT,dM=new Date(),dN=Math.round(dM.getTime()/1000),dY,dL,dO=1024,dV,dP,dK=a2("ses"),dS=a2("ref"),dR=aL(dK),dJ=bX(),dX=bf||bZ,dU,dQ,dW={};
dU=dJ[0];dQ=dJ[1];dY=dJ[2];dL=dJ[3];if(!cr(dX)&&!dR){if(!bI||!dU.length){for(dT in cH){if(Object.prototype.hasOwnProperty.call(cH,dT)){dU=e(dX,cH[dT]);if(dU.length){break}}}for(dT in bT){if(Object.prototype.hasOwnProperty.call(bT,dT)){dQ=e(dX,bT[dT]);if(dQ.length){break}}}}dV=d(bA);dP=dL.length?d(dL):"";if(dV.length&&!a6(dV)&&!cJ(bA)&&(!bI||!dP.length||a6(dP)||cJ(dL))){dL=bA}if(dL.length||dU.length){dY=dN;dJ=[dU,dQ,dY,cf(dL.slice(0,dO))];dE(dS,X.JSON.stringify(dJ),dv,bC,dm,b5,aR)}}if(dU.length){dW._rcn=u(dU)}if(dQ.length){dW._rck=u(dQ)}dW._refts=dY;if(String(dL).length){dW._ref=u(cf(dL.slice(0,dO)))}return dW}function cL(dK,dW,dX){var dV,dJ=new Date(),dU=aZ,dQ=a2("cvar"),dZ=bf||bZ,dL=cr(dZ);if(bx){aN()}if(dd){return""}var dY=new RegExp("^file://","i");if(!cV&&(X.location.protocol==="file:"||dY.test(dZ))){return""}c5();var dR=a9();var dO=K.characterSet||K.charset;if(!dO||dO.toLowerCase()==="utf-8"){dO=null}dK+="&idsite="+cj+"&rec=1&r="+String(Math.random()).slice(2,8)+"&h="+dJ.getHours()+"&m="+dJ.getMinutes()+"&s="+dJ.getSeconds()+"&url="+u(cf(dZ))+(bA.length&&!cJ(bA)&&!dL?"&urlref="+u(cf(bA)):"")+(ad(bL)?"&uid="+u(bL):"")+"&_id="+dR.uuid+"&_idn="+dR.newVisitor+(dO?"&cs="+u(dO):"")+"&send_image=0";
var dT=dz();for(dV in dT){if(Object.prototype.hasOwnProperty.call(dT,dV)){dK+="&"+dV+"="+dT[dV]}}var d1=[];if(dW){for(dV in dW){if(Object.prototype.hasOwnProperty.call(dW,dV)&&/^dimension\d+$/.test(dV)){var dM=dV.replace("dimension","");d1.push(parseInt(dM,10));d1.push(String(dM));dK+="&"+dV+"="+u(dW[dV]);delete dW[dV]}}}if(dW&&E(dW)){dW=null}for(dV in cN){if(Object.prototype.hasOwnProperty.call(cN,dV)){dK+="&"+dV+"="+u(cN[dV])}}for(dV in bz){if(Object.prototype.hasOwnProperty.call(bz,dV)){var dP=(-1===Q(d1,dV));if(dP){dK+="&dimension"+dV+"="+u(bz[dV])}}}if(dW){dK+="&data="+u(X.JSON.stringify(dW))}else{if(aw){dK+="&data="+u(X.JSON.stringify(aw))}}function dN(d2,d3){var d4=X.JSON.stringify(d2);if(d4.length>2){return"&"+d3+"="+u(d4)}return""}var d0=dI(b9);var dS=dI(cC);dK+=dN(d0,"cvar");dK+=dN(dS,"e_cvar");if(aZ){dK+=dN(aZ,"_cvar");for(dV in dU){if(Object.prototype.hasOwnProperty.call(dU,dV)){if(aZ[dV][0]===""||aZ[dV][1]===""){delete aZ[dV]}}}if(b3){dE(dQ,X.JSON.stringify(aZ),cE,bC,dm,b5,aR)
}}if(bd&&bR&&!bs){dK=aI(dK);bs=true}if(aU){dK+="&pv_id="+aU}aV(dR);cs();dK+=ah(dX,{tracker:bV,request:dK});if(dp.length){dK+="&"+dp}if(au()){dK+="&tracker_install_check="+q}if(D(cq)){dK=cq(dK)}return dK}b4=function bi(){var dJ=new Date();dJ=dJ.getTime();if(!dn){return false}if(dn+bg<=dJ){bV.ping();return true}return false};function bD(dM,dL,dQ,dN,dJ,dT){var dP="idgoal=0",dK=new Date(),dR=[],dS,dO=String(dM).length;if(dO){dP+="&ec_id="+u(dM)}dP+="&revenue="+dL;if(String(dQ).length){dP+="&ec_st="+dQ}if(String(dN).length){dP+="&ec_tx="+dN}if(String(dJ).length){dP+="&ec_sh="+dJ}if(String(dT).length){dP+="&ec_dt="+dT}if(dq){for(dS in dq){if(Object.prototype.hasOwnProperty.call(dq,dS)){if(!N(dq[dS][1])){dq[dS][1]=""}if(!N(dq[dS][2])){dq[dS][2]=""}if(!N(dq[dS][3])||String(dq[dS][3]).length===0){dq[dS][3]=0}if(!N(dq[dS][4])||String(dq[dS][4]).length===0){dq[dS][4]=1}dR.push(dq[dS])}}dP+="&ec_items="+u(X.JSON.stringify(dR))}dP=cL(dP,aw,"ecommerce");bS(dP,bW);if(dO){dq={}}}function cb(dJ,dN,dM,dL,dK,dO){if(String(dJ).length&&N(dN)){bD(dJ,dN,dM,dL,dK,dO)
}}function bF(dJ){if(N(dJ)){bD("",dJ,"","","","")}}function cd(dK,dM,dL){if(!bN){aU=br()}var dJ=cL("action_name="+u(aq(dK||bu)),dM,"log");if(bd&&!bs){dJ=aI(dJ)}bS(dJ,bW,dL)}function bb(dL,dK){var dM,dJ="(^| )(piwik[_-]"+dK+"|matomo[_-]"+dK;if(dL){for(dM=0;dM<dL.length;dM++){dJ+="|"+dL[dM]}}dJ+=")( |$)";return new RegExp(dJ)}function a4(dJ){return(aM&&dJ&&0===String(dJ).indexOf(aM))}function cP(dN,dJ,dO,dK){if(a4(dJ)){return 0}var dM=bb(bY,"download"),dL=bb(bj,"link"),dP=new RegExp("\\.("+dw.join("|")+")([?&#]|$)","i");if(dL.test(dN)){return"link"}if(dK||dM.test(dN)||dP.test(dJ)){return"download"}if(dO){return 0}return"link"}function aC(dK){var dJ;dJ=dK.parentNode;while(dJ!==null&&N(dJ)){if(aj.isLinkElement(dK)){break}dK=dJ;dJ=dK.parentNode}return dK}function dC(dO){dO=aC(dO);if(!aj.hasNodeAttribute(dO,"href")){return}if(!N(dO.href)){return}var dN=aj.getAttributeValueFromNode(dO,"href");var dK=dO.pathname||cB(dO.href);var dP=dO.hostname||d(dO.href);var dQ=dP.toLowerCase();var dL=dO.href.replace(dP,dQ);
var dM=new RegExp("^(javascript|vbscript|jscript|mocha|livescript|ecmascript|mailto|tel):","i");if(!dM.test(dL)){var dJ=cP(dO.className,dL,aA(dQ,dK),aj.hasNodeAttribute(dO,"download"));if(dJ){return{type:dJ,href:dL}}}}function aY(dJ,dK,dL,dM){var dN=x.buildInteractionRequestParams(dJ,dK,dL,dM);if(!dN){return}return cL(dN,null,"contentInteraction")}function bm(dJ,dK){if(!dJ||!dK){return false}var dL=x.findTargetNode(dJ);if(x.shouldIgnoreInteraction(dL)){return false}dL=x.findTargetNodeNoDefault(dJ);if(dL&&!Z(dL,dK)){return false}return true}function cO(dL,dK,dN){if(!dL){return}var dJ=x.findParentContentNode(dL);if(!dJ){return}if(!bm(dJ,dL)){return}var dM=x.buildContentBlock(dJ);if(!dM){return}if(!dM.target&&dN){dM.target=dN}return x.buildInteractionRequestParams(dK,dM.name,dM.piece,dM.target)}function a7(dK){if(!cp||!cp.length){return false}var dJ,dL;for(dJ=0;dJ<cp.length;dJ++){dL=cp[dJ];if(dL&&dL.name===dK.name&&dL.piece===dK.piece&&dL.target===dK.target){return true}}return false}function a8(dJ){return function(dN){if(!dJ){return
}var dL=x.findParentContentNode(dJ);var dK;if(dN){dK=dN.target||dN.srcElement}if(!dK){dK=dJ}if(!bm(dL,dK)){return}if(!dL){return false}var dO=x.findTargetNode(dL);if(!dO||x.shouldIgnoreInteraction(dO)){return false}var dM=dC(dO);if(dy&&dM&&dM.type){return dM.type}return bV.trackContentInteractionNode(dK,"click")}}function ce(dL){if(!dL||!dL.length){return}var dJ,dK;for(dJ=0;dJ<dL.length;dJ++){dK=x.findTargetNode(dL[dJ]);if(dK&&!dK.contentInteractionTrackingSetupDone){dK.contentInteractionTrackingSetupDone=true;at(dK,"click",a8(dK))}}}function bK(dL,dM){if(!dL||!dL.length){return[]}var dJ,dK;for(dJ=0;dJ<dL.length;dJ++){if(a7(dL[dJ])){dL.splice(dJ,1);dJ--}else{cp.push(dL[dJ])}}if(!dL||!dL.length){return[]}ce(dM);var dN=[];for(dJ=0;dJ<dL.length;dJ++){dK=cL(x.buildImpressionRequestParams(dL[dJ].name,dL[dJ].piece,dL[dJ].target),undefined,"contentImpressions");if(dK){dN.push(dK)}}return dN}function cW(dK){var dJ=x.collectContent(dK);return bK(dJ,dK)}function bk(dK){if(!dK||!dK.length){return[]
}var dJ;for(dJ=0;dJ<dK.length;dJ++){if(!x.isNodeVisible(dK[dJ])){dK.splice(dJ,1);dJ--}}if(!dK||!dK.length){return[]}return cW(dK)}function aO(dL,dJ,dK){var dM=x.buildImpressionRequestParams(dL,dJ,dK);return cL(dM,null,"contentImpression")}function dB(dM,dK){if(!dM){return}var dJ=x.findParentContentNode(dM);var dL=x.buildContentBlock(dJ);if(!dL){return}if(!dK){dK="Unknown"}return aY(dK,dL.name,dL.piece,dL.target)}function dc(dK,dM,dJ,dL){return"e_c="+u(dK)+"&e_a="+u(dM)+(N(dJ)?"&e_n="+u(dJ):"")+(N(dL)?"&e_v="+u(dL):"")+"&ca=1"}function aB(dL,dN,dJ,dM,dP,dO){if(!ad(dL)||!ad(dN)){ap("Error while logging event: Parameters `category` and `action` must not be empty or filled with whitespaces");return false}var dK=cL(dc(dL,dN,dJ,dM),dP,"event");bS(dK,bW,dO)}function cm(dJ,dM,dK,dN){var dL=cL("search="+u(dJ)+(dM?"&search_cat="+u(dM):"")+(N(dK)?"&search_count="+dK:""),dN,"sitesearch");bS(dL,bW)}function dg(dJ,dN,dM,dL){var dK=cL("idgoal="+dJ+(dN?"&revenue="+dN:""),dM,"goal");bS(dK,bW,dL)}function dr(dM,dJ,dQ,dP,dL){var dO=dJ+"="+u(cf(dM));
var dK=cO(dL,"click",dM);if(dK){dO+="&"+dK}var dN=cL(dO,dQ,"link");bS(dN,bW,dP)}function b7(dK,dJ){if(dK!==""){return dK+dJ.charAt(0).toUpperCase()+dJ.slice(1)}return dJ}function cw(dO){var dN,dJ,dM=["","webkit","ms","moz"],dL;if(!bp){for(dJ=0;dJ<dM.length;dJ++){dL=dM[dJ];if(Object.prototype.hasOwnProperty.call(K,b7(dL,"hidden"))){if(K[b7(dL,"visibilityState")]==="prerender"){dN=true}break}}}if(dN){at(K,dL+"visibilitychange",function dK(){K.removeEventListener(dL+"visibilitychange",dK,false);dO()});return}dO()}function bE(){var dK=bV.getVisitorId();var dJ=aS();return dK+dJ}function cz(dJ){if(!dJ){return}if(!aj.hasNodeAttribute(dJ,"href")){return}var dK=aj.getAttributeValueFromNode(dJ,"href");if(!dK||a4(dK)){return}if(!bV.getVisitorId()){return}dK=j(dK,aD);var dL=bE();dK=J(dK,aD,dL);aj.setAnyAttribute(dJ,"href",dK)}function bw(dM){var dN=aj.getAttributeValueFromNode(dM,"href");if(!dN){return false}dN=String(dN);var dK=dN.indexOf("//")===0||dN.indexOf("http://")===0||dN.indexOf("https://")===0;
if(!dK){return false}var dJ=dM.pathname||cB(dM.href);var dL=(dM.hostname||d(dM.href)).toLowerCase();if(aA(dL,dJ)){if(!c4(dh,P(dL))){return true}return false}return false}function c3(dJ){var dK=dC(dJ);if(dK&&dK.type){dK.href=p(dK.href);dr(dK.href,dK.type,undefined,null,dJ);return}if(da){dJ=aC(dJ);if(bw(dJ)){cz(dJ)}}}function cQ(){return K.all&&!K.addEventListener}function di(dJ){var dL=dJ.which;var dK=(typeof dJ.button);if(!dL&&dK!=="undefined"){if(cQ()){if(dJ.button&1){dL=1}else{if(dJ.button&2){dL=3}else{if(dJ.button&4){dL=2}}}}else{if(dJ.button===0||dJ.button==="0"){dL=1}else{if(dJ.button&1){dL=2}else{if(dJ.button&2){dL=3}}}}}return dL}function b6(dJ){switch(di(dJ)){case 1:return"left";case 2:return"middle";case 3:return"right"}}function bc(dJ){return dJ.target||dJ.srcElement}function dj(dJ){return dJ==="A"||dJ==="AREA"}function aK(dJ){function dK(dM){var dN=bc(dM);var dO=dN.nodeName;var dL=bb(bM,"ignore");while(!dj(dO)&&dN&&dN.parentNode){dN=dN.parentNode;dO=dN.nodeName}if(dN&&dj(dO)&&!dL.test(dN.className)){return dN
}}return function(dN){dN=dN||X.event;var dO=dK(dN);if(!dO){return}var dM=b6(dN);if(dN.type==="click"){var dL=false;if(dJ&&dM==="middle"){dL=true}if(dO&&!dL){c3(dO)}}else{if(dN.type==="mousedown"){if(dM==="middle"&&dO){a0=dM;bO=dO}else{a0=bO=null}}else{if(dN.type==="mouseup"){if(dM===a0&&dO===bO){c3(dO)}a0=bO=null}else{if(dN.type==="contextmenu"){c3(dO)}}}}}}function az(dM,dL,dJ){var dK=typeof dL;if(dK==="undefined"){dL=true}at(dM,"click",aK(dL),dJ);if(dL){at(dM,"mouseup",aK(dL),dJ);at(dM,"mousedown",aK(dL),dJ);at(dM,"contextmenu",aK(dL),dJ)}}function a1(dK,dN,dO){if(cu){return true}cu=true;var dP=false;var dM,dL;function dJ(){dP=true}n(function(){function dQ(dS){setTimeout(function(){if(!cu){return}dP=false;dO.trackVisibleContentImpressions();dQ(dS)},dS)}function dR(dS){setTimeout(function(){if(!cu){return}if(dP){dP=false;dO.trackVisibleContentImpressions()}dR(dS)},dS)}if(dK){dM=["scroll","resize"];for(dL=0;dL<dM.length;dL++){if(K.addEventListener){K.addEventListener(dM[dL],dJ,false)}else{X.attachEvent("on"+dM[dL],dJ)
}}dR(100)}if(dN&&dN>0){dN=parseInt(dN,10);dQ(dN)}})}var bQ={enabled:true,requests:[],timeout:null,interval:2500,sendRequests:function(){var dJ=this.requests;this.requests=[];if(dJ.length===1){bS(dJ[0],bW)}else{dF(dJ,bW)}},canQueue:function(){return !m&&this.enabled},pushMultiple:function(dK){if(!this.canQueue()){dF(dK,bW);return}var dJ;for(dJ=0;dJ<dK.length;dJ++){this.push(dK[dJ])}},push:function(dJ){if(!dJ){return}if(!this.canQueue()){bS(dJ,bW);return}bQ.requests.push(dJ);if(this.timeout){clearTimeout(this.timeout);this.timeout=null}this.timeout=setTimeout(function(){bQ.timeout=null;bQ.sendRequests()},bQ.interval);var dK="RequestQueue"+aF;if(!Object.prototype.hasOwnProperty.call(b,dK)){b[dK]={unload:function(){if(bQ.timeout){clearTimeout(bQ.timeout)}bQ.sendRequests()}}}}};bt();this.hasConsent=function(){return bP};this.getVisitorInfo=function(){if(!aL(a2("id"))){aV()}return db()};this.getVisitorId=function(){return this.getVisitorInfo()[1]};this.getAttributionInfo=function(){return bX()
};this.getAttributionCampaignName=function(){return bX()[0]};this.getAttributionCampaignKeyword=function(){return bX()[1]};this.getAttributionReferrerTimestamp=function(){return bX()[2]};this.getAttributionReferrerUrl=function(){return bX()[3]};this.setTrackerUrl=function(dJ){aM=dJ};this.getTrackerUrl=function(){return aM};this.getMatomoUrl=function(){return ab(this.getTrackerUrl(),bU)};this.getPiwikUrl=function(){return this.getMatomoUrl()};this.addTracker=function(dL,dK){if(!N(dL)||null===dL){dL=this.getTrackerUrl()}var dJ=new U(dL,dK);M.push(dJ);v.trigger("TrackerAdded",[this]);return dJ};this.getSiteId=function(){return cj};this.setSiteId=function(dJ){cg(dJ)};this.resetUserId=function(){bL=""};this.setUserId=function(dJ){if(ad(dJ)){bL=dJ}};this.setVisitorId=function(dK){var dJ=/[0-9A-Fa-f]{16}/g;if(y(dK)&&dJ.test(dK)){b0=dK}else{ap("Invalid visitorId set"+dK)}};this.getUserId=function(){return bL};this.setCustomData=function(dJ,dK){if(aa(dJ)){aw=dJ}else{if(!aw){aw={}}aw[dJ]=dK}};this.getCustomData=function(){return aw
};this.setCustomRequestProcessing=function(dJ){cq=dJ};this.appendToTrackingUrl=function(dJ){dp=dJ};this.getRequest=function(dJ){return cL(dJ)};this.addPlugin=function(dJ,dK){b[dJ]=dK};this.setCustomDimension=function(dJ,dK){dJ=parseInt(dJ,10);if(dJ>0){if(!N(dK)){dK=""}if(!y(dK)){dK=String(dK)}bz[dJ]=dK}};this.getCustomDimension=function(dJ){dJ=parseInt(dJ,10);if(dJ>0&&Object.prototype.hasOwnProperty.call(bz,dJ)){return bz[dJ]}};this.deleteCustomDimension=function(dJ){dJ=parseInt(dJ,10);if(dJ>0){delete bz[dJ]}};this.setCustomVariable=function(dK,dJ,dN,dL){var dM;if(!N(dL)){dL="visit"}if(!N(dJ)){return}if(!N(dN)){dN=""}if(dK>0){dJ=!y(dJ)?String(dJ):dJ;dN=!y(dN)?String(dN):dN;dM=[dJ.slice(0,bG),dN.slice(0,bG)];if(dL==="visit"||dL===2){c2();aZ[dK]=dM}else{if(dL==="page"||dL===3){b9[dK]=dM}else{if(dL==="event"){cC[dK]=dM}}}}};this.getCustomVariable=function(dK,dL){var dJ;if(!N(dL)){dL="visit"}if(dL==="page"||dL===3){dJ=b9[dK]}else{if(dL==="event"){dJ=cC[dK]}else{if(dL==="visit"||dL===2){c2();
dJ=aZ[dK]}}}if(!N(dJ)||(dJ&&dJ[0]==="")){return false}return dJ};this.deleteCustomVariable=function(dJ,dK){if(this.getCustomVariable(dJ,dK)){this.setCustomVariable(dJ,"","",dK)}};this.deleteCustomVariables=function(dJ){if(dJ==="page"||dJ===3){b9={}}else{if(dJ==="event"){cC={}}else{if(dJ==="visit"||dJ===2){aZ={}}}}};this.storeCustomVariablesInCookie=function(){b3=true};this.setLinkTrackingTimer=function(dJ){bW=dJ};this.getLinkTrackingTimer=function(){return bW};this.setDownloadExtensions=function(dJ){if(y(dJ)){dJ=dJ.split("|")}dw=dJ};this.addDownloadExtensions=function(dK){var dJ;if(y(dK)){dK=dK.split("|")}for(dJ=0;dJ<dK.length;dJ++){dw.push(dK[dJ])}};this.removeDownloadExtensions=function(dL){var dK,dJ=[];if(y(dL)){dL=dL.split("|")}for(dK=0;dK<dw.length;dK++){if(Q(dL,dw[dK])===-1){dJ.push(dw[dK])}}dw=dJ};this.setDomains=function(dJ){aG=y(dJ)?[dJ]:dJ;var dN=false,dL=0,dK;for(dL;dL<aG.length;dL++){dK=String(aG[dL]);if(c4(dh,P(dK))){dN=true;break}var dM=cB(dK);if(dM&&dM!=="/"&&dM!=="/*"){dN=true;
break}}if(!dN){aG.push(dh)}};this.setExcludedReferrers=function(dJ){cS=y(dJ)?[dJ]:dJ};this.enableCrossDomainLinking=function(){da=true};this.disableCrossDomainLinking=function(){da=false};this.isCrossDomainLinkingEnabled=function(){return da};this.setCrossDomainLinkingTimeout=function(dJ){ba=dJ};this.getCrossDomainLinkingUrlParameter=function(){return u(aD)+"="+u(bE())};this.setIgnoreClasses=function(dJ){bM=y(dJ)?[dJ]:dJ};this.setRequestMethod=function(dJ){if(dJ){dA=String(dJ).toUpperCase()}else{dA=cx}if(dA==="GET"){this.disableAlwaysUseSendBeacon()}};this.setRequestContentType=function(dJ){cR=dJ||aQ};this.setGenerationTimeMs=function(dJ){ap("setGenerationTimeMs is no longer supported since Matomo 4. The call will be ignored. The replacement is setPagePerformanceTiming.")};this.setPagePerformanceTiming=function(dN,dP,dO,dK,dQ,dL){var dM={pf_net:dN,pf_srv:dP,pf_tfr:dO,pf_dm1:dK,pf_dm2:dQ,pf_onl:dL};try{dM=R(dM,N);dM=C(dM);cD=l(dM);if(cD===""){ap("setPagePerformanceTiming() called without parameters. This function needs to be called with at least one performance parameter.");
return}bs=false;bR=true}catch(dJ){ap("setPagePerformanceTiming: "+dJ.toString())}};this.setReferrerUrl=function(dJ){bA=dJ};this.setCustomUrl=function(dJ){bf=b8(bZ,dJ)};this.getCurrentUrl=function(){return bf||bZ};this.setDocumentTitle=function(dJ){bu=dJ};this.setPageViewId=function(dJ){aU=dJ;bN=true};this.getPageViewId=function(){return aU};this.setAPIUrl=function(dJ){bU=dJ};this.setDownloadClasses=function(dJ){bY=y(dJ)?[dJ]:dJ};this.setLinkClasses=function(dJ){bj=y(dJ)?[dJ]:dJ};this.setCampaignNameKey=function(dJ){cH=y(dJ)?[dJ]:dJ};this.setCampaignKeywordKey=function(dJ){bT=y(dJ)?[dJ]:dJ};this.discardHashTag=function(dJ){b1=dJ};this.setCookieNamePrefix=function(dJ){bv=dJ;if(aZ){aZ=ca()}};this.setCookieDomain=function(dJ){var dK=P(dJ);if(!bx&&!bJ(dK)){ap("Can't write cookie on domain "+dJ)}else{dm=dK;bt()}};this.setExcludedQueryParams=function(dJ){cy=y(dJ)?[dJ]:dJ};this.getCookieDomain=function(){return dm};this.hasCookies=function(){return"1"===ci()};this.setSessionCookie=function(dL,dK,dJ){if(!dL){throw new Error("Missing cookie name")
}if(!N(dJ)){dJ=cE}bH.push(dL);dE(a2(dL),dK,dJ,bC,dm,b5,aR)};this.getCookie=function(dK){var dJ=aL(a2(dK));if(dJ===0){return null}return dJ};this.setCookiePath=function(dJ){bC=dJ;bt()};this.getCookiePath=function(){return bC};this.setVisitorCookieTimeout=function(dJ){c7=dJ*1000};this.setSessionCookieTimeout=function(dJ){cE=dJ*1000};this.getSessionCookieTimeout=function(){return cE};this.setReferralCookieTimeout=function(dJ){dv=dJ*1000};this.setConversionAttributionFirstReferrer=function(dJ){bI=dJ};this.setSecureCookie=function(dJ){if(dJ&&location.protocol!=="https:"){ap("Error in setSecureCookie: You cannot use `Secure` on http.");return}b5=dJ};this.setCookieSameSite=function(dJ){dJ=String(dJ);dJ=dJ.charAt(0).toUpperCase()+dJ.toLowerCase().slice(1);if(dJ!=="None"&&dJ!=="Lax"&&dJ!=="Strict"){ap("Ignored value for sameSite. Please use either Lax, None, or Strict.");return}if(dJ==="None"){if(location.protocol==="https:"){this.setSecureCookie(true)}else{ap("sameSite=None cannot be used on http, reverted to sameSite=Lax.");
dJ="Lax"}}aR=dJ};this.disableCookies=function(){bx=true;if(cj){aN()}};this.areCookiesEnabled=function(){return !bx};this.setCookieConsentGiven=function(){if(bx&&!dd){bx=false;if(!dl){this.enableBrowserFeatureDetection()}if(cj&&aE){aV();var dJ=cL("ping=1",null,"ping");bS(dJ,bW)}}};this.requireCookieConsent=function(){if(this.getRememberedCookieConsent()){return false}this.disableCookies();return true};this.getRememberedCookieConsent=function(){return aL(c0)};this.forgetCookieConsentGiven=function(){cc(c0,bC,dm);this.disableCookies()};this.rememberCookieConsentGiven=function(dK){if(dK){dK=dK*60*60*1000}else{dK=30*365*24*60*60*1000}this.setCookieConsentGiven();var dJ=new Date().getTime();dE(c0,dJ,dK,bC,dm,b5,aR)};this.deleteCookies=function(){aN()};this.setDoNotTrack=function(dK){var dJ=g.doNotTrack||g.msDoNotTrack;dd=dK&&(dJ==="yes"||dJ==="1");if(dd){this.disableCookies()}};this.alwaysUseSendBeacon=function(){dk=true};this.disableAlwaysUseSendBeacon=function(){dk=false};this.addListener=function(dK,dJ){az(dK,dJ,false)
};this.enableLinkTracking=function(dK){if(dy){return}dy=true;var dJ=this;r(function(){ax=true;var dL=K.body;az(dL,dK,true)})};this.enableJSErrorTracking=function(){if(df){return}df=true;var dJ=X.onerror;X.onerror=function(dO,dM,dL,dN,dK){cw(function(){var dP="JavaScript Errors";var dQ=dM+":"+dL;if(dN){dQ+=":"+dN}if(Q(cM,dP+dQ+dO)===-1){cM.push(dP+dQ+dO);aB(dP,dQ,dO)}});if(dJ){return dJ(dO,dM,dL,dN,dK)}return false}};this.disablePerformanceTracking=function(){bd=false};this.enableHeartBeatTimer=function(dJ){dJ=Math.max(dJ||15,5);bg=dJ*1000;if(dn!==null){dH()}};this.disableHeartBeatTimer=function(){if(bg||aW){if(X.removeEventListener){X.removeEventListener("focus",bl);X.removeEventListener("blur",aH);X.removeEventListener("visibilitychange",a5)}else{if(X.detachEvent){X.detachEvent("onfocus",bl);X.detachEvent("onblur",aH);X.detachEvent("visibilitychange",a5)}}}bg=null;aW=false};this.killFrame=function(){if(X.location!==X.top.location){X.top.location=X.location}};this.redirectFile=function(dJ){if(X.location.protocol==="file:"){X.location=dJ
}};this.setCountPreRendered=function(dJ){bp=dJ};this.trackGoal=function(dJ,dM,dL,dK){cw(function(){dg(dJ,dM,dL,dK)})};this.trackLink=function(dK,dJ,dM,dL){cw(function(){dr(dK,dJ,dM,dL)})};this.getNumTrackedPageViews=function(){return cK};this.trackPageView=function(dJ,dL,dK){cp=[];c8=[];cM=[];if(S(cj)){cw(function(){ae(aM,bU,cj)})}else{cw(function(){cK++;cd(dJ,dL,dK)})}};this.disableBrowserFeatureDetection=function(){dl=false;dx={};if(av()){ay()}};this.enableBrowserFeatureDetection=function(){dl=true;c5()};this.trackAllContentImpressions=function(){if(S(cj)){return}cw(function(){r(function(){var dJ=x.findContentNodes();var dK=cW(dJ);bQ.pushMultiple(dK)})})};this.trackVisibleContentImpressions=function(dJ,dK){if(S(cj)){return}if(!N(dJ)){dJ=true}if(!N(dK)){dK=750}a1(dJ,dK,this);cw(function(){n(function(){var dL=x.findContentNodes();var dM=bk(dL);bQ.pushMultiple(dM)})})};this.trackContentImpression=function(dL,dJ,dK){if(S(cj)){return}dL=a(dL);dJ=a(dJ);dK=a(dK);if(!dL){return}dJ=dJ||"Unknown";
cw(function(){var dM=aO(dL,dJ,dK);bQ.push(dM)})};this.trackContentImpressionsWithinNode=function(dJ){if(S(cj)||!dJ){return}cw(function(){if(cu){n(function(){var dK=x.findContentNodesWithinNode(dJ);var dL=bk(dK);bQ.pushMultiple(dL)})}else{r(function(){var dK=x.findContentNodesWithinNode(dJ);var dL=cW(dK);bQ.pushMultiple(dL)})}})};this.trackContentInteraction=function(dL,dM,dJ,dK){if(S(cj)){return}dL=a(dL);dM=a(dM);dJ=a(dJ);dK=a(dK);if(!dL||!dM){return}dJ=dJ||"Unknown";cw(function(){var dN=aY(dL,dM,dJ,dK);if(dN){bQ.push(dN)}})};this.trackContentInteractionNode=function(dL,dK){if(S(cj)||!dL){return}var dJ=null;cw(function(){dJ=dB(dL,dK);if(dJ){bQ.push(dJ)}});return dJ};this.logAllContentBlocksOnPage=function(){var dL=x.findContentNodes();var dJ=x.collectContent(dL);var dK=typeof console;if(dK!=="undefined"&&console&&console.log){console.log(dJ)}};this.trackEvent=function(dK,dM,dJ,dL,dO,dN){cw(function(){aB(dK,dM,dJ,dL,dO,dN)})};this.trackSiteSearch=function(dJ,dL,dK,dM){cp=[];cw(function(){cm(dJ,dL,dK,dM)
})};this.setEcommerceView=function(dN,dJ,dL,dK){cN={};if(ad(dL)){dL=String(dL)}if(!N(dL)||dL===null||dL===false||!dL.length){dL=""}else{if(dL instanceof Array){dL=X.JSON.stringify(dL)}}var dM="_pkc";cN[dM]=dL;if(N(dK)&&dK!==null&&dK!==false&&String(dK).length){dM="_pkp";cN[dM]=dK}if(!ad(dN)&&!ad(dJ)){return}if(ad(dN)){dM="_pks";cN[dM]=dN}if(!ad(dJ)){dJ=""}dM="_pkn";cN[dM]=dJ};this.getEcommerceItems=function(){return JSON.parse(JSON.stringify(dq))};this.addEcommerceItem=function(dN,dJ,dL,dK,dM){if(ad(dN)){dq[dN]=[String(dN),dJ,dL,dK,dM]}};this.removeEcommerceItem=function(dJ){if(ad(dJ)){dJ=String(dJ);delete dq[dJ]}};this.clearEcommerceCart=function(){dq={}};this.trackEcommerceOrder=function(dJ,dN,dM,dL,dK,dO){cb(dJ,dN,dM,dL,dK,dO)};this.trackEcommerceCartUpdate=function(dJ){bF(dJ)};this.trackRequest=function(dK,dM,dL,dJ){cw(function(){var dN=cL(dK,dM,dJ);bS(dN,bW,dL)})};this.ping=function(){this.trackRequest("ping=1",null,null,"ping")};this.disableQueueRequest=function(){bQ.enabled=false
};this.setRequestQueueInterval=function(dJ){if(dJ<1000){throw new Error("Request queue interval needs to be at least 1000ms")}bQ.interval=dJ};this.queueRequest=function(dK,dJ){cw(function(){var dL=dJ?dK:cL(dK);bQ.push(dL)})};this.isConsentRequired=function(){return cX};this.getRememberedConsent=function(){var dJ=aL(bo);if(aL(c9)){if(dJ){cc(bo,bC,dm)}return null}if(!dJ||dJ===0){return null}return dJ};this.hasRememberedConsent=function(){return !!this.getRememberedConsent()};this.requireConsent=function(){cX=true;bP=this.hasRememberedConsent();if(!bP){bx=true}z++;b["CoreConsent"+z]={unload:function(){if(!bP){aN()}}}};this.setConsentGiven=function(dK){bP=true;if(!dl){this.enableBrowserFeatureDetection()}cc(c9,bC,dm);var dL,dJ;for(dL=0;dL<c8.length;dL++){dJ=typeof c8[dL][0];if(dJ==="string"){bS(c8[dL][0],bW,c8[dL][1])}else{if(dJ==="object"){dF(c8[dL][0],bW)}}}c8=[];if(!N(dK)||dK){this.setCookieConsentGiven()}};this.rememberConsentGiven=function(dL){if(dL){dL=dL*60*60*1000}else{dL=30*365*24*60*60*1000
}var dJ=true;this.setConsentGiven(dJ);var dK=new Date().getTime();dE(bo,dK,dL,bC,dm,b5,aR)};this.forgetConsentGiven=function(dJ){if(dJ){dJ=dJ*60*60*1000}else{dJ=30*365*24*60*60*1000}cc(bo,bC,dm);dE(c9,new Date().getTime(),dJ,bC,dm,b5,aR);this.forgetCookieConsentGiven();this.requireConsent()};this.isUserOptedOut=function(){return !bP};this.optUserOut=this.forgetConsentGiven;this.forgetUserOptOut=function(){this.setConsentGiven(false)};this.enableFileTracking=function(){cV=true};n(function(){setTimeout(function(){bR=true},0)});v.trigger("TrackerSetup",[this]);v.addPlugin("TrackerVisitorIdCookie"+aF,{unload:function(){if(av()&&!by){by=true;ay()}if(!aE){aV();dz()}}})}function L(){return{push:ak}}function c(az,ay){var aA={};var aw,ax;for(aw=0;aw<ay.length;aw++){var au=ay[aw];aA[au]=1;for(ax=0;ax<az.length;ax++){if(az[ax]&&az[ax][0]){var av=az[ax][0];if(au===av){ak(az[ax]);delete az[ax];if(aA[av]>1&&av!=="addTracker"&&av!=="enableLinkTracking"){ap("The method "+av+' is registered more than once in "_paq" variable. Only the last call has an effect. Please have a look at the multiple Matomo trackers documentation: https://developer.matomo.org/guides/tracking-javascript-guide#multiple-piwik-trackers')
}aA[av]++}}}}return az}var F=["addTracker","enableFileTracking","forgetCookieConsentGiven","requireCookieConsent","disableBrowserFeatureDetection","disableCookies","setTrackerUrl","setAPIUrl","enableCrossDomainLinking","setCrossDomainLinkingTimeout","setSessionCookieTimeout","setVisitorCookieTimeout","setCookieNamePrefix","setCookieSameSite","setSecureCookie","setCookiePath","setCookieDomain","setDomains","setUserId","setVisitorId","setSiteId","alwaysUseSendBeacon","disableAlwaysUseSendBeacon","enableLinkTracking","setCookieConsentGiven","requireConsent","setConsentGiven","disablePerformanceTracking","setPagePerformanceTiming","setExcludedQueryParams","setExcludedReferrers"];function ai(aw,av){var au=new U(aw,av);M.push(au);_paq=c(_paq,F);for(I=0;I<_paq.length;I++){if(_paq[I]){ak(_paq[I])}}_paq=new L();v.trigger("TrackerAdded",[au]);return au}at(X,"beforeunload",an,false);at(X,"visibilitychange",function(){if(m){return}if(K.visibilityState==="hidden"){ah("unload")}},false);at(X,"online",function(){if(N(g.serviceWorker)){g.serviceWorker.ready.then(function(au){if(au&&au.sync){return au.sync.register("matomoSync")
}},function(){})}},false);at(X,"message",function(az){if(!az||!az.origin){return}var aB,ax,av;var aC=d(az.origin);var ay=v.getAsyncTrackers();for(ax=0;ax<ay.length;ax++){av=d(ay[ax].getMatomoUrl());if(av===aC){aB=ay[ax];break}}if(!aB){return}var aw=null;try{aw=JSON.parse(az.data)}catch(aA){return}if(!aw){return}function au(aF){var aH=K.getElementsByTagName("iframe");for(ax=0;ax<aH.length;ax++){var aG=aH[ax];var aD=d(aG.src);if(aG.contentWindow&&N(aG.contentWindow.postMessage)&&aD===aC){var aE=JSON.stringify(aF);aG.contentWindow.postMessage(aE,az.origin)}}}if(N(aw.maq_initial_value)){au({maq_opted_in:aw.maq_initial_value&&aB.hasConsent(),maq_url:aB.getMatomoUrl(),maq_optout_by_default:aB.isConsentRequired()})}else{if(N(aw.maq_opted_in)){ay=v.getAsyncTrackers();for(ax=0;ax<ay.length;ax++){aB=ay[ax];if(aw.maq_opted_in){aB.rememberConsentGiven()}else{aB.forgetConsentGiven()}}au({maq_confirm_opted_in:aB.hasConsent(),maq_url:aB.getMatomoUrl(),maq_optout_by_default:aB.isConsentRequired()})}}},false);
Date.prototype.getTimeAlias=Date.prototype.getTime;v={initialized:false,JSON:X.JSON,DOM:{addEventListener:function(ax,aw,av,au){var ay=typeof au;if(ay==="undefined"){au=false}at(ax,aw,av,au)},onLoad:n,onReady:r,isNodeVisible:i,isOrWasNodeVisible:x.isNodeVisible},on:function(av,au){if(!A[av]){A[av]=[]}A[av].push(au)},off:function(aw,av){if(!A[aw]){return}var au=0;for(au;au<A[aw].length;au++){if(A[aw][au]===av){A[aw].splice(au,1)}}},trigger:function(aw,ax,av){if(!A[aw]){return}var au=0;for(au;au<A[aw].length;au++){A[aw][au].apply(av||X,ax)}},addPlugin:function(au,av){b[au]=av},getTracker:function(av,au){if(!N(au)){au=this.getAsyncTracker().getSiteId()}if(!N(av)){av=this.getAsyncTracker().getTrackerUrl()}return new U(av,au)},getAsyncTrackers:function(){return M},addTracker:function(aw,av){var au;if(!M.length){au=ai(aw,av)}else{au=M[0].addTracker(aw,av)}return au},getAsyncTracker:function(ay,ax){var aw;if(M&&M.length&&M[0]){aw=M[0]}else{return ai(ay,ax)}if(!ax&&!ay){return aw}if((!N(ax)||null===ax)&&aw){ax=aw.getSiteId()
}if((!N(ay)||null===ay)&&aw){ay=aw.getTrackerUrl()}var av,au=0;for(au;au<M.length;au++){av=M[au];if(av&&String(av.getSiteId())===String(ax)&&av.getTrackerUrl()===ay){return av}}},retryMissedPluginCalls:function(){var av=am;am=[];var au=0;for(au;au<av.length;au++){ak(av[au])}}};if(typeof define==="function"&&define.amd){define("piwik",[],function(){return v});define("matomo",[],function(){return v})}return v}())}
/*!!! pluginTrackerHook */

/* GENERATED: tracker.min.js */
/*!!
 * Copyright (C) InnoCraft Ltd - All rights reserved.
 *
 * All information contained herein is, and remains the property of InnoCraft Ltd.
 *
 * @link https://www.innocraft.com/
 * @license For license details see https://www.innocraft.com/license
 */
(function(){var x=new Date().getTime();var D=null;var U=false;var F=10;var O=false;var a=true;var A=null;var I=1000*60*60*3;var t=document;var G=window;var i=0;var o=0;var E=false;var d={play:50,pause:25,resume:25,finish:50,seek:50};var M=function(){return{play:0,pause:0,resume:0,finish:0,seek:0}};var m={play:50,pause:100,resume:100,finish:50,seek:100};var j=25;var y=true;var u=function(){return""};var K=[];function q(){if(typeof Piwik==="object"&&typeof Piwik.JSON==="object"){return Piwik.JSON}else{if(G.JSON&&G.JSON.parse&&G.JSON.stringify){return G.JSON}else{if(typeof G.JSON2==="object"&&G.JSON2.parse&&G.JSON2.stringify){return G.JSON2}else{return{parse:function(){return{}},stringify:function(){return""}}}}}}var e=true;function f(){if(U&&"undefined"!==typeof console&&console&&console.debug){console.debug.apply(console,arguments)
}}function v(W){return typeof W==="object"&&typeof W.length==="number"}function N(){return t.getElementById("engage_video")&&t.getElementById("videoDisplay1_wrapper")}function b(){return"function"===typeof jwplayer}function n(){return"function"===typeof flowplayer}function r(Y,X){if(!X.getMediaTitle()&&"function"===typeof u){var W=u(Y);if(W){X.setMediaTitle(W)}}}var g={AUDIO:"Audio",VIDEO:"Video"};var J={getLocation:function(){var W=this.location||G.location;if(!W.origin){W.origin=W.protocol+"//"+W.hostname+(W.port?":"+W.port:"")}return W},setLocation:function(W){this.location=W},makeUrlAbsolute:function(X){if((!X||String(X)!==X)&&X!==""){return X}if(X.indexOf("//")===0){return this.getLocation().protocol+X}if(X.indexOf("://")!==-1){return X}if(X.indexOf("/")===0){return this.getLocation().origin+X}if(X.indexOf("#")===0||X.indexOf("?")===0){return this.getLocation().origin+this.getLocation().pathname+X}if(""===X){return this.getLocation().href}var W="(.*/)";var Y=this.getLocation().origin+this.getLocation().pathname.match(new RegExp(W))[0];
return Y+X}};var T={getCurrentTime:function(){return new Date().getTime()},roundTimeToSeconds:function(W){return Math.round(W/1000)},isNumber:function(W){return !isNaN(W)},isArray:function(W){return typeof W==="object"&&W!==null&&typeof W.length==="number"},indexOfArray:function(Y,X){if(!Y){return -1}if(Y.indexOf){return Y.indexOf(X)}if(!this.isArray(Y)){return -1}for(var W=0;W<Y.length;W++){if(Y[W]===X){return W}}return -1},getTimeScriptLoaded:function(W){return x},generateUniqueId:function(){var Z="";var X="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";var Y=X.length;for(var W=0;W<6;W++){Z+=X.charAt(Math.floor(Math.random()*Y))}return Z},trim:function(W){if(W&&String(W)===W){return W.replace(/^\s+|\s+$/g,"")}return W},getQueryParameter:function(W,aa){var Z=new RegExp("[?&]"+aa+"(=([^&#]*)|&|#|$)");var Y=Z.exec(W);if(!Y){return null}if(!Y[2]){return""}var X=Y[2].replace(/\+/g," ");return decodeURIComponent(X)},isDocumentOffScreen:function(){return t&&"undefined"!==t.hidden&&t.hidden
},roundUp:function(X,W){if(X!==null&&X!==false&&this.isNumber(X)){return Math.ceil(X/W)*W}}};var p={getAttribute:function(X,W){if(X&&X.getAttribute&&W){return X.getAttribute(W)}return null},setAttribute:function(Y,W,X){if(Y&&Y.setAttribute){Y.setAttribute(W,X)}},isMediaIgnored:function(W){var X=p.getAttribute(W,"data-piwik-ignore");if(!!X||X===""){return true}X=p.getAttribute(W,"data-matomo-ignore");if(!!X||X===""){return true}return false},getMediaResource:function(W,X){var Y=p.getAttribute(W,"data-matomo-resource");if(Y){return Y}Y=p.getAttribute(W,"data-piwik-resource");if(Y){return Y}Y=p.getAttribute(W,"src");if(Y){return Y}return X},getMediaTitle:function(W){var X=p.getAttribute(W,"data-matomo-title");if(!X){X=p.getAttribute(W,"data-piwik-title")}if(!X){X=p.getAttribute(W,"title")}if(!X){X=p.getAttribute(W,"alt")}return X},hasCssClass:function(Y,Z){if(Y&&Y.className){var X=(""+Y.className).split(" ");for(var W=0;W<X.length;W++){if(X[W]===Z){return true}}}return false},getFirstParentWithClass:function(Y,Z,W){if(W<=0||!Y||!Y.parentNode){return null
}var X=Y.parentNode;if(this.hasCssClass(X,Z)){return X}else{return this.getFirstParentWithClass(X,Z,--W)}},isFullscreen:function(W){if(W&&t.fullScreenElement===W||t.mozFullScreenElement===W||t.webkitFullscreenElement===W||t.msFullscreenElement===W){return true}return false}};function V(){if(null===A){if("object"===typeof Piwik&&Piwik.getAsyncTrackers){return Piwik.getAsyncTrackers()}}if(v(A)){return A}return[]}function l(X,W,Y){this.playerName=X;this.type=W;this.resource=Y;this.disabled=false;this.reset()}l.piwikTrackers=[];l.prototype.disable=function(){this.disabled=true};l.prototype.reset=function(){this.id=T.generateUniqueId();this.mediaTitle=null;this.timeToInitialPlay=null;this.width=null;this.height=null;this.fullscreen=false;this.timeout=null;this.watchedTime=0;this.lastTimeCheck=null;this.isPlaying=false;this.isPaused=false;this.mediaProgressInSeconds=0;this.mediaLengthInSeconds=0;this.disabled=false;this.numPlaysSameMedia=0;this.numPlaysSameMediaOffScreen=0;this.viewedSegments=[];
this.trackedSegments=[];this.lastSentProgressRequestUrl=""};l.prototype.setResource=function(W){this.resource=W};l.prototype.getResource=function(){return this.resource};l.prototype.makeRequestUrlFromParams=function(Y){var X="";for(var W in Y){if(Object.prototype.hasOwnProperty.call(Y,W)){X+=W+"="+encodeURIComponent(Y[W])+"&"}}return X};l.prototype.trackEvent=function(ac){if(this.disabled){return}if(!D){D=T.getCurrentTime()}else{if((T.getCurrentTime()-D)>I){this.disable();return}}var W=V();var X="Media"+this.type;var Z=this.mediaTitle||this.resource;var aa=this.makeRequestUrlFromParams({e_c:X,e_a:ac,e_n:Z,e_v:parseInt(Math.round(this.mediaProgressInSeconds),10),ca:"1"});if(W&&W.length){var Y=0,ab;for(Y;Y<W.length;Y++){ab=W[Y];if(ab&&ab.MediaAnalytics&&ab.MediaAnalytics.isTrackEventsEnabled()){if(L.isEventsLimitReached(ab,Z,ac,this.mediaLengthInSeconds)){f("Event limit reached for event: "+ac);continue}if("function"===typeof ab.queueRequest&&"function"===typeof ab.disableQueueRequest){ab.queueRequest(aa)
}else{ab.trackRequest(aa)}L.incrLimitPerTrackerPerMediaResource(ab,Z,ac)}}}else{if(typeof G._paq==="undefined"){G._paq=[]}G._paq.push(["trackRequest",aa]);f("piwikWasNotYetInitialized. This means players were scanning too early for media or there are no async trackers")}f("trackEvent",X,Z,ac)};l.prototype.trackProgress=function(aa,ac,ab,X,Y,ae,an,af,ak,ag,ad,W,ai){if(this.disabled){return}if(!D){D=T.getCurrentTime()}else{if((T.getCurrentTime()-D)>I){this.disable();return}}if(this.isPlaying&&!ae){ae=1}var am={ma_id:aa,ma_ti:ac!==null?ac:"",ma_pn:ab,ma_mt:X,ma_re:Y,ma_st:parseInt(Math.floor(ae),10),ma_ps:parseInt(an,10),ma_le:af,ma_ttp:ak!==null?ak:"",ma_w:ag?ag:"",ma_h:ad?ad:"",ma_fs:W?"1":"0",ma_se:ai.join(","),ca:"1"};var ah=this.makeRequestUrlFromParams(am);if(ah===this.lastSentProgressRequestUrl){return}this.lastSentProgressRequestUrl=ah;var al=V();if(al&&al.length){var aj=0,Z;for(aj;aj<al.length;aj++){Z=al[aj];if(Z&&Z.MediaAnalytics&&Z.MediaAnalytics.isTrackProgressEnabled()){if("function"===typeof Z.queueRequest&&"function"===typeof Z.disableQueueRequest){Z.queueRequest(ah)
}else{Z.trackRequest(ah)}}}}else{if(typeof G._paq==="undefined"){G._paq=[]}G._paq.push(["trackRequest",ah]);f("piwikWasNotYetInitialized. This means players were scanning too early for media or there are no async trackers")}if(U){f("trackProgress",q().stringify(am))}};l.prototype.setFullscreen=function(W){if(!this.fullscreen){this.fullscreen=!!W}};l.prototype.setWidth=function(W){if(T.isNumber(W)){this.width=parseInt(W,10)}};l.prototype.setHeight=function(W){if(T.isNumber(W)){this.height=parseInt(W,10)}};l.prototype.setMediaTitle=function(W){this.mediaTitle=W};l.prototype.getMediaTitle=function(){return this.mediaTitle};l.prototype.setMediaProgressInSeconds=function(W){this.mediaProgressInSeconds=W;if(this.isPlaying){this.viewedSegments.push(W)}};l.prototype.getMediaProgressInSeconds=function(){return this.mediaProgressInSeconds};l.prototype.setMediaTotalLengthInSeconds=function(W){this.mediaLengthInSeconds=W};l.prototype.getMediaTotalLengthInSeconds=function(){return this.mediaLengthInSeconds
};l.prototype.play=function(){if(this.isPlaying){return}this.isPlaying=true;this.setMediaProgressInSeconds(this.getMediaProgressInSeconds());this.startWatchedTime();if(e&&this.timeToInitialPlay===null){this.timeToInitialPlay=T.roundTimeToSeconds(T.getCurrentTime()-T.getTimeScriptLoaded())}e=false;if(this.isPaused){this.isPaused=false;this.trackEvent("resume")}else{this.trackEvent("play");var W=T.isDocumentOffScreen();this.numPlaysSameMedia++;i++;if(W){this.numPlaysSameMediaOffScreen++;o++}if(this.numPlaysSameMedia>25||i>50){this.disable()}else{if(this.numPlaysSameMediaOffScreen>10||o>15){this.disable()}}}this.trackUpdate()};l.prototype.startWatchedTime=function(){this.lastTimeCheck=T.getCurrentTime()};l.prototype.stopWatchedTime=function(){if(this.lastTimeCheck){this.watchedTime+=T.getCurrentTime()-this.lastTimeCheck;this.lastTimeCheck=null}};l.prototype.seekStart=function(){if(this.isPlaying){this.stopWatchedTime()}};l.prototype.seekFinish=function(){if(this.isPlaying){this.startWatchedTime()
}};l.prototype.pause=function(){if(this.isPlaying){this.isPaused=true;this.isPlaying=false;if(this.timeout){clearTimeout(this.timeout);this.timeout=null}this.stopWatchedTime();this.trackUpdate();this.trackEvent("pause")}};l.prototype.finish=function(){if(this.timeout){clearTimeout(this.timeout);this.timeout=null}this.stopWatchedTime();this.trackUpdate();this.trackEvent("finish");this.id=T.generateUniqueId();this.timeToInitialPlay=null;this.lastTimeCheck=null;this.isPlaying=false;this.isPaused=false;this.watchedTime=0;this.mediaProgressInSeconds=0};l.prototype.trackUpdate=function(){if(this.timeout){clearTimeout(this.timeout);this.timeout=null}var W=T.getCurrentTime();if(this.lastTimeCheck){this.watchedTime+=(W-this.lastTimeCheck);this.lastTimeCheck=W}var ac=this.mediaLengthInSeconds;if(!ac||!T.isNumber(ac)){ac=""}else{ac=parseInt(this.mediaLengthInSeconds,10)}var Z=T.roundTimeToSeconds(this.watchedTime);var aa=this.mediaProgressInSeconds;if(aa>ac&&ac){aa=ac}var X=[];var Y,ab;for(Y=0;Y<this.viewedSegments.length;
Y++){ab=this.viewedSegments[Y];if(ab>=0&&ab<=ac){if(ab<=300){ab=T.roundUp(ab,15)}else{ab=T.roundUp(ab,30)}if(ab>=0&&ab<1){ab=15}if(-1===T.indexOfArray(X,ab)&&-1===T.indexOfArray(this.trackedSegments,ab)){X.push(ab);this.trackedSegments.push(ab)}}}this.viewedSegments=[];this.trackProgress(this.id,this.mediaTitle,this.playerName,this.type,this.resource,Z,aa,ac,this.timeToInitialPlay,this.width,this.height,this.fullscreen,X)};l.prototype.update=function(){if(this.timeout){return}var Y=T.roundTimeToSeconds(this.watchedTime);var X=F;if(!O&&(Y>=1800||i>10)){X=300}else{if(!O&&(Y>=600||i>4)){X=240}else{if(!O&&(Y>=300||i>2)){X=120}else{if(!O&&Y>=60){X=60}}}}X=X*1000;var W=this;this.timeout=setTimeout(function(){W.trackUpdate();W.timeout=null},X)};var L={isEventsLimitReached:function(Z,X,Y,W){if(!y){return false}if(L.getTotalEventsOnTracker(Z,Y)>=L.getTotalAllowedEventsPerTracker(Y)){f("blocked due to max tracker limit reached for action: "+Y);return true}var aa=(W&&W>900&&(Y==="pause"||Y==="resume"))?2:1;
L.initializeLimitPerTrackerPerMediaResource(Z,X,Y);return(Z.MediaAnalytics.quotaEventRequests[X][Y]>(d[Y]*aa))},getTotalEventsOnTracker:function(Z,Y){var X=0;if(typeof Z.MediaAnalytics.quotaEventRequests==="undefined"){Z.MediaAnalytics.quotaEventRequests={};return X}if(Object.keys(Z.MediaAnalytics.quotaEventRequests).length){for(var W in Z.MediaAnalytics.quotaEventRequests){X=X+(Z.MediaAnalytics.quotaEventRequests[W][Y]||0)}}return X},getTotalAllowedEventsPerTracker:function(W){return(m[W]||j)},initializeLimitPerTrackerPerMediaResource:function(Y,W,X){if(typeof Y.MediaAnalytics.quotaEventRequests==="undefined"){Y.MediaAnalytics.quotaEventRequests={}}if(typeof Y.MediaAnalytics.quotaEventRequests[W]==="undefined"){Y.MediaAnalytics.quotaEventRequests[W]=M()}if(typeof Y.MediaAnalytics.quotaEventRequests[W][X]==="undefined"){Y.MediaAnalytics.quotaEventRequests[W][X]=0}},incrLimitPerTrackerPerMediaResource:function(Y,W,X){if(!y){return}L.initializeLimitPerTrackerPerMediaResource(Y,W,X);Y.MediaAnalytics.quotaEventRequests[W][X]++
}};var c={players:{},registerPlayer:function(W,X){if(!X||!X.scanForMedia||"function"!==typeof X.scanForMedia){throw new Error("A registered player does not implement the scanForMedia function")}W=W.toLowerCase();this.players[W]=X},removePlayer:function(W){W=W.toLowerCase();delete this.players[W]},getPlayer:function(W){W=W.toLowerCase();if(W in this.players){return this.players[W]}return null},getPlayers:function(){return this.players},scanForMedia:function(X){if(!a){return}if("undefined"===typeof X||!X){X=document}var W;for(W in this.players){if(Object.prototype.hasOwnProperty.call(this.players,W)){this.players[W].scanForMedia(X)}}}};var S=function(ai,Y){if(!ai){return}if(!G.addEventListener){return}if(ai.hasPlayerInstance){return}ai.hasPlayerInstance=true;var an=g.VIDEO===Y;var ab=J.makeUrlAbsolute(ai.currentSrc);var W=p.getMediaResource(ai,ab);var ac="html5"+Y.toLowerCase();if(typeof paella==="object"&&typeof paella.opencast==="object"){ac="paella-opencast"}else{if(p.getFirstParentWithClass(ai,"video-js",1)){ac="video.js"
}else{if(p.hasCssClass(ai,"jw-video")){ac="jwplayer";var af=p.getFirstParentWithClass(ai,"jw-flag-media-audio");if(af){Y=g.AUDIO}}else{if(p.getFirstParentWithClass(ai,"flowplayer",3)){ac="flowplayer"}}}}var aa=new l(ac,Y,W);K.push(aa);function X(){if(ai.duration){aa.setMediaTotalLengthInSeconds(ai.duration)}}function ad(){if(an){var au=ai;if(ac==="jwplayer"){var at=p.getFirstParentWithClass(au,"jwplayer");if(at){au=at}}if("undefined"!==typeof au.videoWidth&&au.videoWidth){aa.setWidth(au.videoWidth)}else{if("undefined"!==typeof au.clientWidth&&au.clientWidth){aa.setWidth(au.clientWidth)}}if("undefined"!==typeof au.videoHeight&&au.videoHeight){aa.setHeight(au.videoHeight)}else{if("undefined"!==typeof au.clientHeight&&au.clientHeight){aa.setHeight(au.clientHeight)}}aa.setFullscreen(p.isFullscreen(au))}}function ae(){aa.setMediaProgressInSeconds(ai.currentTime)}function ao(){var at=p.getMediaTitle(ai);if(at){aa.setMediaTitle(at)}else{ak(ai,aa)}}ar(ai,aa);ad();ao();X();ae();var Z=false;var ag=false;
var ah=null;if(ai.currentSrc){ah=ai.currentSrc}function ak(av,az){if(b()&&!az.getMediaTitle()){var ax=p.getFirstParentWithClass(av,"jwplayer",3);if(!ax){ax=p.getFirstParentWithClass(av,"jwplayer-video",3);if(ax&&"undefined"!==typeof ax.children&&ax.children&&ax.children.length&&ax.children[0]){ax=ax.children[0]}}if(ax){try{var aA=jwplayer(ax);if(aA&&aA.getPlaylistItem){var aB=aA.getPlaylistItem();if(aB&&aB.matomoTitle){az.setMediaTitle(aB.matomoTitle)}else{if(aB&&aB.piwikTitle){az.setMediaTitle(aB.piwikTitle)}else{if(aB&&aB.title){az.setMediaTitle(aB.title)}}}}}catch(aw){f(aw)}}}if(n()&&!az.getMediaTitle()){var at=p.getFirstParentWithClass(av,"flowplayer",4);if(at){var aA=flowplayer(at);if(aA&&aA.video&&aA.video.matomoTitle){az.setMediaTitle(aA.video.matomoTitle)}else{if(aA&&aA.video&&aA.video.piwikTitle){az.setMediaTitle(aA.video.piwikTitle)}else{if(aA&&aA.video&&aA.video.title){az.setMediaTitle(aA.video.title)}else{if(aA&&aA.video&&aA.video.fv_title){az.setMediaTitle(aA.video.fv_title)
}}}}}}if(!az.getMediaTitle()){var au=t.getElementById("engage_basic_description_title");if(au&&au.innerText){var ay=T.trim(au.innerText);if(ay){az.setMediaTitle(ay)}}else{if(typeof paella==="object"&&typeof paella.opencast==="object"&&typeof paella.opencast._episode==="object"&&paella.opencast._episode.dcTitle){var ay=T.trim(paella.opencast._episode.dcTitle);if(ay){az.setMediaTitle(ay)}}}}r(av,az)}function ar(ax,aw){if(b()){var az=p.getFirstParentWithClass(ax,"jwplayer",3);if(!az){az=p.getFirstParentWithClass(ax,"jwplayer-video",3);if(az&&"undefined"!==typeof az.children&&az.children&&az.children.length&&az.children[0]){az=az.children[0]}}if(az){try{var au=jwplayer(az);if(au&&au.getPlaylistItem){var av=au.getPlaylistItem();if(av&&"undefined"!==typeof av.matomoResource&&av.matomoResource){aw.setResource(av.matomoResource)}else{if(av&&"undefined"!==typeof av.piwikResource&&av.piwikResource){aw.setResource(av.piwikResource)}}}}catch(ay){f(ay)}}}if(n()){var at=p.getFirstParentWithClass(ax,"flowplayer",4);
if(at){var au=flowplayer(at);if(au&&au.video&&"undefined"!==typeof au.video.matomoResource&&au.video.matomoResource){aw.setResource(au.video.matomoResource)}else{if(au&&au.video&&"undefined"!==typeof au.video.piwikResource&&au.video.piwikResource){aw.setResource(au.video.piwikResource)}}}}}function aj(){if(!ah&&ai.currentSrc){ah=ai.currentSrc}else{if(ah&&ai.currentSrc&&ah!=ai.currentSrc){ah=ai.currentSrc;var au=J.makeUrlAbsolute(ah);var at=aa.getMediaTitle();Z=false;aa.reset();aa.setResource(au);aa.setMediaTitle("");var av=p.getMediaTitle(ai);if(av&&av!==at){aa.setMediaTitle(av)}else{ak(ai,aa)}ar(ai,aa);X()}}}function aq(){if(!ag&&(aa.getResource()||aa.getMediaTitle())){ag=true;ao(ai,aa);ar(ai,aa);aa.trackUpdate()}}function al(){aj();ad();X();ae();aq()}var am=null;if(ai.loop){am=0}var ap=false;if(ai.loop&&ai.autoplay&&ai.muted){ap=true}ai.addEventListener("playing",function(){aj();if("undefined"!==typeof ai.paused&&ai.paused){return}if("undefined"!==typeof ai.ended&&ai.ended){return}if(!Z){ae();
Z=true;aa.play()}},true);ai.addEventListener("durationchange",X,true);ai.addEventListener("loadedmetadata",al,true);ai.addEventListener("loadeddata",al,true);ai.addEventListener("pause",function(){if(ai.currentTime&&ai.duration&&ai.currentTime===ai.duration){return}if(ai.seeking){return}ae();Z=false;aa.pause()},true);ai.addEventListener("seeking",function(){if(ai.seeking){ae();var at=parseInt(aa.getMediaProgressInSeconds(),10);if(am===null||am!==at){am=at;aa.trackEvent("seek")}}},true);ai.addEventListener("ended",function(){Z=false;aa.finish()},true);ai.addEventListener("timeupdate",function(){ae();X();if(an&&!aa.width){ad()}if("undefined"!==typeof ai.paused&&ai.paused){return}if("undefined"!==typeof ai.ended&&ai.ended){return}if(ap){var at=T.roundTimeToSeconds(aa.watchedTime);var au=aa.getMediaTotalLengthInSeconds();if(at>=30&&au>=1&&au<30&&(at/au)>=3){aa.disable()}}ag=true;if(!Z){Z=true;aa.play()}else{aa.update()}},true);ai.addEventListener("seeking",function(){aa.seekStart()},true);ai.addEventListener("seeked",function(){ae();
X();aa.seekFinish()},true);if(an){ai.addEventListener("resize",al,true);G.addEventListener("resize",function(){ad()},false)}aa.timeout=setTimeout(function(){al();aa.timeout=null},1500)};S.scanForMedia=function(Z){if(!G.addEventListener){return}var aa=N();var ad=Z.getElementsByTagName("video");var X;for(var Y=0;Y<ad.length;Y++){if(!p.isMediaIgnored(ad[Y])){X=p.getAttribute(ad[Y],"id");if(aa){var ab=Z.querySelector("#videoDisplay1_wrapper");if(ab&&("function"===typeof ab.contains)&&!ab.contains(ad[Y])){continue}}if(X!=="video_0"&&Z.querySelector("#videoPlayerWrapper_0")&&Z.querySelector("#video_0")){continue}new S(ad[Y],g.VIDEO)}}ad=null;var W=Z.getElementsByTagName("audio");for(var Y=0;Y<W.length;Y++){if(!p.isMediaIgnored(W[Y])){new S(W[Y],g.AUDIO)}}W=null;if("undefined"!==typeof soundManager&&soundManager&&"undefined"!==typeof soundManager.sounds){for(var Y in soundManager.sounds){if(Object.prototype.hasOwnProperty.call(soundManager.sounds,Y)){var ac=soundManager.sounds[Y];if(ac&&ac.isHTML5&&ac._a){if(!p.isMediaIgnored(ac._a)){new S(ac._a,g.AUDIO)
}}}}}};var P=function(Y,ae){if(!Y||!G.addEventListener){return}if(Y.hasPlayerInstance||!b()){return}var af=p.getFirstParentWithClass(Y,"jwplayer",3);if(!af){return}var aj=jwplayer(af);if(!aj||!aj.getItem||"undefined"===(typeof aj.getItem())){return}Y.hasPlayerInstance=true;function ag(al){var am=al.getPlaylistItem();if(am&&am.matomoResource){return am.matomoResource}if(am&&am.piwikResource){return am.piwikResource}if(am&&am.file){return am.file}return""}function X(am){var an=am.getPlaylistItem();if(an&&an.matomoTitle){return an.matomoTitle}if(an&&an.piwikTitle){return an.piwikTitle}if(an&&an.title){return an.title}if("function"===typeof u){var al=u(Y);if(al){return al}}return null}function ad(al,am,ao){var an=ag(al);if(ao&&an&&ao!=an){ao=an;am.reset();am.setResource(J.makeUrlAbsolute(ao));am.setMediaTitle(X(al));am.setWidth(al.getWidth());am.setHeight(al.getHeight());am.setFullscreen(al.getFullscreen());return true}return false}var ai=ag(aj);var W=J.makeUrlAbsolute(ai);var Z=p.getMediaResource(Y,W);
var ah=new l("jwplayer",ae,Z);ah.setMediaTitle(X(aj));ah.setWidth(aj.getWidth());ah.setHeight(aj.getHeight());ah.setFullscreen(aj.getFullscreen());K.push(ah);var aa=aj.getDuration();if(aa){ah.setMediaTotalLengthInSeconds(aa)}var ab=false,ac=ai;var ak=null;aj.on("play",function(){ad(aj,ah,ac);ab=true;ah.play()},true);aj.on("playlistItem",function(){ad(aj,ah,ac);if(aj.getState()!=="playing"){ab=false}},true);aj.on("pause",function(){if(aj.getPosition()&&aj.getDuration()&&aj.getPosition()===aj.getDuration()){return}ah.pause()},true);aj.on("complete",function(){ah.finish()},true);aj.on("time",function(){var al=aj.getPosition();if(al){ah.setMediaProgressInSeconds(al)}var am=aj.getDuration();if(am){ah.setMediaTotalLengthInSeconds(am)}if(ab){ah.update()}else{ab=true;ah.play()}},true);aj.on("seek",function(){ah.seekStart()},true);aj.on("seeked",function(){var al=aj.getPosition();if(al){ah.setMediaProgressInSeconds(al)}var an=aj.getDuration();if(an){ah.setMediaTotalLengthInSeconds(an)}ah.seekFinish();
var am=parseInt(ah.getMediaProgressInSeconds(),10);if(ak===null||ak!==am){ak=am;ah.trackEvent("seek")}},true);aj.on("resize",function(){ah.setWidth(aj.getWidth());ah.setHeight(aj.getHeight());ah.setFullscreen(aj.getFullscreen())},true);aj.on("fullscreen",function(){ah.setWidth(aj.getWidth());ah.setHeight(aj.getHeight());ah.setFullscreen(aj.getFullscreen())},false);ah.trackUpdate()};P.scanForMedia=function(X){if(!G.addEventListener||!b()){return}var Z=X.getElementsByTagName("object");for(var W=0;W<Z.length;W++){if(!p.isMediaIgnored(Z[W])&&p.hasCssClass(Z[W],"jw-swf")){var Y=g.VIDEO;if(p.hasCssClass(Z[W],"jw-flag-media-audio")){Y=g.AUDIO}new P(Z[W],Y)}}Z=null};var s=function(Z,ac){if(!Z){return}if(!G.addEventListener){return}if(Z.playerInstance){return}Z.playerInstance=true;var W=p.getAttribute(Z,"src");var Y=p.getMediaResource(Z,null);var af=new l("vimeo",ac,Y);af.setWidth(Z.clientWidth);af.setHeight(Z.clientHeight);af.setFullscreen(p.isFullscreen(Z));K.push(af);G.addEventListener("resize",function(){af.setWidth(Z.clientWidth);
af.setHeight(Z.clientHeight);af.setFullscreen(p.isFullscreen(Z))},false);var ae=p.getMediaTitle(Z);var aa=!p.getAttribute(Z,"data-piwik-title")&&!p.getAttribute(Z,"data-matomo-title");if(ae){af.setMediaTitle(ae)}Z.matomoSeekLastTime=null;var X=function(ai){if(!(/^(https?:)?\/\/(player.)?vimeo.com(?=$|\/)/).test(ai.origin)){return false}if(!ai||!ai.data){return}if(Z.contentWindow&&ai.source&&Z.contentWindow!==ai.source){return}var aj=ai.data;if("string"===typeof aj){aj=q().parse(ai.data)}if(("event" in aj&&aj.event==="ready")||("method" in aj&&aj.method==="ping")){if(ab==="*"){ab=ai.origin}if(!Z.isVimeoReady){Z.isVimeoReady=true;ad("addEventListener","play");ad("addEventListener","pause");ad("addEventListener","finish");ad("addEventListener","seek");ad("addEventListener","seeked");ad("addEventListener","playProgress");ad("getVideoTitle")}return}if("method" in aj){f("vimeoMethod",aj.method);switch(aj.method){case"getVideoTitle":if(aj.value&&aa){af.setMediaTitle(aj.value)}else{if(aa){r(Z,af)
}}aa=true;af.trackUpdate();break;case"getPaused":if(aj.value){af.pause()}}return}if("event" in aj){var ag=aj.event;f("vimeoEvent",ag);if(aj&&aj.data){aj=aj.data}if(af&&aj&&aj.seconds){if(af.getMediaProgressInSeconds()===aj.seconds&&(ag==="playProgress"||ag==="timeupdate")){return}af.setMediaProgressInSeconds(aj.seconds)}if(af&&aj&&aj.duration){af.setMediaTotalLengthInSeconds(aj.duration)}switch(ag){case"play":af.play();break;case"timeupdate":case"playProgress":if(af._isSeeking){af._isSeeking=false;af.seekFinish()}af.update();break;case"seek":af.seekStart();af._isSeeking=true;break;case"seeked":var ah=parseInt(af.getMediaProgressInSeconds(),10);if(Z.matomoSeekLastTime===null||Z.matomoSeekLastTime!==ah){Z.matomoSeekLastTime=ah;af.trackEvent("seek")}break;case"pause":if(aj&&aj.seconds&&aj&&aj.duration&&aj.seconds===aj.duration){f("ignoring pause event because video is finished");break}setTimeout(function(){ad("getPaused")},700);break;case"finish":af.finish();break}}};G.addEventListener("message",X,true);
var ab="*";af._isSeeking=false;function ad(aj,ah){var ag={method:aj};if(ah!==undefined){ag.value=ah}if(Z&&Z.contentWindow){if(navigator&&navigator.userAgent){var ai=parseFloat(navigator.userAgent.toLowerCase().replace(/^.*msie (\d+).*$/,"$1"));if(ai>=8&&ai<10){ag=q().stringify(ag)}}Z.contentWindow.postMessage(ag,ab)}}ad("ping")};s.scanForMedia=function(Y){if(!G.addEventListener){return}var X=Y.getElementsByTagName("iframe");for(var W=0;W<X.length;W++){if(p.isMediaIgnored(X[W])){continue}var Z=p.getAttribute(X[W],"src");if(Z&&(Z.indexOf("player.vimeo.com")>0||(Z.indexOf("vimeo.com")>0&&Z.indexOf("embed")>0))){new s(X[W],g.VIDEO)}}X=null};var w=function(ab,ae){if(!ab){return}if(!G.addEventListener){return}if(ab.playerInstance){return}if(typeof Plyr==="function"&&p.getFirstParentWithClass(ab,"plyr",2)){return}var X=p.getMediaResource(ab,null);var ah=new l("youtube",ae,X);ah.setWidth(ab.clientWidth);ah.setHeight(ab.clientHeight);ah.setFullscreen(p.isFullscreen(ab));K.push(ah);G.addEventListener("resize",function(){ah.setWidth(ab.clientWidth);
ah.setHeight(ab.clientHeight);ah.setFullscreen(p.isFullscreen(ab))},false);var ag=p.getMediaTitle(ab);if(ag){ah.setMediaTitle(ag)}var Y=false;var aa=null;var ad=!p.getAttribute(ab,"data-piwik-title")&&!p.getAttribute(ab,"data-matomo-title");var W=false;var ac=false;var af=null;function Z(ai){if(!ai||!ai.target){return}var am=ai.target;var al;if(ai&&"undefined"!==typeof ai.data&&null!==ai.data){al=ai.data}else{if(!am.getPlayerState){f("youtubeMissingPlayerState");return}al=am.getPlayerState()}f("youtubeStateChange",al);switch(al){case YT.PlayerState.ENDED:if(am.getCurrentTime){ah.setMediaProgressInSeconds(am.getCurrentTime())}if(am.getDuration){ah.setMediaTotalLengthInSeconds(am.getDuration())}ah.finish();if(aa){clearInterval(aa);aa=null}break;case YT.PlayerState.PLAYING:var aj=null;if(am.getVideoData){aj=am.getVideoData()}if(!af&&aj&&aj.video_id){af=aj.video_id}else{if(af&&aj&&aj.video_id&&af!=aj.video_id){af=aj.video_id;ah.reset();if(am.getVideoUrl){ah.setResource(am.getVideoUrl())}ad=true;
W=false;Y=false;f("currentVideoId has changed to "+af)}}if(am.getCurrentTime){ah.setMediaProgressInSeconds(am.getCurrentTime())}if(am.getDuration){ah.setMediaTotalLengthInSeconds(am.getDuration())}if(ad){if(aj&&aj.title){ah.setMediaTitle(aj.title)}ad=false}if(!W||ac){W=true;ac=false;Y=false;ah.play()}else{if(Y){Y=false;ah.seekFinish()}}ah.update();if(!aa){var ak=[];aa=setInterval(function(){if(ah.isPlaying){if(am&&am.getCurrentTime){var an=am.getCurrentTime();ah.setMediaProgressInSeconds(an);ak.push(an);if(ak.length>60){ak.shift();var ao=0;var ap=true;for(ao=0;ao<ak.length;ao++){if(ak[ao]!==ak[0]){ap=false}}if(ap){ac=true;ah.pause();ak=[];return}}}ah.update()}},1*1000)}break;case -1:case YT.PlayerState.PAUSED:setTimeout(function(){if(am&&am.getPlayerState&&am.getPlayerState()==YT.PlayerState.PAUSED){if(am&&am.getCurrentTime){ah.setMediaProgressInSeconds(am.getCurrentTime())}ah.pause();ac=true;if(aa){clearInterval(aa);aa=null}}else{f("target not found in YT paused state")}},1000);break;case YT.PlayerState.BUFFERING:ah.seekStart();
Y=true;if(aa){clearInterval(aa);aa=null}break}}ab.playerInstance=new YT.Player(ab,{events:{onReady:function(ai){if(!ai||!ai.target){return}if(ad&&ai.target&&ai.target.getVideoData){var aj=ai.target.getVideoData();if(aj&&aj.title){ah.setMediaTitle(aj.title)}else{r(ab,ah)}}ah.trackUpdate();if(ai.target.getPlayerState&&ai.target.getPlayerState()==YT.PlayerState.PLAYING){Z(ai)}},onError:function(ai){if(!ai||!ai.data){return}if(ah.isPlaying){ac=true;ah.pause()}f("YT onError event happened")},onStateChange:Z}})};w.scanForMedia=function(ae){if(!G.addEventListener){return}var Y=[];var ad=ae.getElementsByTagName("iframe");for(var aa=0;aa<ad.length;aa++){if(p.isMediaIgnored(ad[aa])){continue}var W=p.getAttribute(ad[aa],"src");if(W&&(W.indexOf("youtube.com")>0||W.indexOf("youtube-nocookie.com")>0)){p.setAttribute(ad[aa],"enablejsapi","true");Y.push(ad[aa])}}ad=null;function X(ai,ah){if(!(ai in window)){return}var aj=window[ai];if("function"!==typeof aj){return}try{if(aj.toString&&aj.toString().indexOf("function replaceMe")===0){return
}}catch(ag){}function af(){try{aj.apply(window,[].slice.call(arguments,0));ah()}catch(ak){ah();throw ak}}window[ai]=af}function ac(){return"object"===typeof YT&&YT&&YT.Player}function Z(){if(!ac()){return}var af=ae.getElementsByTagName("iframe");for(var ag=0;ag<af.length;ag++){if(p.isMediaIgnored(af[ag])){continue}var ah=p.getAttribute(af[ag],"src");if(ah&&(ah.indexOf("youtube.com")>0||ah.indexOf("youtube-nocookie.com")>0)){if(af[ag].setAttribute){af[ag].setAttribute("enablejsapi","true")}new w(af[ag],g.VIDEO)}}}if(Y&&Y.length){if(ac()){Z()}else{if(G.onYouTubeIframeAPIReady){X("onYouTubeIframeAPIReady",Z);ab(false)}else{if(G.onYouTubePlayerAPIReady){X("onYouTubePlayerAPIReady",Z);ab(false)}else{G.onYouTubeIframeAPIReady=Z;ab(true)}}}}function ab(ah){if(!ah&&(typeof G.YT==="object"||t.querySelectorAll('script[src="https://www.youtube.com/iframe_api"]').length>0)){return}var ag=t.createElement("script");ag.src="https://www.youtube.com/iframe_api";var af=t.getElementsByTagName("script");if(af&&af.length){var ai=af[0];
ai.parentNode.insertBefore(ag,ai)}else{if(t.body){t.body.appendChild(ag)}}}Y=null};var H=function(Z,aj){if(!Z){return}if(Z.playerInstance){return}var am=new SC.Widget(Z);Z.playerInstance=am;var W=p.getAttribute(Z,"data-matomo-resource");if(!W){W=p.getAttribute(Z,"data-piwik-resource")}var al=new l("soundcloud",aj,W);K.push(al);var ak=p.getMediaTitle(Z);if(ak){al.setMediaTitle(ak)}var X=false;var Y=null;var ae=!p.getAttribute(Z,"data-piwik-title")&&!p.getAttribute(Z,"data-matomo-title");function ab(){return al.getMediaTitle()&&al.getResource()}var ai=null;function ah(an){am.getCurrentSound(function(ao){if(ao===null){am.getCurrentSoundIndex(function(ap){if(ap>=0){am.getSounds(function(aq){if(ap in aq&&aq[ap]){an(aq[ap])}})}})}else{an(ao)}})}function ag(an){if(!an){return}ai=an.id;if(ae&&!al.getMediaTitle()&&an.title){al.setMediaTitle(an.title)}if(an.uri&&!al.getResource()){al.setResource(an.uri)}if(an.duration){al.setMediaTotalLengthInSeconds(parseInt(Math.floor(an.duration/1000)))}al.trackUpdate()
}function ad(an){if(an&&an.soundId&&ai!==an.soundId){ai=an.soundId;al.reset();al.setResource("");al.setMediaTitle("");ae=true;X=false;ah(ag);f("currentId has changed to "+ai);return true}return false}function aa(){am.getDuration(function(an){al.setMediaTotalLengthInSeconds(parseInt(Math.floor(an/1000)))})}function af(an){if("object"===typeof an&&"undefined"!==typeof an.currentPosition){al.setMediaProgressInSeconds(parseInt(Math.floor(an.currentPosition/1000)))}}var ac=false;am.bind(SC.Widget.Events.READY,function(an){ah(ag);am.bind(SC.Widget.Events.PLAY,function(ao){if(!ab()){return}if(ad(ao)){return}aa();af(ao);al.play()});am.bind(SC.Widget.Events.PLAY_PROGRESS,function(ao){if(!ab()){return}if(ad(ao)){return}aa();af(ao);if(ac){return}if(al.isPaused){al.play();return}if(!al.isPlaying){return}if(X){X=false;al.seekFinish()}al.update()});am.bind(SC.Widget.Events.PAUSE,function(ao){if(!ab()){return}if(ad(ao)){return}aa();af(ao);if(al.getMediaProgressInSeconds()&&al.getMediaTotalLengthInSeconds()===al.getMediaProgressInSeconds()){f("ignoring pause event because video is finished");
return}al.pause();ac=true;setTimeout(function(){ac=false},1000)});am.bind(SC.Widget.Events.FINISH,function(ao){if(!ab()){return}if(ad(ao)){return}aa();af(ao);al.finish()});am.bind(SC.Widget.Events.SEEK,function(ao){if(!ab()){return}if(ad(ao)){return}aa();af(ao);al.seekStart();X=true})})};H.scanForMedia=function(ab){function Z(){var ag=[];var ae=ab.getElementsByTagName("iframe");for(var af=0;af<ae.length;af++){if(p.isMediaIgnored(ae[af])){continue}var ah=p.getAttribute(ae[af],"src");if(ah&&ah.indexOf("w.soundcloud.com")>0){ag.push(ae[af])}}return ag}function aa(){return"object"===typeof SC&&SC&&SC.Widget}function ad(){if(!aa()){return}var af=Z();for(var ae=0;ae<af.length;ae++){var ag=p.getAttribute(af[ae],"src");if(ag&&ag.indexOf("w.soundcloud.com")>0){new H(af[ae],g.AUDIO)}}}var Y=Z();if(Y&&Y.length){if(aa()){ad()}else{var X=t.createElement("script");X.src="https://w.soundcloud.com/player/api.js";X.onload=ad;var W=t.getElementsByTagName("script");if(W&&W.length){var ac=W[0];ac.parentNode.insertBefore(X,ac)
}else{if(t.body){t.body.appendChild(X)}}}}Y=null};c.registerPlayer("html5",S);c.registerPlayer("vimeo",s);c.registerPlayer("youtube",w);c.registerPlayer("jwplayer",P);c.registerPlayer("soundcloud",H);function C(W){if("undefined"!==typeof W.MediaAnalytics){return}W.MediaAnalytics={enableEvents:true,enableProgress:true,quotaEventRequests:{},disableTrackEvents:function(){this.enableEvents=false},enableTrackEvents:function(){this.enableEvents=true},isTrackEventsEnabled:function(){return a&&this.enableEvents},disableTrackProgress:function(){this.enableProgress=false},enableTrackProgress:function(){this.enableProgress=true},isTrackProgressEnabled:function(){return a&&this.enableProgress}};

;Piwik.trigger("MediaAnalytics.TrackerInitialized",[W])}function z(){if(typeof window==="object"&&"function"===typeof G.piwikMediaAnalyticsAsyncInit){G.piwikMediaAnalyticsAsyncInit()}if(typeof window==="object"&&"function"===typeof G.matomoMediaAnalyticsAsyncInit){G.matomoMediaAnalyticsAsyncInit()
}E=true}var B=false;var k=false;function h(){if(!B&&b()){B=true;var X=jwplayer();if("object"===typeof X&&"function"===typeof X.on){X.on("ready",function(Y){c.scanForMedia(document)})}}if(!k&&n()){k=true;flowplayer(function(Z,Y){if(Z){Z.on("ready",function(){c.scanForMedia(document)});Z.on("load",function(){c.scanForMedia(document)})}});var W=flowplayer();if("object"===typeof W&&"function"===typeof W.on){W.on("ready",function(){c.scanForMedia(document)});W.on("load",function(){c.scanForMedia(document)})}}}function Q(){Piwik.DOM.onReady(function(){var W=V();if(!W||!v(W)||!W.length){return}c.scanForMedia(document);h()});Piwik.DOM.onLoad(function(){var W=V();if(!W||!v(W)||!W.length){return}c.scanForMedia(document);h()})}function R(){if("object"===typeof G&&"object"===typeof G.Piwik&&"object"===typeof G.Piwik.MediaAnalytics){return}if("object"===typeof G&&!G.Piwik){return}Piwik.MediaAnalytics={utils:T,url:J,element:p,players:c,rateLimit:L,MediaTracker:l,mediaType:g,scanForMedia:function(Y){c.scanForMedia(Y||document)
},setPingInterval:function(Y){if(10>Y){throw new Error("Ping interval needs to be at least ten seconds")}O=true;F=parseInt(Y,10)},removePlayer:function(Y){c.removePlayer(Y)},addPlayer:function(Z,Y){c.registerPlayer(Z,Y)},disableMediaAnalytics:function(){a=false},enableMediaAnalytics:function(){a=true},setMatomoTrackers:function(Y){this.setPiwikTrackers(Y)},setPiwikTrackers:function(Y){if(Y===null){A=null;return}if(!v(Y)){Y=[Y]}A=Y;if(E){Q()}},setMediaTitleFallback:function(Y){if("function"!==typeof Y){throw new Error("The mediaTitleFallback needs to be callback function")}u=Y},getMatomoTrackers:function(){return V()},getPiwikTrackers:function(){return V()},isMediaAnalyticsEnabled:function(){return a},setMaxTrackingTime:function(Y){I=parseInt(Y,10)*1000},enableDebugMode:function(){U=true},enableRateLimit:function(){y=true},disableRateLimit:function(){y=false}};Piwik.addPlugin("MediaAnalytics",{unload:function(){var Z;f("tracker intances mediaTrackerInstances");for(var Y=0;Y<K.length;Y++){Z=K[Y];
if(Z&&Z.timeout){f("before unload");Z.trackUpdate()}}},log:function(aa){var Y=V();if(Y&&Y.length){for(var Z=0;Z<Y.length;Z++){if(typeof Y[Z].MediaAnalytics.quotaEventRequests!=="undefined"&&Object.keys(Y[Z].MediaAnalytics.quotaEventRequests).length>0){Y[Z].MediaAnalytics.quotaEventRequests={}}}}return""}});if(G.Piwik.initialized){var W=Piwik.getAsyncTrackers();var X=0;for(X;X<W.length;X++){C(W[X])}Piwik.on("TrackerSetup",C);Piwik.retryMissedPluginCalls();z();Q();Piwik.on("TrackerAdded",Q)}else{Piwik.on("TrackerSetup",C);Piwik.on("MatomoInitialized",function(){z();Q();Piwik.on("TrackerAdded",Q)})}}if("object"===typeof G.Piwik){R()}else{if("object"!==typeof G.matomoPluginAsyncInit){G.matomoPluginAsyncInit=[]}G.matomoPluginAsyncInit.push(R)}})();
/* END GENERATED: tracker.min.js */


/* GENERATED: tracker.min.js */
/*!!
 * Copyright (C) InnoCraft Ltd - All rights reserved.
 *
 * All information contained herein is, and remains the property of InnoCraft Ltd.
 *
 * @link https://www.innocraft.com/
 * @license For license details see https://www.innocraft.com/license
 */
(function(){var l=false;var r=true;var q=null;var k=false;var j="FIELD_CHECKABLE";var y="FIELD_SELECTABLE";var h="FIELD_TEXT";var n=["password","text","url","tel","email","search","",null];var a=["color","date","datetime","datetime-local","month","number","range","time","week"];var b=["radio","checkbox"];var p=["button","submit","hidden","reset"];var u=30000;var z=[];var o=500;function e(){if(l&&"undefined"!==typeof console&&console&&console.debug){console.debug.apply(console,arguments)}}var c={getAttribute:function(B,A){if(B&&B.getAttribute&&A){return B.getAttribute(A)}return null},hasClass:function(B,A){if(!B||!B.className){return false}return(" "+B.className+" ").indexOf(" "+A+" ")>-1},hasNodeAttribute:function(B,A){if(B&&B.hasAttribute){return B.hasAttribute(A)
}if(B&&B.attributes){var C=(typeof B.attributes[A]);return C!=="undefined"}return false},isIgnored:function(A){if(this.hasNodeAttribute(A,"data-matomo-ignore")){return true}if(this.hasNodeAttribute(A,"data-piwik-ignore")){return true}return false},getTagName:function(A){if(A&&A.tagName){return(""+A.tagName).toLowerCase()}return null},findAllFormElements:function(A){if(A&&A.querySelectorAll){return A.querySelectorAll("form, [data-piwik-form], [data-matomo-form]")}return[]},findAllFieldElements:function(A){if(A&&A.querySelectorAll){return A.querySelectorAll("input,select,textarea,button,textarea")}return[]},findFormTrackerInstance:function(B,A){if("undefined"===typeof A){A=100}if(A<=0||!B){return null}if(B.formTrackerInstance){return B.formTrackerInstance}if(B.parentNode){return this.findFormTrackerInstance(B.parentNode,--A)}}};var v={isArray:function(A){return typeof A==="object"&&A!==null&&typeof A.length==="number"},indexOfArray:function(C,B){if(!C){return -1}if(C.indexOf){return C.indexOf(B)
}if(!this.isArray(C)){return -1}for(var A=0;A<C.length;A++){if(C[A]===B){return A}}return -1},getCurrentTime:function(){return new Date().getTime()},isNumber:function(A){return !isNaN(A)},generateUniqueId:function(){var D="";var B="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";var C=B.length;for(var A=0;A<6;A++){D+=B.charAt(Math.floor(Math.random()*C))}return D},paramsToQueryString:function(C){if(!C){C={}}var B="";for(var A in C){if(Object.prototype.hasOwnProperty.call(C,A)){if(C[A]===null){continue}B+=A+"="+encodeURIComponent(C[A])+"&"}}return B}};var g={getPiwikTrackers:function(){if(null===q){if("object"===typeof Piwik&&Piwik.getAsyncTrackers){return Piwik.getAsyncTrackers()}}if(v.isArray(q)){return q}return[]},trackParams:function(F,E){if(!r){return}var C=v.paramsToQueryString(F);if(C){if(C.substr(-1)!=="&"){C+="&"}C+="ca=1"}if(!C||C===""){return}var A=this.getPiwikTrackers();if(A&&A.length){var B=0,D;for(B;B<A.length;B++){D=A[B];if(!D.noOfFormRequestsSent){D.noOfFormRequestsSent=0
}if(D.noOfFormRequestsSent>o){e("maximum number of form request allowed for a tracker reached");continue}if(E&&500===D.getLinkTrackingTimer()&&D.setLinkTrackingTimer){D.setLinkTrackingTimer(650)}if(D&&(!D.FormAnalytics||D.FormAnalytics.isEnabled())){D.queueRequest(C);D.noOfFormRequestsSent++}}}if(l){e("trackProgress: "+Piwik.JSON.stringify(F))}}};function f(){
Matomo.FormAnalytics.setMaxNoOfFormRequestsAllowed(500);
;if(typeof window==="object"&&"function"===typeof window.piwikFormAnalyticsAsyncInit){window.piwikFormAnalyticsAsyncInit()}if(typeof window==="object"&&"function"===typeof window.matomoFormAnalyticsAsyncInit){window.matomoFormAnalyticsAsyncInit()}k=true}function t(A){this.reset();this.fields=[];this.firstFieldEngagementDate=null;this.lastFieldEngagementDate=null;this.hesitationTimeTracked=false;this.formStartTracked=false;this.node=A;this.formId=c.getAttribute(A,"id");this.formName=c.getAttribute(A,"data-matomo-name");if(!this.formName){this.formName=c.getAttribute(A,"data-piwik-name")}if(!this.formName){this.formName=c.getAttribute(A,"name")
}this.entryFieldName="";this.exitFieldName="";this.lastFocusedFieldName="";this.fieldsWithUpdates=[];this.fieldNodes=[];this.initialFormViewLoggedWithTrackers=[];this.trackingTimeout=null;this.timeLastTrackingRequest=0;this.timeOffWindowBeforeEngagement=0;this.timeOffWindowSinceEngagement=0;Piwik.DOM.addEventListener(window,"focus",(function(B){return function(){if(!B.timeWindowBlur){return}var C=v.getCurrentTime()-B.timeWindowBlur;B.timeWindowBlur=null;if(C<0){C=0}if(B.timeLastTrackingRequest){B.timeLastTrackingRequest=B.timeLastTrackingRequest+C}if(B.firstFieldEngagementDate){B.timeOffWindowSinceEngagement+=C;e("time off engaged "+B.timeOffWindowSinceEngagement)}else{B.timeOffWindowBeforeEngagement+=C;e("time off not engaged "+B.timeOffWindowBeforeEngagement)}}})(this));Piwik.DOM.addEventListener(window,"blur",(function(B){return function(){B.timeWindowBlur=v.getCurrentTime();e("window blur")}})(this));Piwik.DOM.addEventListener(A,"submit",(function(B){return function(){e("form submit");
B.trackFormSubmit()}})(this))}t.prototype.reset=function(){this.detectionDate=v.getCurrentTime();this.formViewId=v.generateUniqueId();this.fieldsWithUpdates=[];this.firstFieldEngagementDate=null;this.lastFieldEngagementDate=null;this.timeOffWindowSinceEngagement=0;this.timeOffWindowBeforeEngagement=0;this.formStartTracked=false;if(this.fields&&this.fields.length){for(var A=0;A<this.fields.length;A++){this.fields[A].resetOnFormSubmit()}}};t.prototype.trackFormSubmit=function(){this.setEngagedWithForm();var A=this.lastFieldEngagementDate-this.firstFieldEngagementDate-this.timeOffWindowSinceEngagement;if(A<0){A=0}var B={fa_su:1,fa_tts:A};this.sendUpdate(this.fields,B,true);this.reset()};t.prototype.trackFormConversion=function(){if(!this.timeLastTrackingRequest){this.sendUpdate([],{fa_co:1});return}var A=(v.getCurrentTime()-this.timeLastTrackingRequest)/1000;if(A<2){var B=this;setTimeout(function(){B.sendUpdate([],{fa_co:1})},800)}else{this.sendUpdate([],{fa_co:1})}};t.prototype.shouldBeTracked=function(){return !!this.fields&&!!this.fields.length
};t.prototype.trackInitialFormView=function(){if(!this.initialFormViewLoggedWithTrackers||!this.initialFormViewLoggedWithTrackers.length){this.initialFormViewLoggedWithTrackers=g.getPiwikTrackers();this.sendUpdate([],{fa_fv:"1"})}};t.prototype.setEngagedWithForm=function(A){this.lastFieldEngagementDate=v.getCurrentTime();if(!this.firstFieldEngagementDate){this.firstFieldEngagementDate=this.lastFieldEngagementDate}};t.prototype.trackFieldUpdate=function(A){if(v.indexOfArray(this.fieldsWithUpdates,A)===-1){this.fieldsWithUpdates.push(A)}this.scheduleSendUpdate()};t.prototype.scheduleSendUpdate=function(){if(this.trackingTimeout){clearTimeout(this.trackingTimeout);this.trackingTimeout=null}var A=this;this.trackingTimeout=setTimeout(function(){var B=A.fieldsWithUpdates;A.fieldsWithUpdates=[];A.sendUpdate(B)},u)};t.prototype.sendUpdate=function(D,G,F){if(!this.shouldBeTracked()){return}if(this.trackingTimeout){clearTimeout(this.trackingTimeout);this.trackingTimeout=null}if(!D){D=[]}var A=[];
for(var C=0;C<D.length;C++){A.push(D[C].getTrackingParams())}var E={fa_vid:this.formViewId,fa_id:this.formId,fa_name:this.formName};if(this.entryFieldName){E.fa_ef=this.entryFieldName}if(this.exitFieldName){E.fa_lf=this.exitFieldName}if(A.length){E.fa_fields=Piwik.JSON.stringify(A)}if(this.firstFieldEngagementDate){if(!this.formStartTracked){E.fa_st="1";this.formStartTracked=true}if(!this.hesitationTimeTracked){E.fa_ht=this.firstFieldEngagementDate-this.detectionDate-this.timeOffWindowBeforeEngagement;this.hesitationTimeTracked=true}if(this.lastFieldEngagementDate&&this.timeLastTrackingRequest){E.fa_ts=this.lastFieldEngagementDate-this.timeLastTrackingRequest;if(E.fa_ts<0){E.fa_ts=0}}else{if(this.lastFieldEngagementDate&&!this.timeLastTrackingRequest){E.fa_ts=this.lastFieldEngagementDate-this.firstFieldEngagementDate-this.timeOffWindowSinceEngagement;if(E.fa_ts<0){E.fa_ts=0}}}this.timeLastTrackingRequest=v.getCurrentTime()}if(G){for(var B in G){if(Object.prototype.hasOwnProperty.call(G,B)){E[B]=G[B]
}}}if("undefined"===typeof F){F=false}g.trackParams(E,F)};t.prototype.scanForFields=function(){var D,C=0,G,F,B;F=c.findAllFieldElements(this.node);for(D=0;D<F.length;D++){if(!F[D]){continue}if(this.fields&&this.fields.length&&this.fields.length>2500){continue}B=F[D];if(c.isIgnored(B)||v.indexOfArray(this.fieldNodes,B)>-1){continue}var A=c.getTagName(B);var E=c.getAttribute(B,"type");if(v.indexOfArray(p,E)!==-1){continue}else{if("button"===A){continue}}if(A==="input"&&!E){E="text"}var H=c.getAttribute(B,"data-matomo-name");if(!H){H=c.getAttribute(B,"data-piwik-name");if(!H){H=c.getAttribute(B,"name");if(!H){H=c.getAttribute(B,"id");if(!H){continue}}}}this.fieldNodes.push(B);var I=false;for(C=0;C<this.fields.length;C++){if(this.fields[C]&&this.fields[C].fieldName===H){I=true;this.fields[C].addNode(B);break}}if(!I){G=new w(this,F[D],A,E,H);this.addFormField(G)}}};t.prototype.addFormField=function(A){this.fields.push(A)};function w(E,D,C,B,F){this.discoveredDate=v.getCurrentTime();this.tracker=E;
this.timespent=0;this.hesitationtime=0;this.nodes=[];this.tagName=C;this.fieldName=F;this.fieldType=B;this.startFocus=null;this.timeLastChange=null;this.numChanges=0;this.numFocus=0;this.numDeletes=0;this.numCursor=0;this.canCountChange=true;this.isFocusedCausedAuto=c.hasNodeAttribute(D,"autofocus");if(this.tagName==="select"){this.category=y}else{if(this.tagName==="textarea"){this.category=h}else{if(v.indexOfArray(b,this.fieldType)!==-1){this.category=j}else{if(v.indexOfArray(a,this.fieldType)!==-1){this.category=y}else{this.category=h}}}}this.addNode(D);var A=(D===document.activeElement);if(A){this.onFocus()}}w.prototype.addNode=function(B){this.nodes.push(B);function A(E,C,G){if(E&&"object"===typeof tinymce&&"function"===typeof tinymce.get&&c.getTagName(E)==="textarea"&&c.getAttribute(E,"id")){var F=c.getAttribute(E,"id");var D=tinymce.get(F);if(D){D.on(C,G);return}}else{if(E&&"function"===typeof jQuery&&c.getTagName(E)==="select"&&c.hasClass(E,"select2-hidden-accessible")&&E.nextSibling){if(C==="focus"){C="select2:open"
}else{if(C==="blur"){C="select2:close"}}jQuery(E).on(C,G);return}}Piwik.DOM.addEventListener(E,C,G)}A(B,"focus",(function(C){return function(D){if(C.isAutoFocus()){e("field autofocus "+C.fieldName)}else{e("field focus "+C.fieldName)}C.onFocus()}})(this));A(B,"blur",(function(C){return function(){e("field blur "+C.fieldName);C.onBlur()}})(this));if(this.category===h){A(B,"keyup",(function(C){return function(F){var E=F.which||F.keyCode;var D=[9,16,17,18,20,27,91];if((E&&v.indexOfArray(D,E)!==-1)||F.isCtrlKey){return}if(E>=37&&E<=40){if(!C.isBlank()){C.numCursor++;C.tracker.trackFieldUpdate(C)}return}if(E==8||E==46){if(!C.isBlank()){C.numDeletes++;C.tracker.trackFieldUpdate(C)}return}e("field text keyup "+C.fieldName);C.onChange()}})(this));A(B,"paste",(function(C){return function(){e("field text paste "+C.fieldName);C.onChange()}})(this))}else{A(B,"change",(function(C){return function(){e("field change "+C.fieldName);C.onChange()}})(this))}};w.prototype.resetOnFormSubmit=function(){this.hesitationtime=0;
this.timespent=0;this.numFocus=0;this.numDeletes=0;this.numCursor=0;this.numChanges=0;this.startFocus=null;this.timeLastChange=null;this.canCountChange=true;this.hasChangedValueSinceFocus=false;this.isFocusedCausedAuto=false};w.prototype.isAutoFocus=function(){if(!this.isFocusedCausedAuto){return false}if(this.tracker.entryFieldName&&this.tracker.entryFieldName!==this.fieldName){this.isFocusedCausedAuto=false}if(this.tracker.exitFieldName&&this.tracker.exitFieldName!==this.fieldName){this.isFocusedCausedAuto=false}return this.isFocusedCausedAuto};w.prototype.getTrackingParams=function(){return{fa_fts:this.getTimeSpent(),fa_fht:this.getHesitationTime(),fa_fb:this.isBlank(),fa_fn:this.fieldName,fa_fch:this.numChanges,fa_ff:this.numFocus,fa_fd:this.numDeletes,fa_fcu:this.numCursor,fa_ft:this.fieldType||this.tagName,fa_fs:this.getFieldSize()}};w.prototype.isBlank=function(){if(this.category===j){for(var A=0;A<this.nodes.length;A++){if(this.nodes[A]&&this.nodes[A].checked){return false}}return true
}if(!this.nodes[0]){return false}var B=this.nodes[0];if("undefined"===typeof B.value){return true}var C=B.value;if(null===C||false===C||""===C){return true}return String(C).length===0};w.prototype.getFieldSize=function(){if(this.category===h){if(this.nodes[0]&&this.nodes[0].value){return String(this.nodes[0].value).length}else{return 0}}else{return -1}};w.prototype.getTimeSpent=function(){if(this.numChanges&&!this.timeSpent){this.timeSpent=1}if(!this.startFocus||this.isAutoFocus()){return this.timespent}if(this.timeLastChange){var A=this.timeLastChange-this.startFocus;if(A<0){A=0}return this.timespent+A}return this.timespent+v.getCurrentTime()-this.startFocus};w.prototype.getHesitationTime=function(){if(this.numChanges||!this.startFocus||this.isAutoFocus()){return this.hesitationtime}var A=v.getCurrentTime();return this.hesitationtime+(A-this.startFocus)};w.prototype.onFocus=function(){this.startFocus=v.getCurrentTime();var A=this.fieldName!==this.tracker.lastFocusedFieldName;if(A&&this.tracker.lastFocusedFieldName){this.isFocusedCausedAuto=false
}this.timeLastChange=null;this.hasChangedValueSinceFocus=false;this.tracker.lastFocusedFieldName=this.fieldName;if(A){this.canCountChange=true}if(A&&!this.isAutoFocus()){this.numFocus++;this.tracker.setEngagedWithForm();this.tracker.trackFieldUpdate(this);this.tracker.exitFieldName=this.fieldName;this.tracker.scheduleSendUpdate()}};w.prototype.onBlur=function(){if(!this.startFocus){return}if(this.hasChangedValueSinceFocus){if(this.timeLastChange&&this.startFocus){this.timespent+=(this.timeLastChange-this.startFocus)}this.timeLastChange=null;this.startFocus=null;return}if(!this.isAutoFocus()){var A=v.getCurrentTime();this.timespent+=A-this.startFocus;if(!this.numChanges){this.hesitationtime+=A-this.startFocus}this.tracker.setEngagedWithForm();this.tracker.trackFieldUpdate(this)}this.startFocus=null};w.prototype.onChange=function(){this.timeLastChange=v.getCurrentTime();if(this.isAutoFocus()){this.startFocus=this.timeLastChange}else{if(!this.startFocus){return}}this.isFocusedCausedAuto=false;
this.hasChangedValueSinceFocus=true;if(!this.numChanges){this.hesitationtime+=this.timeLastChange-this.startFocus}if(this.canCountChange){this.numChanges++;this.canCountChange=false}if(!this.tracker.entryFieldName){this.tracker.entryFieldName=this.fieldName}this.tracker.setEngagedWithForm();this.tracker.trackFieldUpdate(this)};function x(C,A){if(!r){return}if(!document.querySelectorAll){return}var B;if(C&&C.formTrackerInstance){B=C.formTrackerInstance;B.scanForFields()}else{if(!c.isIgnored(C)){B=new t(C);B.scanForFields();z.push(B);C.formTrackerInstance=B}}if(A&&B&&B.shouldBeTracked()){B.trackInitialFormView()}return B}function d(C){if("undefined"===typeof C){C=document}var A=c.findAllFormElements(C);for(var B=0;B<A.length;B++){x(A[B],true)}}function i(){Piwik.DOM.onReady(function(){var A=g.getPiwikTrackers();if(!A||!v.isArray(A)||!A.length){return}d(document)});Piwik.DOM.onLoad(function(){var A=g.getPiwikTrackers();if(!A||!v.isArray(A)||!A.length){return}d(document)})}function m(A){if("undefined"!==typeof A.FormAnalytics){return
}A.FormAnalytics={enabled:true,enable:function(){this.enabled=true},disable:function(){this.enabled=false},isEnabled:function(){return r&&this.enabled}}}function s(){if("object"===typeof window&&"object"===typeof window.Piwik&&"object"===typeof window.Piwik.FormAnalytics){return}if("object"===typeof window&&!window.Piwik){return}Piwik.FormAnalytics={element:c,utils:v,tracking:g,FormField:w,FormTracker:t,disableFormAnalytics:function(){r=false},enableFormAnalytics:function(){r=true},isFormAnalyticsEnabled:function(){return r},setMatomoTrackers:function(A){this.setPiwikTrackers(A)},setPiwikTrackers:function(A){if(A===null){q=null;return}if(!v.isArray(A)){A=[A]}q=A;if(k){i()}},setTrackingTimer:function(A){if(A<5){throw new Error("Delay needs to be at least five")}u=parseInt(A,10)},enableDebugMode:function(){l=true},scanForForms:d,trackFormSubmit:function(B){var A=c.findFormTrackerInstance(B);if(A){A.trackFormSubmit()}},trackFormConversion:function(A,C){if("string"===typeof A||"string"===typeof C){g.trackParams({fa_vid:v.generateUniqueId(),fa_id:C,fa_name:A,fa_co:1});
return}var B=c.findFormTrackerInstance(A);if(B){B.trackFormConversion()}},trackForm:function(A){return x(A,true)},setMaxNoOfFormRequestsAllowed:function(A){if(A==parseInt(A)){o=A}}};Piwik.addPlugin("FormAnalytics",{log:function(F){if(!r||!F||!F.tracker){return""}var C=F.tracker;if(C.FormAnalytics&&!C.FormAnalytics.isEnabled()){return""}var A=c.findAllFormElements(document);var E="";for(var B=0;B<A.length;B++){var D=x(A[B],false);if(D&&D.shouldBeTracked()&&v.indexOfArray(D.initialFormViewLoggedWithTrackers,C)===-1){D.initialFormViewLoggedWithTrackers.push(C);if(D.formViewId!==null){E+="&fa_fp["+B+"][fa_vid]="+encodeURIComponent(D.formViewId)}if(D.formId!==null){E+="&fa_fp["+B+"][fa_id]="+encodeURIComponent(D.formId)}if(D.formName!==null){E+="&fa_fp["+B+"][fa_name]="+encodeURIComponent(D.formName)}E+="&fa_fp["+B+"][fa_fv]=1"}}if(E){e("sending request with pageview"+E);return"&fa_pv=1"+E}return""},unload:function(){var B;for(var A=0;A<z.length;A++){B=z[A];if(B&&B.trackingTimeout){e("before unload");
clearTimeout(B.trackingTimeout);B.sendUpdate(B.fieldsWithUpdates,{},true)}}}});if(window.Piwik.initialized){Piwik.on("TrackerSetup",m);Piwik.retryMissedPluginCalls();f();i();Piwik.on("TrackerAdded",function(){setTimeout(i,700)})}else{Piwik.on("TrackerSetup",m);Piwik.on("MatomoInitialized",function(){f();i();Piwik.on("TrackerAdded",function(){setTimeout(i,700)})})}}if("object"===typeof window.Piwik){s()}else{if("object"!==typeof window.matomoPluginAsyncInit){window.matomoPluginAsyncInit=[]}window.matomoPluginAsyncInit.push(s)}})();
/* END GENERATED: tracker.min.js */


/* GENERATED: tracker.min.js */
(function(){function a(){if("object"===typeof window&&!window.Matomo){return}window.Matomo.on("TrackerSetup",function(b){b.setCookieConsentGiven=function(){};b.rememberCookieConsentGiven=function(){};b.disableCookies()})}if("object"===typeof window.Matomo){a()}else{if("object"!==typeof window.matomoPluginAsyncInit){window.matomoPluginAsyncInit=[]}window.matomoPluginAsyncInit.push(a)}})();
/* END GENERATED: tracker.min.js */

(function(){function b(){if("object"!==typeof _paq){return false}var c=typeof _paq.length;if("undefined"===c){return false}return !!_paq.length}if(window&&"object"===typeof window.matomoPluginAsyncInit&&window.matomoPluginAsyncInit.length){var a=0;for(a;a<window.matomoPluginAsyncInit.length;a++){if(typeof window.matomoPluginAsyncInit[a]==="function"){window.matomoPluginAsyncInit[a]()}}}if(window&&window.piwikAsyncInit){window.piwikAsyncInit()}if(window&&window.matomoAsyncInit){window.matomoAsyncInit()}if(!window.Matomo.getAsyncTrackers().length){if(b()){window.Matomo.addTracker()
}else{_paq={push:function(c){var d=typeof console;if(d!=="undefined"&&console&&console.error){console.error("_paq.push() was used but Matomo tracker was not initialized before the matomo.js file was loaded. Make sure to configure the tracker via _paq.push before loading matomo.js. Alternatively, you can create a tracker via Matomo.addTracker() manually and then use _paq.push but it may not fully work as tracker methods may not be executed in the correct order.",c)}}}}}window.Matomo.trigger("MatomoInitialized",[]);window.Matomo.initialized=true}());(function(){var a=(typeof window.AnalyticsTracker);if(a==="undefined"){window.AnalyticsTracker=window.Matomo}}());if(typeof window.piwik_log!=="function"){window.piwik_log=function(c,e,g,f){function b(h){try{if(window["piwik_"+h]){return window["piwik_"+h]}}catch(i){}return}var d,a=window.Matomo.getTracker(g,e);a.setDocumentTitle(c);a.setCustomData(f);d=b("tracker_pause");if(d){a.setLinkTrackingTimer(d)}d=b("download_extensions");if(d){a.setDownloadExtensions(d)
}d=b("hosts_alias");if(d){a.setDomains(d)}d=b("ignore_classes");if(d){a.setIgnoreClasses(d)}a.trackPageView();if(b("install_tracker")){piwik_track=function(i,j,k,h){a.setSiteId(j);a.setTrackerUrl(k);a.trackLink(i,h)};a.enableLinkTracking()}}}
/*!! @license-end */;