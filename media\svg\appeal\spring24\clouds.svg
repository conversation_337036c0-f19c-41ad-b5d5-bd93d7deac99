<svg width="1708" height="1639" viewBox="0 0 1708 1639" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2317_129)">
<path opacity="0.8" d="M1708 0H0V1638.5H1708V0Z" fill="url(#paint0_radial_2317_129)"/>
<path opacity="0.9" d="M1708 -385H383C171.475 -385 0 -213.525 0 -2.00002V666C0 824.506 128.494 953 287 953H1708V-385Z" fill="url(#paint1_radial_2317_129)"/>
<mask id="mask0_2317_129" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="17" y="601" width="1691" height="856">
<rect width="876" height="856" transform="matrix(-1 0 0 1 1708 601)" fill="url(#paint2_linear_2317_129)"/>
<path d="M832 601H445C208.622 601 17 792.622 17 1029V1029C17 1265.38 208.622 1457 445 1457H832V601Z" fill="url(#paint3_radial_2317_129)"/>
</mask>
<g mask="url(#mask0_2317_129)">
<path opacity="0.6" d="M1236.5 947C1226.44 947 1216.58 947.866 1207 949.528C1193.12 881.325 1132.8 830 1060.5 830C1034.42 830 1009.91 836.676 988.565 848.412C955.957 805.619 904.454 778 846.5 778C833.587 778 820.995 779.371 808.86 781.976C786.594 678.531 694.601 601 584.5 601C500.114 601 426.366 646.544 386.494 714.393C378.624 713.473 370.617 713 362.5 713C267.138 713 187.021 778.274 164.39 866.58C154.994 864.243 145.146 863 135 863C69.8306 863 17 914.263 17 977.5C17 979.681 17.063 981.849 17.187 984H17L16.9971 1371C16.9971 1371.22 16.998 1371.45 17 1371.67V1457H1708V1297H1708C1707.46 1218.48 1643.64 1155 1565 1155H1427.11C1429.66 1143.24 1431 1131.03 1431 1118.5C1431 1023.78 1354.22 947 1259.5 947H1236.5Z" fill="#E3E5F2"/>
<path opacity="0.8" d="M1707.48 1293C1707.82 1288.55 1708 1284.04 1708 1279.5C1708 1184.78 1631.22 1108 1536.5 1108H1478.86C1475.46 1028.45 1409.89 965 1329.5 965C1303.42 965 1278.91 971.676 1257.57 983.412C1224.96 940.619 1173.45 913 1115.5 913C1102.59 913 1090 914.371 1077.86 916.976C1055.59 813.531 963.601 736 853.5 736C769.114 736 695.366 781.544 655.494 849.393C647.624 848.473 639.617 848 631.5 848C536.138 848 456.021 913.274 433.39 1001.58C423.994 999.243 414.146 998 404 998C340.384 998 288.525 1046.85 286.089 1108H188.5C93.7832 1108 17 1184.78 17 1279.5C17 1284.04 17.1768 1288.55 17.5234 1293H17V1457H1708V1293H1707.48Z" fill="#FAFAFA"/>
<path opacity="0.6" d="M709.066 1119C709.022 1117.51 709 1116.01 709 1114.5C709 1031.93 775.933 965 858.499 965C884.575 965 909.092 971.676 930.434 983.412C963.042 940.619 1014.55 913 1072.5 913C1085.41 913 1098 914.371 1110.14 916.976C1132.41 813.531 1224.4 736 1334.5 736C1418.89 736 1492.63 781.544 1532.51 849.393C1540.38 848.473 1548.38 848 1556.5 848C1651.86 848 1707.5 913 1707.5 998C1707.5 1022 1707.5 1059.5 1707.5 1084C1707.5 1108.5 1707.5 1098.76 1707.5 1162C1707.5 1177.5 1707.62 1198.85 1707.5 1201V1252.5V1456.5H211.311L211 1431.5C211 1429.17 211.104 1426.87 211.311 1424.6C211.104 1421.76 211 1418.89 211 1416C211 1351.38 263.382 1299 328 1299H471.952C493.411 1224.5 562.091 1170 643.5 1170C666.624 1170 688.72 1174.4 709 1182.4V1119H709.066Z" fill="white"/>
</g>
</g>
<defs>
<radialGradient id="paint0_radial_2317_129" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1708 819) rotate(90) scale(820 1708)">
<stop offset="0.221624" stop-color="#FFF4DF"/>
<stop offset="0.605" stop-color="#FFE6FB"/>
<stop offset="0.895" stop-color="#BDCAF8" stop-opacity="0.483333"/>
<stop offset="1" stop-color="#80C2F5" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint1_radial_2317_129" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1708 232.471) rotate(179.831) scale(1708.01 720.532)">
<stop stop-color="#FFFBE9"/>
<stop offset="1" stop-color="#FFFAE1" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint2_linear_2317_129" x1="438" y1="0" x2="438" y2="856" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint3_radial_2317_129" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(832 601) rotate(180) scale(832.5 856)">
<stop/>
<stop offset="1" stop-color="#666666" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_2317_129">
<rect width="1708" height="1639" fill="white" transform="matrix(-1 0 0 1 1708 0)"/>
</clipPath>
</defs>
</svg>
