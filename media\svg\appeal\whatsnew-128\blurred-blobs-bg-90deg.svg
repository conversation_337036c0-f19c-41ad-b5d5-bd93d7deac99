<svg width="615" height="654" viewBox="0 0 615 654" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.2" filter="url(#filter0_if_2658_99373)">
<path d="M179.125 183.085C179.781 189.118 181.611 195.659 189.51 200.233C200.894 206.42 216.294 209.517 230.602 213.152C255.91 219.192 281.519 225.65 302.212 235.835C327.397 248.002 346 263.524 370.016 276.303C382.267 282.813 397.41 288.083 414.5 289.714C438.259 292.247 464.554 289.128 482.793 280.188C495.861 273.542 502.459 264.099 506.268 254.798C512.418 239.689 512.42 224.112 511.609 208.731C510.755 196.233 508.88 183.753 508.969 171.238C509.016 170.16 509.035 168.561 509.088 167.282C509.427 156.565 512.405 145.918 516.996 135.501C521.797 124.062 527.794 112.701 529.864 100.989C530.936 94.1808 530.491 86.8021 523.363 80.9206C516.06 74.8961 504.728 70.6826 492.957 67.5777C467.877 61.1317 439.69 59.0804 412.141 59.5708C388.08 59.9323 364.135 61.4077 340.145 62.4079C328.417 62.8997 316.126 63.262 304.161 63.5125C278.863 64.1035 253.21 64.2016 228.524 67.6637C217.832 69.2967 206.767 71.911 200.254 77.0591C195.127 81.1404 192.468 85.9792 190.5 90.7579C186.76 101.029 186.204 111.534 185.312 121.96C184.558 133.476 183.578 144.993 181.445 156.464C180.022 165.311 178.406 174.197 179.125 183.085Z" fill="#404288"/>
</g>
<g opacity="0.2" filter="url(#filter1_if_2658_99373)">
<path d="M165.277 221.294C156.606 225.165 147.967 229.975 146.435 236.971C144.839 246.819 150.648 257.071 154.901 267.037C163.01 284.439 170.694 302.25 169.397 319.531C168.164 340.431 157.377 359.672 154.422 380.286C152.929 390.797 155.28 402.173 164.455 412.5C176.811 427.012 199.435 439.626 225.37 444.431C244.315 447.735 263.079 445.861 279.732 442.539C306.753 437.122 330.296 428.058 352.988 418.663C371.295 410.922 388.878 402.631 407.853 395.397C409.514 394.796 411.944 393.875 413.912 393.161C430.34 387.11 448.46 382.549 467.33 379.006C487.887 374.984 509.141 371.664 528.251 365.985C539.27 362.611 550.118 358.073 554.152 350.739C558.282 343.227 556.931 334.556 553.606 326.291C546.265 308.778 530.165 292.117 510.659 277.285C493.723 264.292 475.184 252.011 457.332 239.429C448.6 233.279 439.68 226.746 431.152 220.326C413.027 206.788 395.405 192.768 373.358 181.237C363.608 176.32 352.12 171.77 339.903 171.192C330.243 170.753 321.119 172.11 312.557 173.811C294.487 177.735 278.233 183.543 261.868 189.12C243.95 195.408 225.877 201.572 207.089 207.076C192.749 211.444 178.22 215.728 165.277 221.294Z" fill="#793D61"/>
</g>
<g opacity="0.2" filter="url(#filter2_if_2658_99373)">
<path d="M173.848 410.97C178.585 405.035 184.81 398.833 195.605 395.866C210.887 391.998 227.977 392.071 244.369 391.376C273.1 390.539 302.409 389.344 329.465 383.381C362.24 376.354 390.89 364.588 422.943 356.702C439.289 352.689 457.604 350.523 475.343 352.419C500.184 354.791 523.652 363.374 535.325 376.145C543.525 385.538 543.53 396.405 540.916 406.553C536.634 423.028 526.051 438.702 514.81 454.009C505.487 466.408 495.182 478.576 486.765 491.187C486.078 492.281 485.01 493.895 484.193 495.192C477.239 506.046 472.903 517.375 470.292 528.807C467.191 541.31 465.307 553.983 459.363 566.195C455.78 573.268 450.333 580.6 439.4 585.042C428.2 589.591 414.31 591.484 400.746 592.171C371.961 593.464 343.139 589.691 316.664 583.494C293.496 578.148 271.198 571.705 248.533 565.731C237.455 562.808 225.741 559.899 214.268 557.169C190.052 551.336 165.156 545.926 143.488 537.331C134.193 533.474 125.202 528.552 122.362 522.024C120.147 516.856 120.847 511.437 122.179 506.221C125.52 495.112 132.117 484.428 138.333 473.752C145.425 462.009 152.296 450.218 158.016 438.235C162.643 429.039 167.108 419.764 173.848 410.97Z" fill="#3D6E79"/>
</g>
<defs>
<filter id="filter0_if_2658_99373" x="120.003" y="0.556046" width="469.245" height="348.953" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.36818"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2658_99373"/>
<feGaussianBlur stdDeviation="29.4727" result="effect2_foregroundBlur_2658_99373"/>
</filter>
<filter id="filter1_if_2658_99373" x="87.9361" y="112.88" width="527.011" height="391.509" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.27852"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2658_99373"/>
<feGaussianBlur stdDeviation="29.1141" result="effect2_foregroundBlur_2658_99373"/>
</filter>
<filter id="filter2_if_2658_99373" x="60.1458" y="290.936" width="543.102" height="362.275" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.59845"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2658_99373"/>
<feGaussianBlur stdDeviation="30.3938" result="effect2_foregroundBlur_2658_99373"/>
</filter>
</defs>
</svg>
