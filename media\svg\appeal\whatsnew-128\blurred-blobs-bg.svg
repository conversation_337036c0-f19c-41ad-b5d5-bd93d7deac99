<svg width="1201" height="615" viewBox="0 0 1201 615" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.2" filter="url(#filter0_if_2490_39340)">
<path d="M310.095 435.874C322.32 435.219 335.574 433.389 344.843 425.49C357.379 414.106 363.654 398.706 371.019 384.398C383.259 359.09 396.344 333.481 416.981 312.788C441.635 287.603 473.087 269 498.982 244.984C512.173 232.733 522.85 217.59 526.157 200.5C531.289 176.74 524.969 150.445 506.853 132.207C493.386 119.139 474.254 112.541 455.405 108.732C424.792 102.582 393.228 102.58 362.062 103.391C336.737 104.245 311.449 106.12 286.09 106.031C283.906 105.984 280.665 105.965 278.075 105.912C256.358 105.573 234.784 102.594 213.676 98.0035C190.498 93.2032 167.478 87.2061 143.746 85.1359C129.951 84.0641 114.999 84.5092 103.082 91.6371C90.8743 98.9398 82.3364 110.272 76.0452 122.043C62.9837 147.122 58.8272 175.31 59.8208 202.858C60.5534 226.92 63.5429 250.865 65.5697 274.855C66.5661 286.583 67.3004 298.874 67.8078 310.839C69.0054 336.137 69.2042 361.79 76.2195 386.476C79.5282 397.168 84.8256 408.233 95.2571 414.746C103.527 419.873 113.332 422.532 123.015 424.5C143.827 428.24 165.112 428.795 186.239 429.688C209.574 430.442 232.911 431.422 256.153 433.555C274.081 434.977 292.085 436.594 310.095 435.874Z" fill="#404288"/>
</g>
<g opacity="0.2" filter="url(#filter1_if_2490_39340)">
<path d="M387.519 449.723C395.361 458.394 405.108 467.033 419.284 468.565C439.238 470.161 460.012 464.352 480.206 460.099C515.468 451.99 551.557 444.306 586.574 445.603C628.923 446.836 667.91 457.623 709.681 460.578C730.979 462.071 754.029 459.72 774.955 450.545C804.36 438.189 829.92 415.565 839.657 389.63C846.351 370.685 842.554 351.921 835.821 335.268C824.845 308.247 806.481 284.704 787.443 262.012C771.757 243.705 754.957 226.122 740.3 207.147C739.081 205.486 737.216 203.056 735.768 201.088C723.507 184.66 714.265 166.54 707.087 147.67C698.937 127.113 692.21 105.859 680.702 86.7492C673.866 75.7298 664.671 64.8817 649.811 60.8482C634.588 56.7178 617.019 58.0687 600.27 61.3942C564.784 68.7352 531.024 84.8349 500.971 104.341C474.644 121.277 449.759 139.816 424.265 157.668C411.804 166.4 398.565 175.32 385.556 183.848C358.125 201.973 329.717 219.595 306.352 241.641C296.388 251.392 287.168 262.88 285.996 275.097C285.108 284.757 287.857 293.881 291.303 302.443C299.256 320.513 311.024 336.767 322.325 353.132C335.066 371.05 347.555 389.123 358.709 407.91C367.559 422.251 376.239 436.78 387.519 449.723Z" fill="#793D61"/>
</g>
<g opacity="0.2" filter="url(#filter2_if_2490_39340)">
<path d="M771.854 441.152C759.829 436.415 747.261 430.19 741.249 419.395C733.412 404.113 733.559 387.023 732.152 370.631C730.455 341.9 728.034 312.592 715.951 285.535C701.712 252.76 677.871 224.11 661.892 192.057C653.761 175.712 649.371 157.396 653.215 139.657C658.019 114.816 675.411 91.3476 701.29 79.6752C720.323 71.4749 742.342 71.4703 762.905 74.0844C796.287 78.3662 828.047 88.9488 859.064 100.19C884.187 109.513 908.844 119.818 934.397 128.235C936.614 128.922 939.883 129.99 942.511 130.807C964.505 137.761 987.461 142.097 1010.62 144.708C1035.96 147.809 1061.64 149.693 1086.38 155.637C1100.72 159.22 1115.57 164.667 1124.57 175.6C1133.79 186.8 1137.63 200.69 1139.02 214.254C1141.64 243.039 1133.99 271.862 1121.44 298.336C1110.6 321.504 1097.55 343.802 1085.44 366.467C1079.52 377.545 1073.63 389.259 1068.09 400.732C1056.28 424.948 1045.31 449.844 1027.9 471.512C1020.08 480.807 1010.11 489.798 996.881 492.638C986.409 494.853 975.428 494.153 964.86 492.821C942.35 489.481 920.701 482.883 899.069 476.667C875.274 469.575 851.382 462.704 827.102 456.984C808.466 452.357 789.673 447.892 771.854 441.152Z" fill="#3D6E79"/>
</g>
<defs>
<filter id="filter0_if_2490_39340" x="0.734848" y="25.7517" width="586.088" height="469.245" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.36818"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2490_39340"/>
<feGaussianBlur stdDeviation="29.4727" result="effect2_foregroundBlur_2490_39340"/>
</filter>
<filter id="filter1_if_2490_39340" x="227.6" y="0.0527573" width="673.79" height="527.011" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.27852"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2490_39340"/>
<feGaussianBlur stdDeviation="29.1141" result="effect2_foregroundBlur_2490_39340"/>
</filter>
<filter id="filter2_if_2490_39340" x="591.018" y="11.7519" width="609.301" height="543.102" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.59845"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2490_39340"/>
<feGaussianBlur stdDeviation="30.3938" result="effect2_foregroundBlur_2490_39340"/>
</filter>
</defs>
</svg>
