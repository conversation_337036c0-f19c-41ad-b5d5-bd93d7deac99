<?xml version="1.0" encoding="UTF-8"?>
<svg width="643" height="247" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 640 248">
  <defs>
    <style>
      .cls-1 {
        fill: url(#radial-gradient-2);
      }

      .cls-1, .cls-2, .cls-3, .cls-4, .cls-5, .cls-6, .cls-7, .cls-8, .cls-9, .cls-10, .cls-11, .cls-12, .cls-13, .cls-14, .cls-15, .cls-16, .cls-17, .cls-18, .cls-19, .cls-20, .cls-21, .cls-22, .cls-23, .cls-24, .cls-25, .cls-26, .cls-27, .cls-28, .cls-29, .cls-30, .cls-31, .cls-32, .cls-33, .cls-34, .cls-35, .cls-36, .cls-37, .cls-38, .cls-39 {
        stroke-width: 0px;
      }

      .cls-1, .cls-20, .cls-21, .cls-23, .cls-24, .cls-40, .cls-39 {
        isolation: isolate;
      }

      .cls-1, .cls-23, .cls-24 {
        mix-blend-mode: screen;
      }

      .cls-2 {
        fill: #a6a6a6;
      }

      .cls-41 {
        mix-blend-mode: luminosity;
      }

      .cls-42 {
        clip-path: url(#clippath);
      }

      .cls-3 {
        fill: none;
      }

      .cls-43 {
        mask: url(#mask);
      }

      .cls-44, .cls-20 {
        opacity: .1;
      }

      .cls-4 {
        fill: url(#linear-gradient);
      }

      .cls-5 {
        fill: url(#linear-gradient-25);
      }

      .cls-6 {
        fill: url(#linear-gradient-12);
      }

      .cls-7 {
        fill: url(#linear-gradient-13);
      }

      .cls-8 {
        fill: url(#linear-gradient-10);
      }

      .cls-9 {
        fill: url(#linear-gradient-17);
      }

      .cls-10 {
        fill: url(#linear-gradient-16);
      }

      .cls-11 {
        fill: url(#linear-gradient-19);
      }

      .cls-12 {
        fill: url(#linear-gradient-15);
      }

      .cls-13 {
        fill: url(#linear-gradient-23);
      }

      .cls-14 {
        fill: url(#linear-gradient-21);
      }

      .cls-15 {
        fill: url(#linear-gradient-18);
      }

      .cls-16 {
        fill: url(#linear-gradient-14);
      }

      .cls-17 {
        fill: url(#linear-gradient-22);
      }

      .cls-18 {
        fill: url(#linear-gradient-20);
      }

      .cls-19 {
        fill: url(#linear-gradient-24);
      }

      .cls-20, .cls-36 {
        fill: #fff;
      }

      .cls-21 {
        fill: #eeeef0;
      }

      .cls-21, .cls-45 {
        opacity: .3;
      }

      .cls-46 {
        clip-path: url(#clippath-1);
      }

      .cls-22 {
        fill: #000;
      }

      .cls-23 {
        fill: url(#linear-gradient-4);
      }

      .cls-24 {
        fill: url(#linear-gradient-3);
      }

      .cls-25 {
        fill: url(#radial-gradient-3);
      }

      .cls-26 {
        fill: #7d7d7d;
      }

      .cls-27 {
        fill: url(#linear-gradient-11);
        fill-rule: evenodd;
      }

      .cls-47 {
        opacity: .2;
      }

      .cls-48 {
        opacity: .4;
      }

      .cls-49, .cls-39 {
        opacity: .9;
      }

      .cls-50 {
        opacity: .8;
      }

      .cls-28 {
        fill: #363636;
      }

      .cls-29 {
        fill: url(#linear-gradient-2);
      }

      .cls-30 {
        fill: url(#linear-gradient-8);
      }

      .cls-31 {
        fill: url(#linear-gradient-9);
      }

      .cls-32 {
        fill: url(#linear-gradient-7);
      }

      .cls-33 {
        fill: url(#linear-gradient-5);
      }

      .cls-34 {
        fill: url(#linear-gradient-6);
      }

      .cls-35 {
        fill: #a3a3a3;
      }

      .cls-37 {
        fill: #616161;
      }

      .cls-38 {
        fill: #606060;
      }

      .cls-51 {
        mask: url(#mask-1);
      }

      .cls-39 {
        fill: url(#radial-gradient);
      }
    </style>
    <clipPath id="clippath">
      <rect class="cls-3" x="-3.7" y="35" width="367.1" height="213"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="112.1" y1="182.8" x2="264.3" y2="40.9" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1b91f3"/>
      <stop offset="1" stop-color="#0b68cb"/>
    </linearGradient>
    <radialGradient id="radial-gradient" cx="-76.9" cy="425" fx="-76.9" fy="425" r="1" gradientTransform="translate(-31185.4 21370.6) rotate(66.5) scale(91.3 -87.4)" gradientUnits="userSpaceOnUse">
      <stop offset=".5" stop-color="#0b4186" stop-opacity="0"/>
      <stop offset="1" stop-color="#0b4186" stop-opacity=".4"/>
    </radialGradient>
    <radialGradient id="radial-gradient-2" cx="-88.6" cy="426.4" fx="-88.6" fy="426.4" r="1" gradientTransform="translate(9963.2 -9750) rotate(-128) scale(19.5 -32.3)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ef3acc" stop-opacity="0"/>
      <stop offset="1" stop-color="#ef3acc" stop-opacity=".6"/>
    </radialGradient>
    <linearGradient id="linear-gradient-2" x1="149.3" y1="134.6" x2="204" y2="195.5" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f5db0"/>
      <stop offset="1" stop-color="#0f5db0" stop-opacity="0"/>
    </linearGradient>
    <radialGradient id="radial-gradient-3" cx="-79.9" cy="424.2" fx="-79.9" fy="424.2" r="1" gradientTransform="translate(72278.8 22352.8) rotate(-64.3) scale(142.4 -175.9)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#094188"/>
      <stop offset="1" stop-color="#0b4186" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="linear-gradient-3" x1="214" y1="104.8" x2="188.5" y2="42.3" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#e247c4" stop-opacity="0"/>
      <stop offset="1" stop-color="#e247c4" stop-opacity=".6"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="87.5" y1="150.3" x2="110" y2="60" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ef3acc"/>
      <stop offset="1" stop-color="#ef3acc" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="174.2" y1="137.3" x2="174.2" y2="46" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0d2241"/>
      <stop offset=".6" stop-color="#1c0c36"/>
      <stop offset="1" stop-color="#2e0b5b"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="174.2" y1="137.3" x2="174.2" y2="46" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff"/>
      <stop offset=".9" stop-color="#bee1fe"/>
      <stop offset="1" stop-color="#96cefd"/>
    </linearGradient>
    <mask id="mask" x="121.1" y="85.1" width="118.8" height="119.7" maskUnits="userSpaceOnUse">
      <g id="mask0_2490_46586" data-name="mask0 2490 46586">
        <path class="cls-34" d="M174.2,183.4c29.3,0,53.1-19.4,53.1-43.3s-23.8-43.3-53.1-43.3-53.1,16.3-53.1,43.9c0,42.8,45.2,67.4,83.7,63.6-2.9-.3-20.9-1.3-33.1-15.1-1.1-1.2-3-3.4-2.1-4.8.9-1.4,3.2-1,4.7-1h0Z"/>
      </g>
    </mask>
    <linearGradient id="linear-gradient-7" x1="133.9" y1="141" x2="244.1" y2="139.9" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="133.3" y1="80.7" x2="243.5" y2="79.6" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="133.8" y1="133.4" x2="244" y2="132.3" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="133.3" y1="76.6" x2="243.5" y2="75.5" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="133.6" y1="108.4" x2="243.8" y2="107.2" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="123.3" y1="114.1" x2="255.2" y2="112.4" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-13" x1="123.1" y1="102.4" x2="255" y2="100.6" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="125.2" y1="163.3" x2="284.1" y2="161.6" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="125.2" y1="158.1" x2="284" y2="156.4" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="124.1" y1="56.5" x2="282.9" y2="54.7" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-17" x1="124.1" y1="61.9" x2="283" y2="60.1" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="124.6" y1="105.3" x2="283.5" y2="103.6" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-19" x1="124.7" y1="111.6" x2="283.5" y2="109.9" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-20" x1="125.1" y1="150.3" x2="283.9" y2="148.6" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-21" x1="124.6" y1="108.5" x2="283.5" y2="106.7" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-22" x1="124.7" y1="116.7" x2="283.6" y2="115" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-23" x1="124" y1="51.3" x2="282.9" y2="49.5" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-24" x1="124.6" y1="109.2" x2="283.5" y2="107.4" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-25" x1="124.5" y1="99.8" x2="283.4" y2="98" gradientTransform="translate(0 250) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37adf9"/>
      <stop offset="1" stop-color="#ae55f7"/>
    </linearGradient>
    <clipPath id="clippath-1">
      <rect class="cls-3" x="396" y="4" width="119" height="119" transform="translate(364.8 514.4) rotate(-86.6)"/>
    </clipPath>
    <mask id="mask-1" x="358.7" y="-8.5" width="276.5" height="174.9" maskUnits="userSpaceOnUse">
      <g id="mask1_2490_46586" data-name="mask1 2490 46586">
        <circle class="cls-36" cx="455.5" cy="63.5" r="59.5"/>
      </g>
    </mask>
  </defs>
  <g class="cls-40">
    <g id="Layer_1" data-name="Layer 1">
      <g class="cls-42">
        <g class="cls-45">
          <path class="cls-4" d="M155.7,76h0c6.8-23.8,36.4-35.4,66.6-35.4s39.6,6.6,52.4,17.1c-7.7.4-15.2,1.9-22.4,4.5,10.4,3.9,19.3,9.8,25.9,17.1-5-.9-10-1.2-15.1-1.1,12.4,18,19.1,39.3,19,61.2,0,59.6-48.3,107.9-107.9,107.9s-107.9-49.1-107.9-107.9,1.2-18.9,3.6-27.9c.6-1.9,1.5-3.7,2.7-4.4,1.5-.8,2.8,1.6,3,2.4,1.6,5.9,3.7,11.7,6.3,17.2-.2-12.3,5-23.5,12.3-33.3,4.8-6.5,9.3-12.5,11.4-29.8.1-1.2,1.2-2,2.4-1.6,15.7,5.1,24.1,31.3,22.8,53.2,8.7,1.2,8.6-7.8,8.6-7.8-2.8-8.5-.9-24.4,16.3-31.5h0Z"/>
          <path class="cls-39" d="M278.6,111.9c2.6,59.4-46.2,110.6-105.7,110.6s-101.4-43.1-105.5-97.7c-.7,5.1-1.1,10.3-1.2,15.5.4,58.5,49.5,107.1,107.9,107.1s107.9-48.3,107.9-107.9-1.2-18.7-3.5-27.5Z"/>
          <path class="cls-1" d="M171.6,83.8c-1.2-2.1-6.5-5.1-8.9-5.6,8.9-28.4,54.1-37.2,81.8-32.2,11.5,2.1,25.9,8.4,30.2,11.6-12.8-10.5-31.6-17.1-52.4-17.1-30.2,0-59.8,11.6-66.6,35.4h0c-17.3,7.1-19.1,23-16.3,31.5,2.7-10.2,15.3-22.7,32.2-23.6Z"/>
          <path class="cls-29" d="M202.9,64.2c-24.2,4.8-32.2,6.3-40.3,14,9.1-24.1,32.4-29,60.1-18-6.6,1.4-13.2,2.7-19.8,4h0Z"/>
          <path class="cls-25" d="M72.1,108.2c-6.6,27.1-1.5,59,28.6,85.7-9-9.8-19.9-46,4.2-71.8,1.6-1.7,4.4-.5,4.5,1.9,2,53.7,45.3,86.5,95.3,80.4-15.5-.9-66.7-18.8-28.6-25.9,19.9-3.7,51.1-9.5,51.1-37.5,0-45.4-35.1-58.7-56.4-56.7-14.6,1.4-27.5,10.6-31.5,23.2,1.5,5-4.6,8.4-8.6,7.8,1.3-21.9-7.1-48.1-22.8-53.2-1.1-.4-2.2.5-2.4,1.6-2.1,17.3-6.5,23.3-11.4,29.8-7.2,9.7-12.5,20.9-12.3,33.3-2.6-5.5-4.8-11.3-6.3-17.2-.2-.7-1.1-2.5-2.3-2.6-.6,0-1,.6-1.1,1.2Z"/>
          <path class="cls-24" d="M162.8,181.3c29.3,23.8,88.3,6,88.3-51.9-23.8,36.1-54.1,61-88.3,51.9h0Z"/>
          <path class="cls-23" d="M105,122.1c.3-.3.6-.5,1-.7.4-.1.8-.2,1.2-.1-21.6,26.4-4.2,72.6,7.8,84,.7,1.9-11.3-8-13-10.3-9.1-7.7-22.1-46,3.1-73Z"/>
          <path class="cls-33" d="M174.2,183.4c29.3,0,53.1-19.4,53.1-43.3s-23.8-43.3-53.1-43.3-53.1,16.3-53.1,43.9c0,42.8,45.2,67.4,83.7,63.6-2.9-.3-20.9-1.3-33.1-15.1-1.1-1.2-3-3.4-2.1-4.8.9-1.4,3.2-1,4.7-1h0Z"/>
          <g class="cls-43">
            <g>
              <g class="cls-48">
                <path class="cls-32" d="M183.6,103.1c.5.2.3,1-.3.9-7.6-1.5-15.8-.8-23.5,2.2-2.6,1-5.1,2.3-7.4,3.8,0,0-.1,0-.2,0-5.6,1.6-10.7,4.2-15.2,7.6-.7.5-1.5-.3-1-.9,4.5-5.7,10.5-10.3,17.7-13.2,10-3.9,20.6-3.8,29.9-.5Z"/>
                <path class="cls-30" d="M133.6,158.6c0-.8,1.1-.8,1.4-.1.3.9.6,1.8.9,2.6,3.1,7.8,8.2,14.1,14.5,18.7.5.3,0,1.1-.4.8-6.3-3.2-11.8-8-15.9-14.1,0,0,0-.2-.1-.3-.3-1.8-.4-3.6-.4-5.4s0-1.5,0-2.2Z"/>
                <path class="cls-31" d="M208.4,120.1c0-.1,0-.2-.1-.3-3.9-5.2-9-9.4-14.6-12.3-.5-.3-.9.5-.4.8,6.3,4.6,11.4,10.9,14.5,18.7.2.4.8.4.8-.1,0-1,.1-1.9.1-2.9s0-2.6-.2-3.8Z"/>
                <path class="cls-8" d="M200.2,170.4c0,0-.1,0-.2.1-4.3,4.8-9.7,8.8-16.1,11.3-7.8,3.1-15.9,3.7-23.5,2.2-.6-.1-.9.7-.3.9,9.4,3.4,19.9,3.5,29.9-.5,11.1-4.4,19.3-12.9,23.6-23.2.3-.7-.7-1.3-1.2-.7-3.5,3.9-7.6,7.2-12.1,9.7Z"/>
                <path class="cls-27" d="M172.2,156.2c7.8,0,14.1-6.3,14.1-14.1s-6.3-14.1-14.1-14.1-14.1,6.3-14.1,14.1,6.3,14.1,14.1,14.1ZM172.2,154.3c6.8,0,12.2-5.5,12.2-12.2s-5.5-12.2-12.2-12.2-12.2,5.5-12.2,12.2,5.5,12.2,12.2,12.2Z"/>
              </g>
              <g class="cls-49">
                <path class="cls-6" d="M151.8,161.2c-.8-.4-1.6.6-.9,1.1,7.1,5.4,16,8.7,25.7,8.7,23.4,0,42.4-19,42.4-42.4s-3.2-18.6-8.7-25.7c-.5-.7-1.5.1-1.1.9,3.2,6,5.1,12.8,5.1,20.1,0,23.4-19,42.4-42.4,42.4s-14.1-1.8-20.1-5.1Z"/>
                <path class="cls-7" d="M190.4,123.6c.8.4,1.6-.6.9-1.1-7.1-5.4-16-8.7-25.7-8.7-23.4,0-42.4,19-42.4,42.4s3.2,18.6,8.7,25.7c.5.7,1.5-.1,1.1-.9-3.2-6-5.1-12.8-5.1-20.1,0-23.4,19-42.4,42.4-42.4s14.1,1.8,20.1,5.1Z"/>
              </g>
              <g class="cls-50">
                <path class="cls-16" d="M152.9,87c0,1-.8,1.9-1.9,1.9s-1.9-.8-1.9-1.9.8-1.9,1.9-1.9,1.9.8,1.9,1.9Z"/>
                <path class="cls-12" d="M190.7,89.1c-.2.7-.5,1.4-.7,1.6-.2.2-.9.5-1.6.7-1,.3-2.1.7-2.1,1.2s1.1.9,2.1,1.2c.7.2,1.4.5,1.6.7.2.2.5.9.7,1.6.3,1,.7,2.1,1.2,2.1s.9-1.1,1.2-2.1c.2-.7.5-1.4.7-1.6.2-.2.9-.5,1.6-.7,1-.3,2.1-.7,2.1-1.2s-1.1-.9-2.1-1.2c-.7-.2-1.4-.5-1.6-.7-.2-.2-.5-.9-.7-1.6-.3-1-.7-2.1-1.2-2.1s-.9,1.1-1.2,2.1Z"/>
                <path class="cls-10" d="M148.8,190.3c-.2.7-.5,1.4-.7,1.6-.2.2-.9.5-1.6.7-1,.3-2.1.7-2.1,1.2s1.1.9,2.1,1.2c.7.2,1.4.5,1.6.7s.5.9.7,1.6c.3,1,.7,2.1,1.2,2.1s.9-1.1,1.2-2.1c.2-.7.5-1.4.7-1.6.2-.2.9-.5,1.6-.7,1-.3,2.1-.7,2.1-1.2s-1.1-.9-2.1-1.2c-.7-.2-1.4-.5-1.6-.7s-.5-.9-.7-1.6c-.3-1-.7-2.1-1.2-2.1s-.9,1.1-1.2,2.1Z"/>
                <path class="cls-9" d="M212.5,187.4c.2-.5.4-1.1.6-1.1s.4.6.6,1.1c.1.4.2.7.3.8.1.1.5.2.8.3.5.2,1.1.3,1.1.6s-.6.4-1.1.6c-.4.1-.7.2-.8.3-.1.1-.2.5-.3.8-.2.5-.4,1.1-.6,1.1s-.4-.6-.6-1.1c-.1-.4-.2-.7-.3-.8-.1-.1-.5-.2-.8-.3-.5-.2-1.1-.3-1.1-.6s.6-.4,1.1-.6c.4-.1.7-.2.8-.3.1-.1.2-.5.3-.8Z"/>
                <path class="cls-15" d="M141,143.1c-.1.4-.2.7-.3.8-.1.1-.5.2-.8.3-.5.2-1.1.3-1.1.6s.6.4,1.1.6c.4.1.7.2.8.3.1.1.2.5.3.8.2.5.3,1.1.6,1.1s.4-.6.6-1.1c.1-.4.2-.7.3-.8.1-.1.5-.2.8-.3.5-.2,1.1-.4,1.1-.6s-.6-.4-1.1-.6c-.4-.1-.7-.2-.8-.3-.1-.1-.2-.5-.3-.8-.2-.5-.4-1.1-.6-1.1s-.4.6-.6,1.1Z"/>
                <path class="cls-11" d="M201.2,137.5c.2-.5.4-1.1.6-1.1s.4.6.6,1.1c.1.4.2.7.3.8.1.1.5.2.8.3.5.2,1.1.3,1.1.6s-.6.4-1.1.6c-.4.1-.7.2-.8.3-.1.1-.2.5-.3.8-.2.5-.4,1.1-.6,1.1s-.4-.6-.6-1.1c-.1-.4-.2-.7-.3-.8-.1-.1-.5-.2-.8-.3-.5-.2-1.1-.3-1.1-.6s.6-.4,1.1-.6c.4-.1.7-.2.8-.3s.2-.5.3-.8Z"/>
                <path class="cls-18" d="M126.8,97.9c-.1.4-.2.7-.3.8-.1.1-.5.2-.8.3-.5.2-1.1.3-1.1.6s.6.4,1.1.6c.4.1.7.2.8.3.1.1.2.5.3.8.2.5.3,1.1.6,1.1s.4-.6.6-1.1c.1-.4.2-.7.3-.8.1-.1.5-.2.8-.3.5-.2,1.1-.3,1.1-.6s-.6-.4-1.1-.6c-.4-.1-.7-.2-.8-.3-.1-.1-.2-.5-.3-.8-.2-.5-.3-1.1-.6-1.1s-.4.6-.6,1.1Z"/>
                <path class="cls-14" d="M169.3,142c0,1.6,1.3,2.8,2.8,2.8s2.8-1.3,2.8-2.8-1.3-2.8-2.8-2.8-2.8,1.3-2.8,2.8Z"/>
                <path class="cls-17" d="M149.1,135.5c1,0,1.9-.8,1.9-1.9s-.8-1.9-1.9-1.9-1.9.8-1.9,1.9.8,1.9,1.9,1.9Z"/>
                <path class="cls-13" d="M190,201.3c1,0,1.9-.8,1.9-1.9s-.8-1.9-1.9-1.9-1.9.8-1.9,1.9.8,1.9,1.9,1.9Z"/>
                <path class="cls-19" d="M239.9,142c0,2.1-1.7,3.8-3.8,3.8s-3.8-1.7-3.8-3.8,1.7-3.8,3.8-3.8,3.8,1.7,3.8,3.8Z"/>
                <path class="cls-5" d="M193.8,152.9c1,0,1.9-.8,1.9-1.9s-.8-1.9-1.9-1.9-1.9.8-1.9,1.9.8,1.9,1.9,1.9Z"/>
              </g>
            </g>
          </g>
          <path class="cls-36" d="M181.7,75.1c5.7-1.8,5.2-7.4,5.2-7.4,0,0-2.8-3.3-8.5-1.5-5.3,1.7-6.1,5.5-6.1,5.5,0,0,2.9,5.5,9.4,3.4Z"/>
          <path class="cls-21" d="M91.8,98c.6-1.2,1.3-2.3,2-3.5C38.8,87.9,0,91.3-3.4,105.3c-5.3,21.3,72.5,59,173.9,84.4,101.4,25.3,187.8,28.6,193.2,7.3,3.5-14.1-29.5-35.4-81.6-55.6,0,1.2,0,2.3.1,3.5,41.9,16.4,68,32.9,65.4,43.5-4.3,17.1-81.4,12.5-172.2-10.2C84.4,155.5,14.2,123.2,18.5,106.2c2.6-10.2,31.2-12.7,73.3-8.2Z"/>
        </g>
      </g>
      <g class="cls-46">
        <g class="cls-47">
          <g class="cls-41">
            <g class="cls-51">
              <g>
                <path class="cls-35" d="M367.5,11.3l8.9-.4c8.9-.4,26.8-1.1,44.6-.6,17.8.5,35.5,2.1,53.3,4.1,17.7,2,35.4,4.2,53.1,6.8s35.3,5.4,53.2,5c17.9-.4,35.9-4.1,44.9-5.9l9-1.8.7-11-8.9-.5c-8.9-.5-26.7-1.6-44.4-2.7-17.8-1.1-35.5-2.1-53.3-3.2-17.8-1.1-35.5-2.1-53.3-3.2-17.8-1.1-35.5-2.1-53.3-3.2-17.8-1.1-35.5-2.1-44.4-2.7l-8.9-.5-1.2,19.8Z"/>
                <path class="cls-37" d="M365.9,38l8.7,2.9c8.7,2.9,26.2,8.7,43.8,13.9,17.5,5.2,35.1,9.8,52.9,10,17.8.2,35.9-4.1,53.9-6.3,18-2.2,35.8-2.3,53.6-1.2,17.8,1.1,35.5,3.3,44.3,4.5l8.9,1.1,2.7-45-9,1.8c-9,1.8-27.1,5.5-44.9,5.9-17.9.4-35.5-2.4-53.2-5-17.7-2.6-35.4-4.8-53.1-6.8-17.7-2-35.5-3.6-53.3-4.1-17.8-.5-35.7.2-44.6.6l-8.9.4-1.6,27.3Z"/>
                <path class="cls-26" d="M363.9,71.7h8.9c8.9-.1,26.8-.2,44.2,6.8,17.4,7,34.4,21.1,52.1,23.9,17.7,2.8,36-5.6,54.2-11.6,18.2-6,36.3-9.7,54-7.5,17.7,2.3,35,10.4,43.7,14.5l8.7,4.1,2.4-39.7-8.8-1.1c-8.9-1.1-26.6-3.4-44.3-4.5-17.8-1.1-35.6-1-53.6,1.2-18,2.2-36.1,6.5-53.9,6.3-17.8-.2-35.4-4.8-52.9-10-17.5-5.2-35-11-43.8-13.9l-8.7-2.9-2.1,34.4Z"/>
                <path class="cls-2" d="M362.4,96.6l9-1.2c9-1.2,27-3.7,44.5.9,17.6,4.6,34.7,16.4,52.4,18.6,17.7,2.3,36-5,54.2-11.3,18.2-6.3,36.4-11.8,54-8.3,17.6,3.4,34.7,15.8,43.3,21.9l8.5,6.2,1.3-21.9-8.7-4.1c-8.7-4.1-26-12.3-43.7-14.5-17.7-2.3-35.8,1.4-54,7.5-18.2,6-36.5,14.4-54.2,11.6-17.7-2.8-34.7-16.9-52.1-23.9-17.4-7-35.3-6.9-44.2-6.8h-8.9c0,0-1.5,25.5-1.5,25.5Z"/>
                <path class="cls-28" d="M361.4,112.6l9.1-2.4c9.1-2.4,27.2-7.3,44.8-4.1,17.6,3.1,34.8,14.3,52.5,16.5,17.7,2.3,35.9-4.4,54.1-10.4,18.2-6,36.4-11.5,54-7.8s34.7,16.7,43.2,23.1l8.5,6.5.7-11.3-8.5-6.2c-8.5-6.2-25.6-18.5-43.3-21.9-17.6-3.4-35.8,2-54,8.3-18.2,6.3-36.5,13.6-54.2,11.3-17.7-2.3-34.8-14-52.4-18.6-17.6-4.6-35.5-2.1-44.5-.9l-9,1.2-1,16.6Z"/>
                <path class="cls-38" d="M358.7,157l8.9.8c8.9.8,26.6,2.5,44.4,2.4,17.8-.1,35.8-2,53.6-.9s35.4,5.1,53.1,6.5c17.8,1.4,35.7,0,53.6-.9,17.9-1,35.8-1.7,44.7-2.1l8.9-.4,1.7-29-8.5-6.5c-8.5-6.5-25.6-19.4-43.2-23.1-17.6-3.7-35.8,1.7-54,7.8-18.2,6-36.4,12.7-54.1,10.4-17.7-2.3-34.9-13.4-52.5-16.5-17.6-3.1-35.8,1.7-44.8,4.1l-9.1,2.4-2.7,45Z"/>
              </g>
            </g>
          </g>
        </g>
      </g>
      <circle class="cls-20" cx="600.5" cy="208.5" r="39"/>
      <g class="cls-44">
        <path class="cls-22" d="M607.3,176c-.2,1.1-1.9,1.7-3.9,1.3-2-.3-3.4-1.5-3.2-2.6.2-1.1,1.9-1.7,3.9-1.3,2,.3,3.4,1.5,3.2,2.6Z"/>
        <path class="cls-22" d="M592.2,190.2c-.4,2-2.2,3.4-4.2,3.1-2-.3-3.3-2.3-2.9-4.3.4-2,2.2-3.4,4.2-3.1,2,.3,3.3,2.3,2.9,4.3Z"/>
        <path class="cls-22" d="M606.9,235.9c1.3.2,2.6-.7,2.9-2.1.2-1.4-.6-2.7-2-3-1.3-.2-2.6.7-2.9,2.1-.2,1.4.6,2.7,2,2.9Z"/>
        <path class="cls-22" d="M596.2,213.4c-.3,1.8-2.1,3.1-4,2.7-1.9-.3-3.2-2.1-2.8-3.9.3-1.8,2.1-3.1,4-2.7,1.9.3,3.2,2.1,2.8,3.9Z"/>
        <path class="cls-22" d="M614.5,216.9c1.5.3,2.9-.5,3.1-1.7.2-1.2-.8-2.4-2.4-2.6s-2.9.5-3.1,1.7c-.2,1.2.8,2.4,2.4,2.6Z"/>
        <path class="cls-22" d="M616,201.6c-.9.5-2.5-.4-3.5-2.1-1-1.7-1.1-3.6-.1-4.1.9-.5,2.5.4,3.5,2.1,1,1.7,1.1,3.6.1,4.1Z"/>
        <path class="cls-22" d="M629.6,212c1,1.7,2.6,2.7,3.5,2.1.9-.5.9-2.4-.1-4.1-1-1.7-2.6-2.7-3.5-2.1-.9.5-.9,2.4.1,4.1Z"/>
        <path class="cls-22" d="M572.8,194.8c1.1.2,1.6,2,1.1,3.9-.4,1.9-1.6,3.3-2.7,3.1-1.1-.2-1.6-2-1.1-3.9.4-1.9,1.6-3.3,2.7-3.1Z"/>
        <path class="cls-22" d="M588.3,234.4c-1.7-1.1-3.5-1.3-4.1-.4-.6.9.2,2.5,1.9,3.6,1.7,1.1,3.5,1.3,4.1.4.6-.9-.2-2.5-1.9-3.6Z"/>
      </g>
    </g>
  </g>
</svg>