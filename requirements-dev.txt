# This file is the base requirements for building. When you want to change any
# requirements, update this file and use
#
#   pip install -r requirements-dev.txt
#
# to test it. When you have the requirements how you like, run:
# 
#   pip-compile --generate-hashes -o requirements.txt requirements-dev.txt
#
# to generate the hashed requirements file used by the production build.
#
babel>=2.6.0
docopt>=0.6.2
feedparser>=5.2.1
flake8>=3.7.7
icalendar>=4.0.2
jinja2>=2.10.1,<=3.1.6
markdown>=2.6.11
markupsafe>=1.0
polib>=1.1.0
pyyaml>=5.3.1
watchdog>=0.9.0
webassets>=0.12.1
webob>=1.7.3
requests~=2.32.4
pytest~=7.3.1
python-dateutil~=2.8.2
packaging~=23.1
setuptools==78.1.1
