{# This Source Code Form is subject to the terms of the Mozilla Public
 # License, v. 2.0. If a copy of the MPL was not distributed with this
 # file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% extends "_base-resp.html" %}

{% set fru_form_id = fru_form_id|default('desktop') %}
{% set utm_campaign = utm_campaign|default('main') %}
{% set utm_medium = utm_medium|default('desktop') %}
{% set utm_source = utm_source|default('start_page_tb_beta') %}

{% block channel %}beta{% endblock %}
{% block title %}{{ _('Sharing Freedom – Thunderbird') }}{% endblock %}

{% block main %}
<div id="start-page">
{% block channel_link %}
  <a id="release-channel" href="#">{{ _('Beta Channel') }}</a>
{% endblock %}
<a id="donate-top" href="{{ url('thunderbird.bugzilla.new-bug') }}">
  {{ svg('start/bell-ring') }} {{ _('Report Issues') }}
</a>
  <header>
    <div id="logo" class="logo-img">{{ svg('logo-beta') }}</div>
    <h1>{{ _('Sharing <span class="gradient-text">Freedom</span>') }}</h1>
    <p class="tagline">{{ _('Thunderbird is a gift from thousands of people like you. By using our Beta release and reporting issues, you contribute to that gift of productivity, privacy, and freedom to the world. Thank you!') }}</p>
    <div class="btn-set">
      <a class="btn" href="{{ url('participate.test.desktop.beta') }}">{{ svg('start/docs') }} {{ _('Read Docs') }}</a>
      <a class="btn" href="{{ url('thunderbird.bugzilla.new-bug') }}">{{ svg('start/bell-ring') }} {{ _('Report Issues') }}</a>
      <a class="btn" href="{{ url('participate.discuss.desktop.beta') }}">{{ svg('chat') }} {{ _('Discuss') }}</a>
    </div>
  </header>

  <section class="features">
    <div class="feature-box">
      <h3>{{ _('Know the Risks First') }}</h3>
      <p class="subhead">{{ _('Thunderbird Beta is for testing.') }}</p>
      <p>{{ _('Thunderbird Beta is not intended for general use. It can be unstable and in rare cases result in data loss. We strongly recommend you read <a href="%(kb_url)s">the knowledge base article</a> before proceeding.') | format(kb_url=url('participate.test.desktop.beta')) }}</p>

    </div>
    <div class="feature-box">
      <h3>{{ _('Be Prepared') }}</h3>
      <ul>
        <li>{{ _('Regularly backup your beta profile to prevent data loss.') }}</li>
        <li>{{ _('Anticipate frequent updates and ensure proper installation into a beta-specific directory.') }}</li>
        <li>{{ _('If using mail filters, run them in only one profile.') }}</li>
        <li>{{ _('Be aware that some add-ons may not be compatible with beta.') }}</li>
        <li>{{ _('Avoid using POP accounts with beta. If necessary, configure to leave messages on the server indefinitely.') }}</li>
      </ul>
    </div>
  </section>

  <footer>
    {% block footer_content %}
      {{ _('<a href="%(donate_url)s"><strong>Donate</strong></a> to help fund the work to fix issues.') | format(donate_url=redirect_donate_url(form_id=fru_form_id, campaign=utm_campaign, medium=utm_medium, source=utm_source, content='link', location='thunderbird.donate.form.desktop', show_donation_modal=False)) }}
    {% endblock %}
  </footer>
</div>
{% endblock %}
