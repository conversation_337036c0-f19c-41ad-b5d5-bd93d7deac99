{% macro donate_button(form_id=settings.FRU_FORM_IDS['support'], content='cta', source='new_tab', medium='desktop', campaign='appeal', base_url=None, disable_donation_blocked_notice=False) %}
<a href="{{ donate_url(content=content, campaign=campaign, source=source, medium=medium, form_id=form_id, base_url=base_url) }}" class="donate-banner" {% if base_url != None %}target="_blank"{% endif %} data-donate-btn {% if disable_donation_blocked_notice %}data-dont-show-donation-blocked-notice{% endif %}>
  <div id="donate-banner-left">
    {{ _('Click here to <b>Donate!</b>') }}
  </div>
  <div id="decoration">
    <div id="pill-1" class="pill"></div>
    <div id="pill-2" class="pill"></div>
    <div id="pill-3" class="pill"></div>
    <div id="pill-4" class="pill"></div>
    <div id="pill-5" class="pill"></div>
    <div id="pill-6" class="pill"></div>
    <div id="pill-7" class="pill"></div>
    <div id="pill-8" class="pill"></div>
    <div id="pill-9" class="pill"></div>
    <div id="pill-10" class="pill"></div>
  </div>
  <div id="donate-banner-right" class="text-right accent-text self-end">
    {{ _('Help keep <b>Thunderbird Alive!</b>') }}
  </div>
  <div id="hover-hearts">
    <div id="hover-heart-1">{{ svg('donate-heart') }}</div>
    <div id="hover-heart-2">{{ svg('donate-heart') }}</div>
    <div id="hover-heart-3">{{ svg('donate-heart') }}</div>
  </div>
</a>
{% endmacro %}