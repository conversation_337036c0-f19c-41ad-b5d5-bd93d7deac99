{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}
{% set active_page = "index" %}
{% extends "includes/base/base.html" %}

{# Basic index page to help guide lost travelers to our real website. #}
{% block page_title %}{{ _('Hello!') }}{% endblock %}

{% block site_header %}
<nav class="site-nav">
  <div class="logo">
  <a href="https://www.thunderbird.net">
    {{ svg('logo-wordmark') }}
  </a>
  </div>
</nav>
{% endblock %}

{% block content %}
<section>
  <div class="container">
    <div class="section-text">
      <h1>{{ _('Hello!') }}</h1>
      <p>
        {% trans trimmed url='https://www.thunderbird.net' %}
          You're probably looking for <a href="{{ url }}">Thunderbird.net</a>.
        {% endtrans %}
      </p>
    </div>
  </div>
</section>
{% endblock %}
