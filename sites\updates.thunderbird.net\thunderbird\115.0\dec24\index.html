{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}
{#
 # This is the appeal page that will be loaded into Thunderbird directly.
 # Instead of the donation form it links to $url/donate which forces Thunderbird to open the page
 # in the user's preferred browser. This is hopefully less annoying for the end-user.
 #}
{% set active_page = "appeal-dec24" %}

{# For donation url generation #}
{% set fru_form_id = fru_form_id|default('eoy2024') %}
{% set utm_campaign = utm_campaign|default('dec24_appeal') %}
{% set utm_source = utm_source|default('thunderbird-client115') %}
{% set donation_base_url = donation_base_url|default(url('updates.115.appeal.dec24.donate')) %}

{# Include the 128 version rather than duplicating the code. #}
{% extends "thunderbird/128.0/dec24/index.html" %}
