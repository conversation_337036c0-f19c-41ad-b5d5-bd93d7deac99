{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}
{#
# This is the appeal page that will be loaded into Thunderbird directly.
# Instead of the donation form it links to $url/donate which forces Thunderbird to open the page
# in the user's preferred browser. This is hopefully less annoying for the end-user.
#}

{% set active_page = "appeal-apr25" %}
{% set use_new_mozilla_logo = True %}

{# For donation url generation #}
{% set fru_form_id = fru_form_id|default('apr25') %}
{% set utm_campaign = utm_campaign|default('apr25_appeal') %}
{% set utm_source = utm_source|default('thunderbird-client') %}
{% set donation_base_url = donation_base_url|default(url('updates.128.appeal.apr25.donate')) %}
{# Disable the donation banner on this redirect page, we set this to false in the actual donation page. #}
{% set disable_donation_blocked_notice = disable_donation_blocked_notice|default(True) %}

{% extends "includes/base/base.html" %}
{% from 'includes/macros/donate-button.html' import donate_button with context %}

{% block base_css %}
  <link href="{{ static('css/appeal-apr25-style.css') }}" rel="stylesheet" type="text/css"/>
{% endblock %}
{% block page_title %}{{ _('Only 3% of users support Thunderbird') }}{% endblock %}

{% block site_header %}
  <div id="appeal-header">
    <header>
      <h1 id="appeal-heading" aria-label="{{ _('Only 3% of users support Thunderbird.') }}">
        {{ _('Only 3% of users <span>Support Thunderbird</span>') }}
      </h1>
    </header>
    <aside id="illustration" aria-hidden="true">
      <div id="roc">
        {{ high_res_img('thunderbird/appeal/dec24/forest-roc.png', {'alt': _('')}, alt_formats=('webp', 'avif')) }}
      </div>
    </aside>
  </div>
{% endblock %}
{% block content %}
  <section id="donate-button-container">
    {{ donate_button(form_id=fru_form_id, campaign=utm_campaign, source=utm_source, base_url=donation_base_url, disable_donation_blocked_notice=disable_donation_blocked_notice) }}
  </section>
  <section id="appeal-body">
    <div class="letter-container font-xl">
    <p>
      {% trans trimmed %}
        In 2024, we soared to new heights, improving our technical infrastructure, boosting Thunderbird’s speed and responsiveness, and launching on Android. But we’re just getting started, with exciting plans for 2025.
      {% endtrans %}
    </p>
    <p>
      {% trans trimmed %}
        Will you help support the most powerful version of Thunderbird yet?
      {% endtrans %}
    </p>
    <div class="heart-container">
      <div class="left-lines">
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
      </div>
      <div aria-hidden="true" class="heart-svg">{{ svg('donate-heart') }}</div>
      <div class="right-lines">
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
      </div>
    </div>
    <p class="closing-text">{{ _('The Thunderbird Team') }}</p>
    </div>
  </section>
{% endblock %}
