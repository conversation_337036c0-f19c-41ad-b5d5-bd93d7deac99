{# This Source Code Form is subject to the terms of the Mozilla Public
#License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}
{#
 # This is an exact replicate of jun25/index.html but without the base_url set in donate_button.
 # This actually pulls up the form!
 #}
{% set active_page = "appeal-jun25" %}

{# For donation url generation #}
{% set fru_form_id = 'jun25' %}
{% set utm_campaign = 'jun25_appeal' %}
{% set donation_base_url = None %}
{# We want to show the redirect notice here as this is where the modal will actually open up. #}
{% set disable_donation_blocked_notice = False %}


{# Just include previous page instead of duplicating it all #}
{% extends "thunderbird/140.0/jun25a/index.html" %}
