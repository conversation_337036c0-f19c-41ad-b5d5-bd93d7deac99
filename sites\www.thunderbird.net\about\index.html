{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% set active_page = "who-we-are" %}
{% extends "includes/base/page.html" %}

{% block page_title %}{{ _('Who We Are') }}{% endblock %}
{% block category %}{{ _('About') }}{% endblock %}

{% block content %}
  <section id="meet-thunderbird">
    <div class="container">
      <div class="section-text">
        <h2>{{ _('Meet <span class="txt-gradient">Thunderbird</span>') }}</h2>
        <p>
          {% trans trimmed %}
            Thunderbird is a free and open-source software project founded in 2003 to make communicating and collaborating better.
            We are supported by the talent and generosity of thousands of individuals and part of the Mozilla family.
            Essential values guide us and make our work relevant. Read further to learn more.
          {% endtrans %}
        </p>
      </div>
      <div class="icon-grid-container">
        <div class="icon-grid">
          <div class="icon-item">
            {{ svg('base/icons/about/values') }}
            <p>{{ _('Values') }}</p>
          </div>
          <div class="icon-item">
            {{ svg('base/icons/about/community') }}
            <p>{{ _('Community') }}</p>
          </div>
          <div class="icon-item">
            {{ svg('base/icons/about/organization') }}
            <p>{{ _('Organization') }}</p>
          </div>
        </div>
      </div>
      <div class="back">
        <a class="strong" href="{{ url('thunderbird.about.our-mission-statement') }}">{{ _('Our Mission Statement') }}</a>
      </div>
    </div>
  </section>
  {% include 'includes/components/page-separator.html' %}
  <section id="guided-by-values">
    <div class="container">
      <div class="section-text">
        <h2>{{ _('Guided by <span class="txt-gradient">Values</span>') }}</h2>
      </div>
      <div class="value-section values-privacy">
        <div class="image">
          {{ high_res_img('thunderbird/base/about/privacy.png', {'alt': _('A shield with the Thunderbird logo.')}, alt_formats=('avif', 'webp')) }}
        </div>
        <div class="section-text tight">
          <h3>{{ _('Privacy') }}</h3>
          <h4>{{ _('You are not the product.') }}</h4>
          <p>{{ _('To us, data ownership and privacy are your right wherever you live. Our commitment to <strong>your personal data</strong> is simple:') }}</p>
          <ul class="styled-list">
            <li>{{ _('We do not collect or store it unless you ask us to.') }}</li>
            <li>{{ _('We take great care to keep it safe from misuse.') }}</li>
            <li>{{ _('We will never sell it.') }}</li>
            <li>{{ _('You retain ownership and control of it.') }}</li>
          </ul>
          <a class="strong" href="{{ url('thunderbird.privacy') }}">{{ _('Read the Privacy Policy') }}</a>
        </div>
      </div>
      <div class="value-section values-freedom">
        <div class="image">
          {{ high_res_img('thunderbird/base/about/freedom.png', {'alt': _('A key with the Thunderbird logo.')}, alt_formats=('avif', 'webp')) }}
        </div>
        <div class="section-text tight">
          <h3>{{ _('Freedom') }}</h3>
          <h4>{{ _('Thunderbird belongs to you (and the world).') }}</h4>
          <p>{{ _('Thunderbird is Free and Open-Source Software, which means its code is available to see, modify, use, and share freely. Its license also ensures that it will remain free forever. You can think of Thunderbird as a gift from thousands of contributors to you.') }}</p>
          <a class="strong" href="{{ url('mozorg.mpl2') }}">{{ _('Learn About MPL 2.0') }}</a>
        </div>
      </div>
      <div class="value-section values-voice">
        <div class="image">
          {{ high_res_img('thunderbird/base/about/voice.png', {'alt': _('A speech bubble with the Thunderbird logo.')}, alt_formats=('avif', 'webp')) }}
        </div>
        <div class="section-text tight">
          <h3>{{ _('Voice') }}</h3>
          <h4>{{ _('You have a part in Thunderbird\'s future.') }}</h4>
          <p>{{ _('Thunderbird is free from the commercial requirements and incentives that often guide software development. Anyone can get involved in helping make Thunderbird better and available to more people. A contributor-elected council ensures Thunderbird remains true to its values and mission.') }}</p>
        </div>
      </div>
    </div>
  </section>
  {% include 'includes/components/page-separator.html' %}
  <section id="driven-by-community">
    <div class="container">
      <div class="section-text">
        <h2>{{ _('Driven by <span class="txt-gradient">Community</span>') }}</h2>
        <p>
          {% trans trimmed %}
            Anyone can help make Thunderbird better.
            Programmers can contribute features they are passionate about or improve the existing code.
            Those who are multilingual can make Thunderbird accessible to everyone.
            Those willing can test new versions of Thunderbird and document issues.
            There are many ways to become part of a thriving community that makes Thunderbird unique.
            And if you don't have the time to volunteer, you can help support our work with a financial contribution.
          {% endtrans %}
        </p>
        <a class="strong" href="{{ url('thunderbird.participate') }}">{{ _('Learn How to Get Involved') }}</a>
      </div>
    </div>
  </section>
  <section class="cover-container">
    {{ high_res_img('thunderbird/base/about/contributors.png', {'alt': _('Our Contributors')}, alt_formats=('avif', 'webp')) }}
    {% include 'includes/components/page-separator-cover.html' %}
  </section>
  <section id="governed-by-contributors">
    <div class="container">
      <div class="section-text">
        <h2 aria-label="{{ _('Governed by Contributors') }}">{{ _('Governed by <span class="txt-gradient">Contributors</span>') }}</h2>
        <p>
          {% trans trimmed %}
            The Thunderbird Project is guided by the Thunderbird Council,
            an elected body functioning much like a board of directors for an open-source project. Established in 2014,
            this council is elected by contributors and acts as their voice in determining Thunderbird's strategic
            direction and goals. The Council oversees funds, approves budgets, and shapes the product roadmap,
            ensuring Thunderbird remains true to its core values.
          {% endtrans %}
        </p>
      </div>
      <div>
        <div class="job-grid">
          {# Re-using the job_block macro here #}
          {{ job_block(_('Council Chair'), 'Philipp Kewisch', high_res_img('thunderbird/staff/philipp_kewisch.png', scale='2x', alt_formats=('webp',)), True) }}
          {{ job_block(_('Council Secretary'), 'Patrick Cloke', high_res_img('thunderbird/staff/patrick_cloke.png', scale='2x', alt_formats=('webp',)), True) }}
          {{ job_block(_('Council Member'), 'Magnus Melin', high_res_img('thunderbird/staff/magnus_melin.png', scale='2x', alt_formats=('webp',)), True) }}
          {{ job_block(_('Council Member'), 'Michele Zelco', high_res_img('thunderbird/staff/michele_zelco.png', scale='2x', alt_formats=('webp',)), True) }}
          {{ job_block(_('Council Member'), 'Tim Maks van den Broek', high_res_img('thunderbird/staff/tim_macks_van_den_broek.png', scale='2x', alt_formats=('webp',)), True) }}
          {{ job_block(_('Council Member'), 'Teal Dulcet', high_res_img('thunderbird/staff/teal_dulcet.png', scale='2x', alt_formats=('webp',)), True) }}
        </div>
      </div>
      <div class="section-text">
      </div>
    </div>
  </section>
  {% include 'includes/components/page-separator.html' %}
  <section id="supported-by-mozilla">
    <div class="container">
      <div class="section-text">
        <h2>{{ _('Part of the <span class="txt-gradient">Mozilla Family</span>') }}</h2>
        <p>
          {% trans trimmed %}
            Thunderbird operates in a separate, for-profit subsidiary of the Mozilla Foundation.
            This structure gives us the flexibility to offer optional paid services to sustain Thunderbird’s development far into the future.
          {% endtrans %}
        </p>
        <p>
          {% trans trimmed %}
            We have a growing team of talented employees who develop and maintain Thunderbird, collaborate with our community and partners, and work to deliver Thunderbird to users around the world.
          {% endtrans %}
        </p>
      </div>
      <div>
        <div class="job-grid">
          {# Specific order #}
          {{ job_block(_('Managing Director, Operations'), 'Lisa McCormack', high_res_img('thunderbird/staff/lisa_mccormack.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Managing Director, Product'), 'Ryan Sipes', high_res_img('thunderbird/staff/ryan_sipes.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Director, Desktop & Mobile Apps'), 'Alessandro Castellani', high_res_img('thunderbird/staff/alessandro_castellani.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Director, Infrastructure & Services'), 'Andrei Hajdukewycz', high_res_img('thunderbird/staff/andrei_hajdukewycz.png', scale='2x', alt_formats=('webp',))) }}

          {# Alphabetical by first name #}
          {{ job_block(_('Sr. Software Engineer, Services'), 'Alejandro Aspinwall', high_res_img('thunderbird/staff/alejandro_aspinwall.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. UX Engineer, Desktop'), 'Alex Schmitz', high_res_img('thunderbird/staff/alexander_schmitz.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Finance & Accounting Specialist'), 'Anthony Macchia', high_res_img('thunderbird/staff/anthony_macchia.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Software Engineer, Mobile'), 'Ashley Soucar', high_res_img('thunderbird/staff/ashley_soucar.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Staff Software Engineer, Desktop'), 'Ben Campbell', high_res_img('thunderbird/staff/ben_campbell.png', scale='2x', optional_attributes={'class': 'pixel'})) }}
          {{ job_block(_('Sr. Software Engineer, Desktop'), 'Brendan Abolivier', high_res_img('thunderbird/staff/brendan_abolivier.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Software Engineer, Services'), 'Chris Aquino', high_res_img('thunderbird/staff/chris_aquino.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr Staff Engineer, Strategic Initiatives'), 'Chris Roth', high_res_img('thunderbird/staff/chris_roth.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Manager, Release Operations'), 'Corey Bryant', high_res_img('thunderbird/staff/corey_bryant.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. Release Engineer, Desktop'), 'Daniel Darnell', high_res_img('thunderbird/staff/daniel_darnell.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Software Engineer, Services'), 'Davi Nakano', high_res_img('thunderbird/staff/davi_nakano.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Software Engineer, Desktop'), 'Eleanor Dicharry', high_res_img('thunderbird/staff/eleanor_dicharry.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. Staff Software Engineer, Desktop'), 'Geoff Lankow', high_res_img('thunderbird/staff/geoff_lankow.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. Developer Relations Engineer'), 'Heather Ellsworth', high_res_img('thunderbird/staff/heather_ellsworth.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. UX Design Specialist'), 'Jesse Miksic', high_res_img('thunderbird/staff/jesse_miksic.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. Software Engineer, Desktop Add-ons'), 'John Bieling', high_res_img('thunderbird/staff/john_bieling.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. Security Engineer, Desktop'), 'Kai Engert', high_res_img('thunderbird/staff/kai_engert.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. Technical PM'), 'Kelly McSweeney', high_res_img('thunderbird/staff/kelly.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Manager, UI/UX Design Studio'), 'Laurel Terlesky', high_res_img('thunderbird/staff/laurel_terlesky.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Staff Software Engineer, Desktop'), 'Magnus Melin', high_res_img('thunderbird/staff/magnus_melin.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Manager, Web Services'), 'Malini Das', high_res_img('thunderbird/staff/malini_das.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Manager, People Operations & Strategy'), 'Margaret Baker ', high_res_img('thunderbird/staff/margaret_baker.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Staff Software Engineer, Desktop'), 'Martin Giger', high_res_img('thunderbird/staff/martin_giger.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Staff Software Engineer, Services'), 'Melissa Autumn', high_res_img('thunderbird/staff/melissa_autumn.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Communications & Engagement Coordinator'), 'Monica Ayhens-Madon', high_res_img('thunderbird/staff/monica_ayhens_madon.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Manager, Marketing & Communications'), 'Natalie Ivanova', high_res_img('thunderbird/staff/natalie_ivanova.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. Manager, Mobile Engineering'), 'Philipp Kewisch', high_res_img('thunderbird/staff/philipp_kewisch.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr Software Engineer, Mobile'), 'Rafael Tonholo', high_res_img('thunderbird/staff/rafael_tonholo.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Product Designer'), 'Rebecca Taylor', high_res_img('thunderbird/staff/rebecca_taylor.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. Software Engineer, Test'), 'Rob Wood', high_res_img('thunderbird/staff/rob_wood.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('User Support Specialist'), 'Roland Tanglao', high_res_img('thunderbird/staff/roland_tanglao.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. Site Reliability Engineer'), 'Ryan Jung', high_res_img('thunderbird/staff/ryan_jung.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Customer Experience Specialist'), 'Sarah Regenspan', high_res_img('thunderbird/staff/sarah_regenspan.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Associate Designer'), 'Solange Valverde', high_res_img('thunderbird/staff/solange_valverde.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Employee Experience Specialist'), 'Tarandeep Kaur', high_res_img('thunderbird/staff/tarandeep_kaur.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. Manager, Desktop Engineering'), 'Toby Pilling', high_res_img('thunderbird/staff/toby_pilling.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. Software Engineer, Mobile'), 'Todd Heasley', high_res_img('thunderbird/staff/todd_heasley.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Software Engineer, Desktop'), 'Vineet Deo', high_res_img('thunderbird/staff/vineet_deo.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Community Manager'), 'Wayne Mery', high_res_img('thunderbird/staff/wayne_mery.png', scale='2x', alt_formats=('webp',))) }}
          {{ job_block(_('Sr. Software Engineer, Mobile'), 'Wolf Montwé', high_res_img('thunderbird/staff/wolf_montwe.png', scale='2x', alt_formats=('webp',))) }}
        </div>
      </div>
      <div class="section-text">
        <a class="strong" href="{{ url('mozorg.careers.tb') }}">{{ _('Explore Career Opportunities') }}</a>
      </div>
    </div>
  </section>
{% endblock %}

{% macro job_block(title, name, img, is_council=False) %}
  {% set slug = name.replace(' ', '-')|lower %}
  {% set name = name.replace(' ', '\n') %}
  {% set title_lines = split_keep_delimiter(title, ',') %}
  <div class="person" id="{%- if is_council -%}council-{{ slug }}{%- else -%}staff-{{ slug }}{%- endif -%}">
    {{ img }}
    <h5>{{ name }}</h5>
    <div class="job-title">
      {% for line in title_lines %}
        <span>{{ line }}</span>
      {% endfor %}
    </div>
  </div>
{% endmacro %}
