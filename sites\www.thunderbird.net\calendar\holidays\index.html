{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% set active_page = "holiday-calendar" %}
{% extends "includes/base/page.html" %}

{% block page_title %}{{ _('Holiday Calendar') }}{% endblock %}
{% block category %}{{ _('Resources') }}{% endblock %}

{% block content %}
  <section id="all-holidays">
    <div class="section-text wide">
      <h2>{{ _('Add your nation’s holidays to <span class="txt-gradient">Calendar</span>!') }}</h2>
      <p>
        {{ _('We’ve got some holiday calendar files available for download. You can either download and then import them into Calendar or you can just subscribe to these calendars by copying their URL and then adding them as new remote calendar files. More information on installing a holiday calendar can be found in the <a href="%s">Adding a holiday calendar article</a>.')|format('https://support.mozilla.org/kb/adding-a-holiday-calendar') }}
      </p>
    </div>
    <div class="all-holiday-legend">
      <ul>
      {% for letter in letters -%}
          <li>
            <a href="#{{ letter }}">
              {{ letter }}
            </a>
          </li>
        {% endfor -%}
      </ul>
    </div>
  </section>
  <section id="holidays">
    <div class="holiday-container">
      {% set letter = namespace(first='') %}
      {% for calendar in calendars -%}
        {% if letter.first != calendar.country[:1] %}
          {% set letter.first = calendar.country[:1] %}
          <h4 id="{{ letter.first }}" class="products-letter-legend">
            {{ letter.first }}
          </h4>
        {% endif %}

        <div class="product-row">
            <div>
              {% if calendar.author == CALDATA_AUTOGEN_AUTHOR %}
                {{ _('<a href="%(url)s" class="inline-link text-blue">%(country)s</a>')|format(url=CALDATA_URL + calendar.filename, country=calendar.country ) }}
              {% else %}
                {{ _('<a href="%(url)s" class="inline-link text-blue">%(country)s</a> holidays thanks to %(authors)s')|format(url=CALDATA_URL + calendar.filename, country=calendar.country, authors=calendar.authors) }}
              {% endif %}
            </div>
            <div class="text-right p-2 font-sm uppercase font-semibold">
              {{ calendar.datespan }}
            </div>
        </div>
      {% endfor %}
    </div>
  </section>

{% endblock %}
