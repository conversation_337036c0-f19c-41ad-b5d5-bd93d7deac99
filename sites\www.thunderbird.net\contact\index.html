{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% set active_page = "contact-us" %}
{% extends "includes/base/page.html" %}

{% block page_title %}{{ _('Contact Us') }}{% endblock %}
{% block category %}{{ _('About') }}{% endblock %}

{% block content %}
<section id="need-to-talk">
  <div class="container no-gap">
    <div class="section-text">
      <h2>{{ _('Need to <span class="txt-gradient">Talk?</span>') }}</h2>
      <h4>{{ _('Direct your inquiries to the teams below.') }}</h4>
    </div>
    <div class="block-grid">
      {% call contact_block( svg('base/icons/contact/support'), _('Support'), [(url('support'), _('Support Website')), (url('thunderbird.donate.faq'), _('Donation Help'))]) %}
        {{ _('For questions about how to use Thunderbird, visit and search our Support Website. Feel free to submit a new question if you cannot find an answer. Please remember to be courteous to our staff and volunteers.') }}
        {% if not LANG.startswith('en-') %}
        <p>
          {% trans trimmed lang=translations[LANG], link=url('mozwiki.support-languages') %}
          To get the best support, please ask your question in English. If you prefer to ask a question in {{ lang }} you can try this non-Mozilla community forum <a class="dotted" href="{{ link }}">link</a>.
          {% endtrans %}
        </p>
        {% endif %}
      {% endcall %}
      {% call contact_block( svg('base/icons/contact/security'), _('Security'), [(url('thunderbird.bugzilla.new-bug'), _('Bugzilla'))]) %}
        {{ _('To report a software vulnerability, please submit an issue on Bugzilla with the security box checked. This checkmark will make the report visible only to our security team, who can address vulnerabilities before public disclosure.') }}
      {% endcall %}
      {% call contact_block( svg('base/icons/contact/media'), _('Media'), [('mailto:<EMAIL>', _('Outreach Team'))]) %}
        {{ _('Thunderbird and the people who make it are amazing. Obtain access to our team for your story, review, or other content by emailing our Outreach Team.') }}
      {% endcall %}
      {% call contact_block( svg('base/icons/contact/business'), _('Business'), [('mailto:<EMAIL>', _('Business Team'))]) %}
        {{ _('Organizations wanting to sponsor software development or obtain assistance in large-scale deployments should email our Business Team. This team also handles other partnership inquiries.') }}
      {% endcall %}
      {% call contact_block( svg('base/icons/contact/contributors'), _('Participate'), [(url('thunderbird.participate'), _('Participate Page'))]) %}
        {{ _('If you have talents you\'re interested in sharing but don\'t know where to start, learn how to get started by visiting our Participate page. Here you\'ll find the volunteer community that matches your interests.') }}
      {% endcall %}
      {% call contact_block( svg('base/icons/contact/legal'), _('Legal'), [(url('legal.trademark'), _('Trademark Misuse')), (url('legal.infringement'), _('DMCA Notice'))]) %}
        {{ _('Protect future users from malware, data mining, or being fraudulently charged for Thunderbird by reporting trademark misuse. If you believe we are infringing on your copyright or trademark, please send a DMCA notice.') }}
      {% endcall %}
    </div>
  </div>
</section>
{% endblock %}

{% macro contact_block(icon, title, links) %}
{# Generic block
:param icon: A variable containing pre-formatted html (e.g. the result of svg() or image())
:param title: The title of the block
:param links: A list of tuples that link to relevant locations. First entry is the url/href value, second entry is the link text.
:caller: The text body of the block
#}
<div class="block">
  <div class="block-head">
    <span>{{ icon }}</span>
    <h2>{{ title }}</h2>
  </div>
  <div class="block-body">
    <p>{{ caller() }}</p>
  </div>
  {% for link in links %}
  <a class="strong" href="{{ link[0] }}">{{ link[1] }}</a>
  {% endfor %}
</div>
{% endmacro %}
