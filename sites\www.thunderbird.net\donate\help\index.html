{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% extends "includes/base/page.html" %}

{% set active_page = "donations-help" %}
{% block page_title %}{{ _('Donation Help') }}{% endblock %}
{% block category %}{{ _('Contribute') }}{% endblock %}

{# Here be dragons, and way too much autogenerated html and css.
# This form is modified through an external tool and lovingly placed into thunderbird-website. -#}

{% block extra_meta %}
  {% include 'includes/donor-contact/fa-meta.html' %}
{% endblock %}
{% block extrahead %}
  {% include 'includes/donor-contact/fa-head.html' %}
{% endblock %}

{% block content %}
  <section id="donate">
    <div class="container">
      {% if settings.DONOR_HELP_SLOW_WARNING %}
        <div id="warning" class="warning-panel">
          <p>
            <b>
              {% trans trimmed %}
                Our Team is experiencing a backlog in donor inquiries. Please expect a delay in our response time to your inquiry.
              {% endtrans %}
            </b>
            {% trans trimmed %}
              We appreciate your understanding and thank you in advance for your support!
            {% endtrans %}
          </p>
        </div>
      {% endif %}
      <div class="section-text">
        <p class="intro">
          {% trans trimmed faq=url('thunderbird.donate.faq') %}
            If you need help donating to Thunderbird and did not find an answer in our <a href="{{ faq }}">FAQs</a>,
            please fill out this form and we will get back to you as soon as possible.
          {% endtrans %}
        </p>
        <p class="intro">
          {% trans trimmed modify=url('thunderbird.donate.modify') %}
            <strong>Use the <a href="{{ modify }}">Donor Portal</a> to access your receipts and update or cancel your recurring plan.</strong>
          {% endtrans %}
        </p>
        <p class="intro">
          {% trans trimmed support=url('support') %}
            Please note, our payment support team cannot assist with Thunderbird product questions or technical issues.
            <strong>For technical support, please visit our <a href="{{ support }}">support page</a></strong> where you will find helpful articles and community forums.
          {% endtrans %}
        </p>
      </div>
      <div class="fa-form section-text wide">
        {% include 'includes/donor-contact/fa-body.html' %}
      </div>
    </div>
  </section>
{% endblock %}
