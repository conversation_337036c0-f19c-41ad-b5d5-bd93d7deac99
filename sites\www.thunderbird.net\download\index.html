{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% set active_page = "download" %}
{% extends "includes/base/page.html" %}
{% from 'includes/download/macros/download-smart.html' import download_smart with context %}

{% block page_title %}{{ _('Download') }}{% endblock %}
{% block category %}{{ _('Resources') }}{% endblock %}

{% block content %}
<section>
  <div class="container">
    <div class="section-text">
      <h1>{{ _('Download <span class="txt-gradient">Thunderbird</span>') }}</h1>
      {{ download_smart('btn-gradient', 'download-button-products') }}
    </div>
  </div>
</section>
{% endblock %}
