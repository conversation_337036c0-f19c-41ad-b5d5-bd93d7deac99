{# This Source Code Form is subject to the terms of the Mozilla Public
 # License, v. 2.0. If a copy of the MPL was not distributed with this
 # file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% macro build_search_box(query, full_builds, test_builds) %}
  <form id="language-search" class="billboard" method="get">
    <div class="search-column">
      <input type="search"
             placeholder="{{ _('Search languages') }}"
             name="q"
             id="language-search-q"
             list="languages"
             {% if query %}value="{{ query }}"{% endif %}>
      <datalist id="languages">
        {% for build in full_builds -%}
          {% if build.name_en != build.name_native %}
            <option value="{{ build.name_en }}, {{ build.name_native }}"></option>
          {% else %}
            <option value="{{ build.name_en }}"></option>
          {% endif %}
        {% endfor -%}
        {% for build in test_builds -%}
          {% if build.name_en != build.name_native %}
            <option value="{{ build.name_en }}, {{ build.name_native }}"></option>
          {% else %}
            <option value="{{ build.name_en }}"></option>
          {% endif %}
        {% endfor -%}
      </datalist>
      <button class="form-button" type="submit"><span>{{ _('Search') }}</span></button>
    </div>
    {% if test_builds %}
      <div class="other-column">
        <h4>{{ _('Other Firefox downloads') }}</h4>
        <a href="#localized-testing">{{ _('New languages still in beta') }}</a>
      </div>
    {% endif %}
  </form>
{% endmacro %}

{% macro build_table(platforms, builds) %}
    {% if builds %}
      {% set letter = namespace(first='') %}
      {% for build in builds %}

        {% if letter.first != build.name_en[:1] %}
          {% set letter.first = build.name_en[:1] %}
          <h4 id="{{ letter.first }}" class="products-letter-legend">
            {{ letter.first }}
          </h4>
        {% endif %}

        {{ build_row(platforms, build) }}
      {% endfor %}
    {% endif %}
    <h4 class="subheader-section{% if builds %} hidden{% endif %}">
      {{ _('No matching languages found.') }}
    </h4>
{% endmacro %}

{% macro build_row(platforms, build) %}
  <div id="{{ build.locale }}" data-search="{{ build.name_en|lower }}, {{ build.name_native|lower }}" class="product-row">
    <h4>
      {{ build.name_en }}
      <small>
        {{ build.name_native }}
      </small>
    </h4>
    <div class="product-row-downloads">
      <div class="download-button download-button-page">
        {% for p_name, p_label in (platforms|list()) %}
          {{ build_button(build, p_name, _('Download for %(platform)s in %(locale)s')|format(platform=p_label|replace('\n', ' '), locale=build.name_en), _('%(platform)s')|format(platform=p_label|replace('\n', ' '))) }}
        {% endfor %}
      </div>
    </div>
  </div>
{% endmacro %}

{#
  build: Locale dictionary from product details.
  platform: Value in build.platforms to use. One of 'win', 'win64', 'osx', 'linux' and 'linux64'.
  tooltip: Text to display as tooltip for download link.
#}
{% macro build_button(build, platform, tooltip, text) %}
  {% if build.platforms[platform] %}
    <a href="{{ build.platforms[platform].download_url }}" title="{{ tooltip }}"
      class="btn btn-download-inline btn-download btn-slim btn-white-bg matomo-track-download"
      data-download-version="{{ platform }}"
      data-download-language="{{ build.name_en|lower}}"
      data-download-locale="{{ build.locale }}"
      data-link-type="download"
      {% if platform == 'android' %} data-download-os="Android"
      {% elif platform == 'ios' %} data-download-os="iOS"
      {% else %} data-download-os="Desktop"{% endif %}>
        <span style="width: 1rem; display: inline-block">{{ svg('download') }}</span> {{ text }}
    </a>
  {% endif %}
{% endmacro %}

{% macro build_link(build, platform, tooltip, text) %}
  {% if build.platforms[platform] %}
    <a href="{{ build.platforms[platform].download_url }}" title="{{ tooltip }}"
      class="btn-download-link btn btn-download btn-slim btn-white-bg matomo-track-download"
      data-download-version="{{ platform }}"
      data-download-language="{{ build.name_en|lower}}"
      data-download-locale="{{ build.locale }}"
      data-link-type="download"
      {% if platform == 'android' %} data-download-os="Android"
      {% elif platform == 'ios' %} data-download-os="iOS"
      {% else %} data-download-os="Desktop"{% endif %}>
        <span style="width: 1rem; display: inline-block">{{ svg('download') }}</span> {{ text }}
    </a>
  {% endif %}
{% endmacro %}
