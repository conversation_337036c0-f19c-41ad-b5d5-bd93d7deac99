{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. #}

{% set active_page = "release-notes" %}
{% extends "includes/base/page.html" %}

{% block page_title_prefix %}{% endblock %}
{% block page_title %}{{ _('{channel} Notes')|f(channel=channel_name) }}{% endblock %}
{% block category %}{{ _('Products') }} > {{ _('Thunderbird Desktop') }}{% endblock %}

{# channel_name is for display purposes where needed.  #}
{% set channel_name = channel_name|default('') %}
{% set show_bugs = channel_name == "Beta" or is_preview|default(False) %}

{% block content %}
<section>
  <div class="container release-text-container">
    <div class="section-text wide">
      <h2>{{ _('Thunderbird <span class="txt-gradient">Desktop</span>') }}</h2>
      <h4>{{ _('Version {version}'|f(version=release.version)) }} | {{ _('Released {release}'|f(release=release.release_date|l10n_format_date)) }}</h4>
      <p>
        {{ _('Check out the notes below for this version of Thunderbird. As always, you’re encouraged to
        <a href="%(feedback)s">tell us what you think</a>,
        <a href="%(question)s">ask for help</a>, or
        <a href="%(bugzilla)s">file a bug in Bugzilla</a>.')|format(feedback=url('mozorg.connect.tb'), question=url('support.question'), bugzilla=url('thunderbird.bugzilla.new-bug')) }}
      </p>
      <div class="release-text">
        {{ release.text|markdown|safe }}
      </div>
      <div class="system-requirements remove-h2">
        {% if is_system_requirements_dict() %}
          {% for platform, req in get_system_requirements_for_release_notes().items() %}
            {% set icon_name = platform|lower %}
              {% if platform == 'linux' %}
              {% set icon_name = 'linux' %}
              {% elif platform == 'mac' %}
              {% set icon_name = 'apple' %}
            {% endif %}
            {% set icon_path = 'base/icons/download/%(icon_name)s-dark'|format(icon_name=icon_name) %}
            {% call accordion(svg(icon_path), platform) %}
              {{ req|markdown|safe }}
            {% endcall %}
          {% endfor %}
        {% endif %}
      </div>
    </div>
  </div>
</section>
{% if notes %}
<section>
  <div class="container release-notes-container">
    <div class="section-text wide">

      {% set section = namespace(name='') %}
      {% set release_group = namespace(name='') %}
      {% for group in range(release.groups|length) %}
      {% if release.groups[group] and release.groups[group]|trim %}
      <h2 class="release-group">
        {{ release.groups[group] }}
      </h2>
      {% set section.name = "" %}
      {% endif %}

      {% for note in notes %}
      {% if note.get('group', 1)-1 == group and note.tag != 'unresolved' %}
      {% if note.tag == 'new' and section.name != note.tag %}
      <h3 id="whatsnew" class="header-section">
        {{ _('What’s New')}}
      </h3>
      {% elif note.tag == 'changed' and section.name != note.tag %}
      <h3 id="changes" class="header-section">
        {{ _('What’s Changed')}}
      </h3>
      {% elif note.tag == 'fixed' and section.name != note.tag %}
      <h3 id="fixes" class="header-section">
        {{ _('What’s Fixed')}}
      </h3>
      {% endif %}

      {% set section.name = note.tag %}
      {% set release_group.name = release.groups[group] %}

      <div id="note-{{ loop.index0 }}"
           class="note-container">
        <div class="note-flex">
          <h4 class="note-category">
            {% if note.tag == 'new' %}
            <div class="category-container">
              <span class="category-icon">{{ svg('bookmark') }}</span>
              {{ note.tag }}
            </div>
            {% elif note.tag == 'changed' %}
            <div class="category-container">
              <span class="category-icon">{{ svg('sync') }}</span>
              {{ note.tag }}
            </div>
            {% elif note.tag == 'fixed' %}
            <div class="category-container">
              <span class="category-icon">{{ svg('check') }}</span>
              {{ note.tag }}
            </div>
            {% endif %}
          </h4>
          <div class="note-text">
            {{ note.note|markdown|safe }}
          </div>
        </div>
        {% if show_bugs %}
        <div class="note-bug-numbers">
          {{ note.bug_links|markdown|safe }}
        </div>
        {% endif %}
      </div>
      {% endif %}
      {% endfor %}
      {% endfor %}

      {% if known_issues %}
      <h3 id="known-issues" class="header-section">
        {{ _('Known Issues')}}
      </h3>
      {% for note in known_issues %}
      <div id="note-{{ note.id }}" class="note-container">
        <div class="note-flex">
          <h4 class="note-category">
            <span class="category-container">
              <span class="category-icon">{{ svg('warning') }}</span>
              {{ note.tag }}
            </span>
          </h4>
          <div class="note-text">
            {{ note.note|markdown|safe }}
            {% if note.fixed_in_release %}
            <p class="note">
              <a href="{{ releasenotes_url(note.fixed_in_release) }}">
                {{ _('Resolved in v{version_number}')|f(version_number=note.fixed_in_release.version) }}
              </a>
            </p>
            {% endif %}
          </div>
        </div>
        {% if show_bugs %}
        <div class="note-bug-numbers">
          {{ note.bug_links|markdown|safe }}
        </div>
        {% endif %}
      </div>
      {% endfor %}
      {% endif %}
      <div class="see-all-releases">
        <a class="strong" href="{{ url('thunderbird.releases.index') }}">{{ _('See All Releases') }}</a>
      </div>
    </div>

  </div>
</section>

{% endif %}

{% endblock %}
{% macro accordion(icon, title) %}
{# Formatted accordion with inner contents being the html
:param icon: A variable containing pre-formatted html (e.g. the result of svg() or image())
:param title: The title of the accordion
:caller: The text body, shows up when you click on the title or accordion.
#}
{# Title hack, sorry. #}
{% if title == 'linux' %}
  {% set title = 'GNU/Linux' %}
{% endif %}
<details class="accordion">
  <summary class="question">
    <span>{{ icon }}</span>
    {{ title }}
  </summary>
  <div class="answer">
    {{ caller() }}
  </div>
</details>
{% endmacro %}
