{# This Source Code Form is subject to the terms of the Mozilla Public
 # License, v. 2.0. If a copy of the MPL was not distributed with this
 # file, You can obtain one at http://mozilla.org/MPL/2.0/. #}

{% set active_page = "system-requirements" %}
{% extends "includes/base/page.html" %}

{% block page_title_prefix %}{% endblock %}
{% block page_title %}{{ _('System Requirements') }}{% endblock %}
{% block category %}{{ _('Products') }} > {{ _('Thunderbird Desktop') }}{% endblock %}

{% block content %}
  <section class="section-text">
    <div class="container">
      <div class="section-heading">
        <h2>{{ _('Version <span class="txt-gradient">%(version)s</span>')|format(version=version) }}</h2>
      </div>
      <div class="release-notes-container">
        {% if is_system_requirements_dict() %}
          {% for platform, platform_text in release.system_requirements|items %}
            {{ platform_text|markdown|safe }}
          {% endfor %}
        {% else %}
            {{ release.system_requirements|markdown|safe }}
        {% endif %}
      </div>
    </div>
  </section>
{% endblock %}
