{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{# This is a sample page you can start with to create a basic Thunderbird.net page. It includes the navigation and footer automatically. #}

{% set active_page = "your-page-class-name" %}
{% extends "includes/base/page.html" %}

{% block page_title %}{{ _('Page Title') }}{% endblock %}
{% block category %}{{ _('Page Category Goes Here') }}{% endblock %}

{% block content %}
<section>
  <div class="container">
    <div class="section-text">
      <h2>{{ _('Basic Localized Title') }}</h2>
      <p>
        {{ _('Basic paragraph text') }}
      </p>
      <p>
        {% trans trimmed %}
          Basic paragraph text using the translation block tag
        {% endtrans %}
      </p>
    </div>
  </div>
</section>
{% endblock %}
