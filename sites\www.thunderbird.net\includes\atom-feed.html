<?xml version="1.0" encoding="UTF-8"?>
<feed
  xmlns="http://www.w3.org/2005/Atom"
  xmlns:thr="http://purl.org/syndication/thread/1.0"
  xml:lang="en-US"
>
  <id>{{ feed_url }}</id>
  <title>Thunderbird Release Notes</title>
  <subtitle type="text">Thunderbird Release Notes feed provides a simple way to keep up to date with Thunderbird releases.</subtitle>

  <updated>{{ NOW.isoformat() }}</updated>
  <logo>{{ feed_logo }}</logo>
  <icon>{{ feed_icon }}</icon>

  <link rel="alternate" type="text/html" href="{{ feed_index }}"/>
  <link rel="self" type="application/atom+xml" href="{{ feed_url }}"/>

  <generator uri="{{ settings.CANONICAL_URL }}" version="1.0">thunderbird-website</generator>
  {% for entry in entries %}
  <entry>
    <id>{{ entry.id }}</id>
    <title type="html">{{ entry.title }}</title>
    <author>
      <name>{{ entry.author }}</name>
      <uri>{{ settings.CANONICAL_URL }}</uri>
    </author>
    <link rel="alternate" type="text/html" href="{{ entry.url }}"/>
    <updated>{{ entry.updated }}</updated>
    <published>{{ entry.published }}</published>

    <content type="html"><![CDATA[
      {{ entry.content }}
      ]]>
    </content>
  </entry>
  {% endfor %}
</feed>
