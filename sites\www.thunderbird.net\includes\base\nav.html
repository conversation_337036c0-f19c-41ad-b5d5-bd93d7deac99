{% macro nav_entry(icon, title, description, location) %}
<li class="entry-container">
  <a href="{{ location }}">
    <div class="entry-icon" aria-hidden="true">
      {{ icon }}
    </div>
    <div class="entry-text">
      <h2 aria-label="{{ title }}.">{{ title }}</h2>
      <h6>{{ description }}</h6>
    </div>
  </a>
</li>
{% endmacro %}
<ul id="nav-menu" class="nav-links">
  <li class="nav-entry nav-expandable" tabindex="0">
    <button
      aria-controls="nav-cat-products"
      class="nav-ln"
    >
      {{_('Products')}}
    </button>
    <div id="nav-cat-products" class="popover-container">
      <ul class="popover-panel">
        {{ nav_entry(svg('base/icons/menu/desktop'), _('Thunderbird Desktop'), _('Meet the powerful email, calendar, and contacts app for Windows, Linux, and macOS.'), url('thunderbird.products.desktop')) }}
        {{ nav_entry(svg('base/icons/menu/mobile'), _('Thunderbird Mobile'), _('Learn about the leading open-source email app for Android.'), url('thunderbird.products.mobile')) }}
      </ul>
    </div>
  </li>
  <li class="nav-entry nav-expandable" tabindex="0">
    <button
      aria-controls="nav-cat-resources"
      class="nav-ln"
    >
      {{_('Resources')}}
    </button>
    <div id="nav-cat-resources" class="popover-container">
      <ul class="popover-panel">
        {{ nav_entry(svg('base/icons/menu/blog'), _('Blog'), _('Learn about new product releases, community events, tech tips, and more.'), url('blog')) }}
        {{ nav_entry(svg('base/icons/menu/desktop-help'), _('Desktop Help'), _('Find the help you need configuring and using Thunderbird Desktop.'), url('support')) }}
        {{ nav_entry(svg('base/icons/menu/mobile-help'), _('Mobile Help'), _('Find the help you need configuring and using Thunderbird Mobile.'), url('support.mobile')) }}
      </ul>
    </div>
  </li>
  <li class="nav-entry nav-expandable" tabindex="0">
    <button
      aria-controls="nav-cat-about"
      class="nav-ln"
    >
      {{_('About')}}
    </button>
    <div id="nav-cat-about" class="popover-container">
      <ul class="popover-panel">
        {{ nav_entry(svg('base/icons/menu/about'), _('Who We Are'), _('Learn how our values, community, and organization shape the future of Thunderbird.'), url('thunderbird.about')) }}
        {{ nav_entry(svg('base/icons/menu/contact'), _('Contact Us'), _('Find out who to contact and how.'), url('thunderbird.contact')) }}
        {{ nav_entry(svg('base/icons/menu/careers'), _('Careers'), _('We have an enthusiastic team with diverse talents. Learn how to join us.'), url('mozorg.careers.tb')) }}
      </ul>
    </div>
  </li>
  <li class="nav-entry nav-expandable" tabindex="0">
    <button
      aria-controls="nav-cat-contribute"
      class="nav-ln"
    >
      {{_('Contribute')}}
    </button>
    <div id="nav-cat-contribute" class="popover-container">
      <ul class="popover-panel">
        {{ nav_entry(svg('base/icons/menu/donations'), _('Donate'), _('Learn how to donate as an individual or business to support Thunderbird and open source.'), url('thunderbird.donate')) }}
        {{ nav_entry(svg('base/icons/menu/participate'), _('Participate'), _('We need helping hands of all kinds. Learn how you can help Thunderbird and others.'), url('thunderbird.participate')) }}
      </ul>
    </div>
  </li>
  <li class="nav-entry no-border no-desktop">
    <a
      href="{{ url('thunderbird.latest.all') }}"
      class="nav-ln"
    >
      {{_('Downloads')}}
    </a>
  </li>
  <li class="nav-entry no-border no-mobile nav-button-fix">
    <div class="header-download">
      <a href="{{ url('thunderbird.latest.all') }}" class="btn btn-no-bg" title="{{ _('Find the latest version of Thunderbird.') }}">
        {{_('Downloads')}}
      </a>
    </div>
  </li>
  <li class="nav-entry no-border no-mobile nav-button-fix">
    <div class="header-donate">
      <a data-donate-btn href="{{ donate_url('header', form_id=override_nav_donate_form_id|default(settings.FRU_FORM_IDS['support'])) }}" class="btn btn-no-bg" aria-label="{{ _('Donate') }}" title="{{ _('Donate') }}">
        <span aria-hidden="true">{{ svg('heart') }}</span>
      </a>
    </div>
  </li>
</ul>
