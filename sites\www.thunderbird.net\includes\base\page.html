{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% extends "includes/base/base.html" %}

{% if settings.SITE_ANNOUNCEMENT %}
{% block site_header_unwrapped %}
{% include 'includes/announcement.html' %}
{% endblock %}
{% endif %}

{% block site_nav %}
<nav class="site-nav">
  <div class="nav-container">
    <a href="{{ url('thunderbird.index') }}" class="logo" title="{{ _('Thunderbird') }}" aria-label="{{ _('Thunderbird Logo') }}">
      {{ svg('Thunderbird_Logo') }}
    </a>
    <button
      id="mobile-hamburger-button"
      class="hamburger-btn"
      aria-expanded="false"
      aria-controls="nav-menu"
      aria-label="{{ _('Open or close site navigation') }}"
      data-expanded="false"
      type="button"
    >
      <span class="hamburger-icon" aria-hidden="true">
        <span></span>
        <span></span>
        <span></span>
      </span>
    </button>
    {% include 'includes/base/nav.html' %}
  </div>
</nav>
<noscript>
  <!-- Provide a nav if they don't have javascript enabled. -->
  <div class="always-show-for-nojs-on-mobile">
    {% include 'includes/base/nav.html' %}
  </div>
</noscript>
{% endblock %}

{% block site_header %}
<header id="masthead">

  {% block breadcrumbs %}
  <div class="breadcrumbs" aria-hidden="true">
    {{_('Home')}} > {% block category %}{% endblock %} >
  </div>
  {% endblock %}

  {% block header_content %}
  <h1 class="tagline">
    {% block page_title %}{% endblock %}
  </h1>
  {% endblock %}

  {% include 'includes/donation-includes.html' %}

  {% block site_header_share %}{% endblock %}

  {% block header_separator %}
  <div class="header-separator" aria-hidden="true">
    {{ svg('base/separators/chevron-convex') }}
  </div>
  {% endblock %}

</header>
{% endblock %}

{% block post_content %}
<div class="pre-footer-cover-container">
  {% include 'includes/components/page-separator-cover.html' %}
</div>
<section id="whats-next">
  <div class="container">
    <div class="section-text wrap-balance">
      <h2>{{_('Learn What’s <span class="txt-gradient">Next</span>')}}</h2>
      <p>
        {% block post_content_copy %}
        {% trans trimmed %}
        Thunderbird keeps getting better. Subscribe to our newsletter and follow us on social media to stay
        informed.
        {% endtrans %}
        {% endblock %}
      </p>
    </div>
    <div class="newsletter">
      <!-- Begin Mailchimp Signup Form -->
      <form action="https://thunderbird.us12.list-manage.com/subscribe?u=f8051cc8637cf3ff79661f382&amp;id=56428f2efc"
            class="newsletter-form" method="post" id="mc-embedded-subscribe-form" name="mc-embedded-subscribe-form">
        <input id="mce-EMAIL" name="EMAIL" type="email" placeholder="{{ _('Your Email') }}" required="">
        <button type="submit" id="mc-embedded-subscribe" class="btn btn-no-bg btn-newsletter btn-newsletter-text">{{_('Subscribe')}}</button>
        <button type="submit" id="mc-embedded-subscribe" class="btn btn-no-bg btn-newsletter btn-newsletter-icon">➜</button>
        <!-- Prevent Bot signups -->
        <div style="position: absolute; left: -5000px;" aria-hidden="true"><input type="text" name="b_f8051cc8637cf3ff79661f382_9badb0cec2" tabindex="-1" value="">
        </div>
      </form>
      <!--End mc_embed_signup-->
    </div>
    <div class="social-list">
      <a class="icon" title="{{ _('Follow %(mastodon)s on Mastodon')|format(mastodon='@<EMAIL>') }}"
         href="{{ url('thunderbird.social.mastodon') }}" data-link-type="footer" data-link-name="Mastodon (Thunderbird)" rel="me">
        {{ svg('social-icon-mastodon') }}
      </a>
      <a class="icon" title="{{ _('Follow %(bluesky)s on Bluesky')|format(bluesky='@thunderbird.net') }}"
         href="{{ url('thunderbird.social.bluesky') }}" data-link-type="footer" data-link-name="Bluesky (Thunderbird)" rel="me">
        {{ svg('social-icon-bluesky') }}
      </a>
      <a class="icon" href="{{ url('thunderbird.social.facebook') }}" title="{{ _('Follow %(facebook)s on Facebook')|format(facebook='Thunderbird') }}"
         data-link-type="footer" data-link-name="Facebook (Thunderbird)">
        {{ svg('social-icon-facebook') }}
      </a>
      <a class="icon" title="{{ _('Follow %(youtube)s on YouTube')|format(youtube='@ThunderbirdProject') }}"
         href="{{ url('thunderbird.social.youtube') }}" data-link-type="footer" data-link-name="YouTube (Thunderbird)" rel="me">
        {{ svg('social-icon-youtube') }}
      </a>
      <a class="icon" title="{{ _('Follow %(linkedin)s on LinkedIn')|format(linkedin='thunderbird-email')}}"
         href="{{ url('thunderbird.social.linkedin') }}" data-link-type="footer" data-link-name="LinkedIn (Thunderbird)" rel="me">
        {{ svg('social-icon-linkedin') }}
      </a>
    </div>
    <div class="cta">
      <a href="{{ url('blog') }}" class="cta-ln">{{_('Visit Our Blog')}}</a>
    </div>
    <div class="phone" aria-hidden="true">
      <picture>
        <source srcset="{{ static('img/thunderbird/new/phone/cta-phone.avif') }}" type="image/avif">
        <source srcset="{{ static('img/thunderbird/new/phone/cta-phone.webp') }}" type="image/webp">
        <img src="{{ static('img/thunderbird/new/phone/cta-phone.png') }}" alt="{{_('smartphone')}}">
      </picture>
    </div>
  </div>
</section>
{% endblock %}

{% block js %}
<script>
  const navbar = document.querySelector(".site-nav");
  if (navbar) {
    const navbarClass = 'scrolled-nav';
    let navbarState = navbar.classList.contains(navbarClass);

    window.addEventListener("scroll", () => {
      if (!navbarState && window.scrollY > 30) {
        navbar.classList.add(navbarClass);
        navbarState = true;
      } else if (navbarState && window.scrollY <= 30) {
        navbar.classList.remove(navbarClass)
        navbarState = false;
      }
    });
  }
</script>
{% endblock %}
