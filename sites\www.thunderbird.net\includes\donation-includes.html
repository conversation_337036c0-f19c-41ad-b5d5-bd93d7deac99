{# This Source Code Form is subject to the terms of the Mozilla Public
 # License, v. 2.0. If a copy of the MPL was not distributed with this
 # file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% block additional_site_js %}
<script>
  (function(w,d,s,n,a){if(!w[n]){var l='call,catch,on,once,set,then,track'
  .split(','),i,o=function(n){return'function'==typeof n?o.l.push([arguments])&&o
  :function(){return o.l.push([n,arguments])&&o}},t=d.getElementsByTagName(s)[0],
  j=d.createElement(s);j.async=!0;j.src='https://cdn.fundraiseup.com/widget/'+a;
  t.parentNode.insertBefore(j,t);o.s=Date.now();o.v=4;o.h=w.location.href;o.l=[];
  for(i=0;i<7;i++)o[l[i]]=o(l[i]);w[n]=o}
  })(window,document,'script','FundraiseUp','ADGJGYAN');

  // Set FRU's language
  window._lang = "{{ get_fru_language() }}";
</script>
{% endblock %}