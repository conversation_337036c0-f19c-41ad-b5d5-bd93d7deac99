{# This Source Code Form is subject to the terms of the Mozilla Public
 # License, v. 2.0. If a copy of the MPL was not distributed with this
 # file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}
{% if not button_class %}
  {% set button_class = 'btn-'+channel if channel else 'btn-download' %}
{% endif %}
{% if not flex_class %}
  {% set flex_class = 'self-start' if channel or section == 'body' else 'justify-center' %}
{% endif %}


{% macro alt_buttons(builds) %}
  <div class="download download-dumb">
    <h4>{{ _('Download Thunderbird') }} — {{ locale_name|safe }}</h4>
    <ul class="list-none p-0 flex flex-wrap max-w-lg {{ flex_class }}">
      {% for plat in builds -%}
        <li class="ml-3 mr-3">
          <a href="{{ plat.download_link_direct or plat.download_link }}" class="matomo-track-download download-link {{ button_class }}" data-donate-redirect="download-{{ channel or default_channel }}" data-donate-content="post_download" data-donate-link="{{ redirect_donate_url(content='post_download', download=True, download_channel=channel or default_channel) }}">
            <span class="download-icon" aria-hidden="true">{{ svg('base/icons/download/generic-download-currentcolor') }}</span>
            {{ plat.os_arch_pretty or plat.os_pretty }}
          </a>
        </li>
      {%- endfor %}
    </ul>
  </div>
{% endmacro %}

{% set download_class = 'download-button' %}
{% set download_class = download_class ~ ' download-button-' ~ channel if channel else download_class %}

<div id="{{ id }}" class="{{ download_class }}">
  {% if not hide_footer_links %}
  {# All downloads link #}
  <div class="all-downloads-link">
    <a href="{{ url('thunderbird.latest.all') }}" class="small-link dotted">
    <div class="os-list" aria-label="{{ _('Available on Windows, Linux, Mac, and Android.') }}">
      <span class="icon" aria-hidden="true">
        {{ svg('download-icon-windows') }}
      </span>
      {% if section != 'homepage-hero' %}
        <span class="icon" aria-hidden="true">
        {{ svg('download-icon-linux') }}
        </span>
      {% else %}
        <span class="icon icon-tux-alt" aria-hidden="true">
        {{ svg('download-icon-linux') }}
        </span>
      {% endif %}
      <span class="icon icon-apple" aria-hidden="true">
        {{ svg('download-icon-apple') }}
      </span>
      <span class="icon icon-android" aria-hidden="true">
        {{ svg('base/icons/download/android') }}
      </span>
    </div>
    {{ _('Download and languages options') }}</a>
  </div>
  {% endif %}
  {# no JS #}
  {% if section != 'header' %}
  <div class="nojs-download">
    {{ alt_buttons(builds) }}
  </div>
  <div class="unrecognized-download os_android os_ios">
    {{ alt_buttons(builds) }}
  </div>
  <p class="unsupported-download p-links">
    {{ _("Your system doesn't meet the <a href=\"%(url)s\">requirements</a> to run Thunderbird.")|format(url=thunderbird_url('system-requirements', channel)) }}
  </p>
  <p class="linux-arm-download mb-8 p-links">
    {{ _('Please follow <a href="%(url)s">these instructions</a> to install Thunderbird.')|format(url='https://support.mozilla.org/kb/installing-thunderbird-linux') }}
  </p>
  {% endif %}
  <ul class="download-list list-none p-0 flex flex-wrap max-w-lg mb-0 {{ flex_class }}">
    {% for plat in builds %}
      <li class="os_{{ plat.os }}{% if plat.arch %} {{ plat.arch }}{% endif %}">
        <a class="matomo-track-download download-link {{ button_class }}"
          href="{{ plat.download_link }}"
          data-donate-redirect="download-{{ channel or default_channel }}"
          data-donate-content="post_download"
          data-donate-link="{{ redirect_donate_url(content='post_download', download=True, download_channel=channel or default_channel) }}"
          {% if plat.download_link_direct %}
            data-direct-link="{{ plat.download_link_direct }}"
          {% endif %}>
          <span class="download-icon" aria-hidden="true">{{ svg('base/icons/download/generic-download-currentcolor') }}</span>
          {% if alt_copy %}
            {{ alt_copy }}
          {% else %}
            {% if channel == 'beta' %}
              {{ _('Thunderbird Beta') }}
            {% elif channel == 'daily' %}
              {{ _('Thunderbird Daily') }}
            {% else %}
              {{ _('Download Thunderbird') }}
            {% endif %}
          {% endif %}
        </a>
      </li>
    {% endfor %}
  </ul>
  {% if not hide_footer_links %}
  <small>{{_('Free forever.')}} <a data-donate-btn class="dotted" href="{{ donate_url('header') }}">{{_('Donate')}}</a> {{_('to make it better.')}}</small>
  {% endif %}
</div>
