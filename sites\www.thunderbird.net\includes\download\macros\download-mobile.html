{# Internal macro, use download-block or download-smart instead! #}
{% macro download_mobile_btns(btn_class_name = 'btn-white-bg', container_class_name = 'download-button-page', show_donate_footer = False) -%}
<div>
  <div class="download-button {{ container_class_name }}">
  <a
    id="download-gplay-btn"
    class="btn btn-download btn-slim {{ btn_class_name }}"
    href="{{ download_url(platform_os='gplay', channel='mobile') }}"
    data-donate-redirect="download-{{ channel or 'esr' }}"
    data-donate-content="post_download"
    data-donate-link="{{ redirect_donate_url(content='post_download', download=True, download_channel=channel or 'esr', form_id=settings.FRU_FORM_IDS['tfa']) }}"
  >
    <span class="download-icon" aria-hidden="true">{{ svg('base/icons/download/googleplay-currentcolor') }}</span>
    {{ _('Google Play') }}
  </a>
  <a
    id="download-fdroid-btn"
    class="btn btn-download btn-slim {{ btn_class_name }}"
    href="{{ download_url(platform_os='fdroid', channel='mobile') }}"
    data-donate-redirect="download-{{ channel or 'esr' }}"
    data-donate-content="post_download"
    data-donate-link="{{ redirect_donate_url(content='post_download', download=True, download_channel=channel or 'esr', form_id=settings.FRU_FORM_IDS['tfa']) }}"
  >
    <span class="download-icon" aria-hidden="true">{{ svg('base/icons/download/fdroid-currentcolor') }}</span>
    {{ _('F-Droid') }}
  </a>
</div>
{% if show_donate_footer %}
<small>{{_('Free forever.')}} <a data-donate-btn class="dotted" href="{{ donate_url('header', form_id=settings.FRU_FORM_IDS['tfa']) }}">{{_('Donate')}}</a> {{_('to make it better.')}}</small>
{% endif %}
</div>
{%- endmacro %}