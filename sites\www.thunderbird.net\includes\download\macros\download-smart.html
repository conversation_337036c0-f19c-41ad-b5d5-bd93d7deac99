{# This is the reusable smart download button.
If javascript is not enabled it will show everything
otherwise it will selectively show based on platform and os.

Macros:
  download_smart
    :param btn_class: - Class for the download button (anchor tag fyi!)
    :param container_class: - Class for the container wrapping the download button
    :param force_platform: = 'all'|'desktop'|'android'
    :param desktop_channel: = 'esr'|'release'|'beta'|'daily' - Force the download button to show a specific channel
    :param hide_download_options: - Hide the download options link above the buttons
    :param hide_donate_footer: - Hide the donate footer below the buttons
#}
{% from 'includes/download/macros/download-desktop.html' import download_desktop_btns with context %}
{% from 'includes/download/macros/download-mobile.html' import download_mobile_btns with context %}
{% macro download_smart(btn_class = 'btn-gradient', container_class = 'download-button-products', force_platform='all', desktop_channel = settings.DEFAULT_RELEASE_VERSION, hide_download_options = False, hide_donate_footer = False, form_id=settings.FRU_FORM_IDS['support']) -%}
{# Note: force_platform = 'all' | 'android' | 'desktop' #}
{% if not hide_download_options %}
{% include 'includes/download/download-and-options.html' %}
{% endif %}
  {% if force_platform == 'all' %}
    <div data-download-platform="android">
      {{ download_mobile_btns(btn_class, container_class, False) }}
    </div>
    <div data-download-platform="desktop">
      {{ download_desktop_btns(desktop_channel, btn_class, container_class, False) }}
    </div>
    <div class="download-button" data-download-platform="other">
      <a href="{{ url('thunderbird.latest.all') }}" class="btn btn-download btn-slim {{ btn_class }}">
        {{ _('Downloads') }}
      </a>
    </div>
  {% elif force_platform == 'android' %}
    <div>
      {{ download_mobile_btns(btn_class, container_class, False) }}
    </div>
  {% elif force_platform == 'desktop' %}
    <div data-download-platform="desktop">
      {{ download_desktop_btns(desktop_channel, btn_class, container_class, False) }}
    </div>
    <div class="download-button" data-download-platform="other">
      <a href="{{ url('thunderbird.latest.all') }}" class="btn btn-download btn-slim {{ btn_class }}">
        {{ _('Downloads') }}
      </a>
    </div>
  {% endif %}
{% if not hide_donate_footer %}
<small>{{_('Free forever.')}} <a data-donate-btn class="dotted" href="{{ donate_url('header', form_id=form_id) }}">{{_('Donate')}}</a> {{_('to make it better.')}}</small>
{% endif %}
{% endmacro %}
