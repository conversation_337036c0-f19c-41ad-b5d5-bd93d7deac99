{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% set active_page = "participate" %}
{% extends "includes/base/page.html" %}

{% block page_title %}{{ _('Participate') }}{% endblock %}
{% block category %}{{ _('Contribute') }}{% endblock %}

{% block content %}
  <section id="we-need-your-talents">
    <div class="container no-gap">
      <div class="section-text">
        <h2>{{ _('We Need Your <span class="txt-gradient">Talents</span>') }}</h2>
        <h4>{{ _('Everyone can be a contributor. Here are some of the ways you can help.') }}</h4>
        <p>{{ _('Volunteers help create Thunderbird and ensure it is accessible throughout the world in dozens of languages. They write the documentation and answer the questions that help regular people use Thunderbird.') }}</p>
      </div>
      {% call accordion(svg('base/icons/participate/code'), _('Code')) %}
        <p>
          {% trans trimmed %}
            If you have programming experience there are a lot of ways to contribute to the Thunderbird project. Get started by identifying the area where you have the most experience and interest and following the steps to get started:
          {% endtrans %}
        </p>
        <div class="three-columns">
        <div>
          <h4>{{ _('Desktop Development:') }}</h4>
          <ul class="styled-list">
            <li>{{ _('Explore the <a href="%(url)s">docs</a>')|format(url=url('participate.desktop.docs')) }}</li>
            <li>{{ _('Join the <a href="%(url)s">repository</a>')|format(url=url('participate.desktop.repo')) }}</li>
            <li>{{ _('Say hello in <a href="%(url)s">Matrix</a>')|format(url=url('participate.desktop.matrix')) }}</li>
          </ul>
        </div>
        <div>
          <h4>{{ _('Android Development:') }}</h4>
          <ul class="styled-list">
            <li>{{ _('Explore the <a href="%(url)s">docs</a>')|format(url=url('participate.android.docs')) }}</li>
            <li>{{ _('Join the <a href="%(url)s">repository</a>')|format(url=url('participate.android.repo')) }}</li>
            <li>{{ _('Say hello in <a href="%(url)s">Matrix</a>')|format(url=url('participate.android.matrix')) }}</li>
          </ul>
        </div>
        <div>
          <h4>{{ _('Website Development:') }}</h4>
          <ul class="styled-list">
            <li>{{ _('Explore the <a href="%(url)s">docs</a>')|format(url=url('participate.website.docs')) }}</li>
            <li>{{ _('Join the <a href="%(url)s">repository</a>')|format(url=url('participate.website.repo')) }}</li>
            <li>{{ _('Say hello in <a href="%(url)s">Matrix</a>')|format(url=url('participate.website.matrix')) }}</li>
          </ul>
        </div>
      {% endcall %}
      {% call accordion(svg('base/icons/participate/test'), _('Test')) %}
        <p>{{ _('With so many unique hardware environments and workflows, community members who test pre-release builds of Thunderbird play a vital role in the quality of the software. Plus, you help catch bugs before they can reach the general public!') }}</p>
        <div class="two-columns">
          <div>
            <h4>{{ _('Desktop:') }}</h4>
            <ul class="styled-list">
              <li>{{ _('Learn about testing <a href="%(url)s">Beta</a>')|format(url=url('participate.test.desktop.beta')) }}</li>
              <li>{{ _('Report <a href="%(url)s">bugs</a>')|format(url=url('participate.test.desktop.bugs')) }}</li>
            </ul>
          </div>
          <div>
            <h4>{{ _('Android:') }}</h4>
            <ul class="styled-list">
              <li>{{ _('Learn about testing <a href="%(url)s">Beta</a>')|format(url=url('participate.test.android.beta')) }}</li>
              <li>{{ _('Report <a href="%(url)s">bugs</a>')|format(url=url('participate.test.android.bugs')) }}</li>
            </ul>
          </div>
        </div>
      {% endcall %}
      {% call accordion(svg('base/icons/participate/translate'), _('Translate')) %}
        <p>{{ _('The entire world deserves free and open source software, and Thunderbird is currently translated in more than 50 languages! Use your multilingual talents to help make Thunderbird more widely available than ever.') }}</p>
        <div class="two-columns">
          <div>
            <h4>{{ _('Desktop:') }}</h4>
            <ul class="styled-list">
              <li>{{ _('Explore the <a href="%(url)s">docs</a>')|format(url=url('participate.translate.desktop.docs')) }}</li>
              <li>{{ _('Join us on <a href="%(url)s">Pontoon</a>')|format(url=url('participate.translate.desktop.pontoon')) }}</li>
            </ul>
          </div>
          <div>
            <h4>{{ _('Android:') }}</h4>
            <ul class="styled-list">
              <li>{{ _('Explore the <a href="%(url)s">docs</a>')|format(url=url('participate.translate.android.docs')) }}</li>
              <li>{{ _('Join us on <a href="%(url)s">Weblate</a>')|format(url=url('participate.translate.android.weblate')) }}</li>
            </ul>
          </div>
        </div>
      {% endcall %}
      {% call accordion(svg('base/icons/participate/support'), _('Support')) %}
          <p>{{ _('Are you an experienced Thunderbird user who loves lending a helping hand? Put your knowledge to great use by joining our Support Crew and helping users around the world with their Thunderbird questions.') }}</p>
          <p>{{ _('You can join our Thunderbird Community Support Matrix room to get and give user support.') }}</p>
          <div class="two-columns">
            <div>
              <h4>{{ _('Desktop:') }}</h4>
              <ul class="styled-list">
                <li>{{ _('Say hello in <a href="%(url)s">Matrix</a>')|format(url=url('participate.support.desktop.matrix')) }}</li>
              </ul>
            </div>
            <div>
              <h4>{{ _('Android:') }}</h4>
              <ul class="styled-list">
                <li>{{ _('Say hello in <a href="%(url)s">Matrix</a>')|format(url=url('participate.support.android.matrix')) }}</li>
              </ul>
            </div>
          </div>
      {% endcall %}
      {% call accordion(svg('base/icons/participate/document'), _('Document')) %}
          <p>{{ _('Help ensure the answers are written down before the question is even asked! From documenting features or APIs to frequently asked user support questions, put your knowledge and writing skills to use for the benefit of the entire Thunderbird community.') }}</p>
          <div class="two-columns">
            <div>
              <h4>{{ _('Desktop:') }}</h4>
              <ul class="styled-list">
                <li>{{ _('User Support <a href="%(url)s">documentation</a>')|format(url=url('participate.document.desktop.user')) }}</li>
                <li>{{ _('Developer <a href="%(url)s">documentation</a>')|format(url=url('participate.document.desktop.dev')) }}</li>
                <li>{{ _('Code <a href="%(url)s">documentation</a>')|format(url=url('participate.document.desktop.code')) }}</li>
              </ul>
            </div>
            <div>
              <h4>{{ _('Android:') }}</h4>
              <ul class="styled-list">
                <li>{{ _('User Support <a href="%(url)s">documentation</a>')|format(url=url('participate.document.android.user')) }}</li>
              </ul>
            </div>
          </div>
      {% endcall %}
      {% call accordion(svg('base/icons/participate/promote'), _('Promote')) %}
        <div>
          <p>{{ _('Advocacy is one of the easiest ways to contribute to Thunderbird! Boosting our posts on Mastodon, showcasing the software to a friend, upvoting a helpful answer on Reddit; anything that adds visibility is a huge help. Here’s where to find us on various social platforms, and how to get involved.') }}</p>
          <ul class="styled-list">
            <li><a href="{{ url('thunderbird.social.reddit') }}">{{ _('Reddit') }}</a></li>
            <li><a href="{{ url('thunderbird.social.mastodon') }}">{{ _('Mastodon') }}</a></li>
            <li><a href="{{ url('thunderbird.social.bluesky') }}">{{ _('Bluesky') }}</a></li>
            <li><a href="{{ url('thunderbird.social.youtube') }}">{{ _('YouTube') }}</a></li>
            <li><a href="{{ url('participate.promote.tilvids') }}">{{ _('TILVids') }}</a></li>
            <li><a href="{{ url('thunderbird.social.linkedin') }}">{{ _('LinkedIn') }}</a></li>
            <li><a href="{{ url('participate.promote.android.review') }}">{{ _('Leave a review on the Google Play') }}</a></li>
          </ul>
        </div>
      {% endcall %}
      {% call accordion(svg('base/icons/participate/design'), _('Design')) %}
        <div>
          <p>{{ _('Share your ideas, and contribute to the ongoing conversations around Thunderbird’s UX and UI. We welcome your feedback to developer designs on our UX mailing list, and our companion Matrix room.') }}</p>
          <ul class="styled-list">
            <li>{{ _('Join in on the <a href="%(url)s">discussion</a>')|format(url=url('participate.design.topicbox')) }}</li>
            <li>{{ _('Say hello in <a href="%(url)s">Matrix</a>')|format(url=url('participate.design.matrix')) }}</li>
          </ul>
        </div>
      {% endcall %}
      <div class="section-text tight">
        <p>{{ _('One more way you can contribute is by sharing <strong>ideas</strong>, <strong>priorities</strong>, and <strong>feedback</strong>. Thunderbird is yours and we\'re listening.') }}</p>
        <a class="strong" href="{{ url('mozorg.connect.tb') }}">{{ _('Mozilla Connect') }}</a>
      </div>
      </div>
    </div>
  </section>
  {% include 'includes/components/page-separator.html' %}
  <section id="contributor-experience">
    <div class="container no-gap">
      <div class="section-text">
        <h2>{{ _('Contributor <span class="txt-gradient">Experience</span>') }}</h2>
        <h4>{{ _('The ways and reasons to get involved are as diverse as the people who do.') }}</h4>
        <p>{{ _('Our volunteers come from diverse backgrounds and bring unique talents. They\'re making Thunderbird better for themselves and better for the world at the same time. You can join them.') }}</p>
      </div>
    </div>
    <div class="container no-gap">
      <div class="testimonials">
        {% call testimonial_card('Berna', _('Leadership, Switzerland')) %}
          {{ _("Thunderbird is a testament to the power of the open-source community's contribution to driving innovation and efficiency.") }}
        {% endcall %}
        {% call testimonial_card('Arthur', _('Bug Triager, USA')) %}
          {{ _("If you don't have skin in the game, you can't complain about the direction the product is going in.") }}
        {% endcall %}
        {% call testimonial_card('Hartmut', _('Developer, Germany')) %}
          {{ _("It is great to be able to improve the software by fixing little things that affect me and other users") }}
        {% endcall %}
        {% call testimonial_card('Doug', _('Tester & Bug Triager, USA')) %}
          {{ _("I volunteer to use the beta versions as soon as they are built, report issues I find, and test fixes.") }}
        {% endcall %}
        {% call testimonial_card('Toad-Hall', _('Support & Bug Triager, United Kingdom')) %}
          {{ _("I volunteer to assist others in the Thunderbird Support Forum which I find a very rewarding experience.") }}
        {% endcall %}
        {% call testimonial_card('Bogo', _('Translator, Bug Triager & Leadership, Bulgaria')) %}
          {{ _("I began by contributing translations, thinking that people who speak my language deserve localized versions.") }}
        {% endcall %}
      </div>
    </div>
  </section>
  {% include 'includes/components/page-separator.html' %}
  <section id="shape-the-future">
    <div class="container no-gap">
      <div class="two-columns">
        <div class="section-text tight">
          <h2>{{ _('Shape the <span class="txt-gradient">Future</span>') }}</h2>
          <h4>{{ _('Improve software and lives by volunteering.') }}</h4>
          <p>{{ _('Craft tools you want to use. Help others find and use software that respects their privacy and freedom. Become part of a better future for yourself and others by putting your talents to work at Thunderbird.') }}</p>
        </div>
        <div class="image">
          {{ high_res_img('thunderbird/base/participate/gears.png', {'alt': _('Various gears all working in tandem with a heart, a code symbol, a magnifying glass displaying a very small bug, and the Thunderbird Logo in the centre of it all.')}, alt_formats=('avif', 'webp')) }}
        </div>
      </div>
    </div>
  </section>
{% endblock %}

{% macro accordion(icon, title) %}
  {# Formatted accordion with inner contents being the html
:param icon: A variable containing pre-formatted html (e.g. the result of svg() or image())
:param title: The title of the accordion
:caller: The text body, shows up when you click on the title or accordion.
#}
  {# This is here to account for the navbar height #}
  <div aria-hidden="true" class="anchor-link" id="{{ title|lower|replace(' ', '-') }}"></div>
  <details class="accordion">
    <summary class="question">
      <span>{{ icon }}</span>
      {{ title }}
    </summary>
    <div class="answer">
      {{ caller() }}
    </div>
  </details>
{% endmacro %}

{% macro testimonial_card(name, title) %}
  {# Generic block
:param icon: A variable containing pre-formatted html (e.g. the result of svg() or image())
:param title: The title of the block
:caller: The text body of the block
#}
  <div class="testimonial-card">
    <div class="quote">
      <q>
        {{ caller() }}
      </q>
      <div class="attrib no-photo">
        <div class="name">{{ name }}</div>
        <div class="title">{{ title }}</div>
      </div>
    </div>
  </div>
{% endmacro %}