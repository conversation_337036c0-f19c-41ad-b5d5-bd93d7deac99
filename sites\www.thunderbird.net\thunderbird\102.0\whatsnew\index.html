



<!doctype html>

<html class="windows x86 no-js" lang="{{ LANG|replace('en-US', 'en') }}" dir="{{ DIR }}">
  <head>
    <meta charset="utf-8">

    

    <meta name="viewport" content="width=device-width, initial-scale=1">
    

    <title>{{ _('What’s New in Thunderbird 102') }} — Thunderbird</title>
    <meta name="description" content="">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="{{ _('Thunderbird') }}">
    <meta property="og:locale" content="{{ LANG|replace("-", "_") }}">
    <meta property="og:image" content="https://www.thunderbird.net/media/img/thunderbird/thunderbird-256.png">
    <meta property="og:title" content="{{ _('What’s New in Thunderbird 102') }}">
    <meta property="og:description" content="">
    <meta property="fb:page_id" content="***************">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:domain" content="thunderbird.net">
    <link rel="apple-touch-icon" type="image/png" sizes="180x180" href="/media/archived/img/thunderbird/ios-icon-180.png">
    <link rel="icon" type="image/png" sizes="196x196" href="/media/archived/img/thunderbird/favicon-196.png">
    <link rel="shortcut icon" href="/media/archived/img/thunderbird/favicon.ico">
    
    <link href="/media/archived/css/normalize.css" rel="stylesheet" type="text/css" />
    <link href="/media/archived/css/thunderbird-style.css" rel="stylesheet" type="text/css" />

    

    <!--[if !lte IE 8]><!-->
    
    

    
    
    <!--<![endif]-->

    
      
    
    <script>
      window.siteLocale = "{{ LANG }}";
    </script>
  </head>

  <body class="html-ltr " >
    <div id="strings" ></div>

    
    

    
  <header id="masthead" class="bg-no-repeat bg-cover bg-top flex flex-col bg-header-page">

  <section class="text-left text-white flex justify-between pl-8 pr-8 md:pl-16 md:pr-16 max-w-7xl mx-auto pt-12" style="margin-bottom: -8rem;">
    <aside class="flex-col items-start">
      <h1 class="font-4xl md:font-hero font-thin mt-0 mb-8">{{ _('What’s New in Thunderbird 102') }}</h1>
      <p class="font-lg md:font-xl tracking-wide leading-relaxed max-w-4xl lg:mb-16 mb-8 mt-0 p-links">
        {{ _('Explore the new features and changes in the new release of Thunderbird.') }}
      </p>

      <div class="flex flex-col items-center mb-20 mx-auto xl:ml-0 xl:mr-0 max-w-xl bg-grey p-4 rounded shadow-lg text-black-light no-underline md:font-md">
        <p class="tracking-wide leading-normal m-0 text-center">
          {{ _('<strong>Thunderbird is completely funded by your donations.</strong> <br> Producing Thunderbird requires software engineers, designers, system administrators and server infrastructure. <strong>Help us keep Thunderbird alive!</strong>') }}
        </p>
        <a href="?form=support&utm_content=cta&utm_source=whats_new_page&utm_medium=thunderbird&utm_campaign=whats_new_102" class="btn-donate-lg mt-4" data-donate-btn data-donate-content="cta" data-donate-source="whats_new_page" data-donate-medium="thunderbird" data-donate-campaign="whats_new_102">
          <span class="inline-block w-5 mr-1"><svg viewBox="0 0 14 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="UI" stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd">
        <path d="M7.00000834,12.0000012 C6.8750082,12.0000012 6.75000805,11.9531261 6.65625793,11.859376 L1.78125212,7.15624542 C1.71875205,7.10155785 0,5.53124348 0,3.65624125 C0,1.36717602 1.39843917,-1.31130219e-05 3.73437945,-1.31130219e-05 C5.10156858,-1.31130219e-05 6.38282011,1.07811317 7.00000834,1.6874889 C7.61719658,1.07811317 8.89844811,-1.31130219e-05 10.2656372,-1.31130219e-05 C12.6015775,-1.31130219e-05 14.0000167,1.36717602 14.0000167,3.65624125 C14.0000167,5.53124348 12.2812646,7.10155785 12.2109521,7.17187044 L7.34375875,11.859376 C7.25000864,11.9531261 7.12500849,12.0000012 7.00000834,12.0000012 Z"></path>
    </g>
</svg>
</span>
          {{ _('Donate') }}
        </a>
      </div>
    </aside>
    <aside class="xl:flex hidden xl:ml-8 max-w-2xl">
      <img class="w-full h-auto max-w-2xl self-start shadow-img" src="/media/archived/img/thunderbird/whatsnew/102/102-header.png" alt="Screenshot of Thunderbird's new system dark mode.">
    </aside>

  </section>


    
  




<script>
  (function(w,d,s,n,a){if(!w[n]){var l='call,catch,on,once,set,then,track'
  .split(','),i,o=function(n){return'function'==typeof n?o.l.push([arguments])&&o
  :function(){return o.l.push([n,arguments])&&o}},t=d.getElementsByTagName(s)[0],
  j=d.createElement(s);j.async=!0;j.src='https://cdn.fundraiseup.com/widget/'+a;
  t.parentNode.insertBefore(j,t);o.s=Date.now();o.v=4;o.h=w.location.href;o.l=[];
  for(i=0;i<7;i++)o[l[i]]=o(l[i]);w[n]=o}
  })(window,document,'script','FundraiseUp','ADGJGYAN');

  // Set FRU's language
  window._lang = "{{ get_fru_language() }}";
</script>




<div id="modal-overlay" class="fixed pin-t pin-l m-0 p-0 w-full h-full z-20 bg-black opacity-50 hidden" tabindex="-1"></div>

    

    

  </header>


    
      
    

    <main>
      
  <!-- Message Header Redesign -->
  <section class="pt-20 pb-20 flex justify-center" style="margin-top: 8rem;">
    <div class="flex lg:flex-row flex-col flex-col-reverse flex-1 max-w-6xl ml-16 mr-16">
      <aside class="flex-1 lg:mr-8">
        <img class="w-full h-auto max-w-2xl self-center shadow-img" src="/media/archived/img/thunderbird/whatsnew/102/message-header.gif" alt="Screenshot of Thunderbird's new message header.">
      </aside>
      <aside class="flex-col flex-1 items-start">
        <h3 class="header-section">
          <span><!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><path fill="currentColor" d="M13 2H3C1.3 2 0 3.3 0 5v6c0 1.7 1.3 3 3 3h10c1.7 0 3-1.3 3-3V5c0-1.7-1.3-3-3-3zm0 2c.1 0 .2 0 .3.1h-.1L8 7.9 2.8 4.1h-.1c.1-.1.2-.1.3-.1h10zm1 7c0 .6-.4 1-1 1H3c-.6 0-1-.4-1-1V4.8c0 .1.1.1.2.1l5.5 4c.1.1.2.1.3.1.1 0 .2 0 .3-.1l5.5-4c.1 0 .1-.1.1-.2.1.1.1.2.1.3v6z"></path></svg>
</span>
          {{ _('Message Header Redesign')}}
        </h3>
        <p class="font-lg tracking-wider leading-loose mb-10">
          {{ _('Thunderbird 102 helps you be more productive with less effort. When reading your email, the redesigned message header allows you to focus on what matters as it highlights important information. It’s also more responsive and easier to navigate. Plus, you can star important messages from the message header itself, and easily convert them into a calendar event or a task.') }}
        </p>
      </aside>
    </div>
  </section>

  <!-- Address Book -->
  <section class="pt-20 pb-20 flex justify-center bg-grey-lighter">
    <div class="flex lg:flex-row flex-col flex-1 max-w-6xl ml-16 mr-16">
      <aside class="flex-col flex-1 items-start">
        <h3 class="header-section">
          <span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" height="24" width="24">
  <path d="M4.495 0a3 3 0 0 0-3 3v1.53c-1.98.165-1.98 2.76 0 2.94v3.06c-1.98.18-1.98 2.775 0 2.94v3.06c-1.98.18-1.98 2.76 0 2.94V21c0 1.65 1.35 3 3 3h16.5a3 3 0 0 0 3-3V3a3 3 0 0 0-3-3zm1.5 3h13.5a1.5 1.5 0 0 1 1.5 1.5v15a1.5 1.5 0 0 1-1.5 1.5h-13.5a1.5 1.5 0 0 1-1.5-1.5c2.25 0 2.25-3 0-3v-3c2.25 0 2.25-3 0-3v-3c2.25 0 2.25-3 0-3a1.5 1.5 0 0 1 1.5-1.5Zm13.5 15c0-4.65-1.02-5.04-4.11-5.76 3.375-1.905 1.98-7.05-1.89-6.99-3.795.03-5.16 5.04-1.89 6.975-3.09.78-4.11 1.305-4.11 5.775z"/>
</svg>
</span>
          {{ _('New Address Book')}}
        </h3>
        <p class="font-lg tracking-wider leading-loose mb-10">
          {{ _('Thunderbird 102 gives your contacts a serious upgrade! The refreshed design makes it easier to navigate and interact with your contacts, and helps you better understand who you’re communicating with. The new Address Book has compatibility with vCard specs, meaning if your app can export your existing contacts into vCard format, Thunderbird can import them. Each contact card also acts as a launchpad for messaging, email, and event creation.') }}
        </p>
      </aside>
      <aside class="flex-1 lg:ml-8">
        <img class="w-full h-auto max-w-2xl self-center shadow-img" src="/media/archived/img/thunderbird/whatsnew/102/addressbook.png" alt="Screenshot of Thunderbird's new addressbook.">
      </aside>
    </div>
  </section>

  <!-- Import Export Wizard -->
  <section class="pt-20 pb-20 flex justify-center">
    <div class="flex lg:flex-row flex-col flex-col-reverse flex-1 max-w-6xl ml-16 mr-16">
      <aside class="flex-1 lg:mr-8">
        <img class="w-full h-auto max-w-2xl self-center shadow-img" src="/media/archived/img/thunderbird/whatsnew/102/import-export.png" alt="Screenshot of the new import and export wizards in Thunderbird.">
      </aside>
      <aside class="flex-col flex-1 items-start">
        <h3 class="header-section">
          <span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" height="24" width="24">
  <path d="M20.054 1H6.932A4.244 4.244 0 0 0 3 5.498v5.998h2.999V5.498A1.392 1.392 0 0 1 7.248 4h12.494a1.392 1.392 0 0 1 1.251 1.5v11.995a1.392 1.392 0 0 1-1.25 1.5H7.248a1.392 1.392 0 0 1-1.249-1.5v-1.499H2.999v1.5a4.248 4.248 0 0 0 3.94 4.498h14.429a2.831 2.831 0 0 0 2.624-2.999V5.498A4.248 4.248 0 0 0 20.054 1Z"/>
  <path d="M10.715 16.214a.75.75 0 1 0 1.06 1.06l3-2.999a.75.75 0 0 0 0-1.06l-3-3a.75.75 0 0 0-1.06 1.061l1.72 1.72H2.25a.75.75 0 0 1-.75-.75V9.247a.75.75 0 0 0-1.499 0v3a2.25 2.25 0 0 0 2.25 2.248h10.185Z"/>
</svg>
</span>
          {{ _('New Import/Export Wizard')}}
        </h3>
        <p class="font-lg tracking-wider leading-loose mb-10">
          {{ _('Thunderbird 102 makes it quick and simple to move your accounts and data around. The new step-by-step wizard provides a guided experience for importing all that data that’s important to you. We’ve also taken extra precautions to ensure that no data is accidentally duplicated in your profile after an import. Moving from Outlook, SeaMonkey, or another Thunderbird installation is easier than ever. No add-ons required!') }}
        </p>
      </aside>
    </div>
  </section>

  <!-- Spaces Toolbar -->
  <section class="pt-20 pb-20 flex justify-center bg-grey-lighter">
    <div class="flex lg:flex-row flex-col flex-1 max-w-6xl ml-16 mr-16">
      <aside class="flex-col flex-1 items-start">
        <h3 class="header-section">
          <span><svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" height="24" width="24">
  <path d="M4.5 1h15A4.512 4.512 0 0 1 24 5.5v12a4.513 4.513 0 0 1-4.5 4.5h-15A4.508 4.508 0 0 1 0 17.5v-12A4.52 4.52 0 0 1 4.5 1ZM21 17.5v-12A1.5 1.5 0 0 0 19.5 4H12v15h7.5a1.5 1.5 0 0 0 1.5-1.5Zm-18 0A1.5 1.5 0 0 0 4.5 19h6V4h-6A1.5 1.5 0 0 0 3 5.5Z"/>
  <path d="M5.25 7h3a.75.75 0 0 0 0-1.5h-3a.75.75 0 0 0 0 1.5zm0 3h3a.75.75 0 0 0 0-1.5h-3a.75.75 0 0 0 0 1.5zm1.5 3h1.5a.75.75 0 0 0 0-1.5h-1.5a.75.75 0 0 0 0 1.5z"/>
</svg>
</span>
          {{ _('New Spaces Toolbar')}}
        </h3>
        <p class="font-lg tracking-wider leading-loose mb-10">
          {{ _('Thunderbird 102 makes navigating the application more intuitive thanks to the new Spaces Toolbar. Move between all of your activities with ease, whether it’s managing email, working with contacts through the awesome new address book, using the calendar and tasks functionality, chat, and even add-ons! If you want to save screen real estate, the Spaces Toolbar can be dismissed, and you can instead navigate the different activities Thunderbird offers with the new Pinned Spaces tab.') }}
        </p>
      </aside>
      <aside class="flex-1 lg:ml-8">
        <img class="w-full h-auto max-w-2xl self-center shadow-img" src="/media/archived/img/thunderbird/whatsnew/102/spaces-toolbar.gif" alt="Screenshot showing the new matrix chat in Thunderbird.">
      </aside>
    </div>
  </section>

  <!-- Matrix Chat -->
  <section class="pt-20 pb-20 flex justify-center">
    <div class="flex lg:flex-row flex-col flex-col-reverse flex-1 max-w-6xl ml-16 mr-16">
      <aside class="flex-1 lg:mr-8">
        <img class="w-full h-auto max-w-2xl self-center shadow-img" src="/media/archived/img/thunderbird/whatsnew/102/matrix-chat.png" alt="Screenshot of the new spaces toolbar in Thunderbird.">
      </aside>
      <aside class="flex-col flex-1 items-start">
        <h3 class="header-section">
          <span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" height="24" width="24">
  <path fill-opacity="context-fill-opacity" d="M18 23.5a1.5 1.5 0 0 1-1.155-.555L12.39 17.5H3.63A3.645 3.645 0 0 1 0 13.87V4.63A3.645 3.645 0 0 1 3.63 1h16.74A3.645 3.645 0 0 1 24 4.63v9.24a3.645 3.645 0 0 1-3.63 3.63h-.87V22a1.5 1.5 0 0 1-1.5 1.5ZM3.63 4a.63.63 0 0 0-.63.63v9.24c0 .348.282.63.63.63h9.465a1.5 1.5 0 0 1 1.155.555l2.25 2.73V16a1.5 1.5 0 0 1 1.5-1.5h2.37a.63.63 0 0 0 .63-.63V4.63a.63.63 0 0 0-.63-.63Z"/>
</svg>
</span>
          {{ _('New Matrix Chat Support')}}
        </h3>
        <p class="font-lg tracking-wider leading-loose mb-10">
          {{ _('Thunderbird 102 offers a new way to chat privately and securely with your colleagues, friends, and family. We’ve added support for the open-source, decentralized chat protocol Matrix, and it’s usable right out of the box! (As always, we welcome your feedback since it’s an evolving service.)') }}
        </p>
      </aside>
    </div>
  </section>

  <section class="pt-20 flex justify-center bg-calendar pr-8 pl-8">
    <aside class="flex flex-col flex-1 lg:max-w-6xl lg:ml-16 lg:mr-16">
      <h3 class="header-section">
        <span><svg viewBox="0 0 22 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="UI" stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd">
        <path d="M10.8125123,4.71427345 L6.95536481,4.71427345 L6.95536481,8.57142091 L10.8125123,8.57142091 L10.8125123,4.71427345 Z M12.0982281,11.1428525 L12.0982281,12.4285684 L5.66964899,12.4285684 L5.66964899,11.1428525 L12.0982281,11.1428525 Z M12.0982281,3.42855763 L12.0982281,9.85713673 L5.66964899,9.85713673 L5.66964899,3.42855763 L12.0982281,3.42855763 Z M18.5268072,11.1428525 L18.5268072,12.4285684 L13.3839439,12.4285684 L13.3839439,11.1428525 L18.5268072,11.1428525 Z M18.5268072,8.57142091 L18.5268072,9.85713673 L13.3839439,9.85713673 L13.3839439,8.57142091 L18.5268072,8.57142091 Z M18.5268072,5.99998927 L18.5268072,7.28570509 L13.3839439,7.28570509 L13.3839439,5.99998927 L18.5268072,5.99998927 Z M18.5268072,3.42855763 L18.5268072,4.71427345 L13.3839439,4.71427345 L13.3839439,3.42855763 L18.5268072,3.42855763 Z M3.********,13.0714263 L3.********,3.42855763 L1.81250153,3.42855763 L1.81250153,13.0714263 C1.81250153,13.4229892 2.10379652,13.7142842 2.45535944,13.7142842 C2.80692236,13.7142842 3.********,13.4229892 3.********,13.0714263 Z M19.812523,13.0714263 L19.812523,2.14284182 L4.38393317,2.14284182 L4.38393317,13.0714263 C4.38393317,13.2924087 4.34375455,13.5133911 4.27344197,13.7142842 L19.1696651,13.7142842 C19.521228,13.7142842 19.812523,13.4229892 19.812523,13.0714263 Z M21.0982388,0.********* L21.0982388,13.0714263 C21.0982388,14.1361597 20.2343985,15 19.1696651,15 L2.45535944,15 C1.39062603,15 0.526785714,14.1361597 0.526785714,13.0714263 L0.526785714,2.14284182 L3.********,2.14284182 L3.********,0.********* L21.0982388,0.********* Z"></path>
    </g>
</svg>
</span>
        {{ _('Other Notable Changes')}}
      </h3>
      <p class="font-lg tracking-wider leading-loose mb-6">
        {{ _('There are many other big changes that make Thunderbird 102 a very large release including:') }}
      </p>
      <ul class="font-md tracking-wider leading-loose mb-6 p-links-blue">
        <li>{{ _('New Icons and Default Folder Colors') }}</li>
        <li>{{ _('Use Thunderbird without an email account (for example, as an RSS reader)') }}</li>
        <li>{{ _('Numerous OpenPGP improvements') }}</li>
        <li>{{ _('CalendarLocal parser converted to javascript') }}</li>
        <li>{{ _('Today Pane calendar UI refresh') }}</li>
        <li>{{ _('POP3 implemented in javascript') }}</li>
        <li>{{ _('Account settings moved to macOS application menu') }}</li>
        <li>{{ _('Plus: Application-wide font size control, Theme fixes, FileLink fixes, Accessibility fixes, improved vCard integration, improved calendar startup performance, Calendar fixes including correct order of week view headers and days for right to left locales.') }}</li>
      </ul>
      <p class="font-lg tracking-wider leading-loose mb-10 p-links-blue">
        {{ _('You can see details of all our changes in the release notes for <a href="%(release)s">Thunderbird 102.0 and up</a>.')|format(release=url('thunderbird.releases.index')) }}      </p>
    </aside>
  </section>

  <section class="bg-prefooter bg-grey-lighter -mb-10">
    <section class="pt-10 pb-10 lg:pt-20 lg:pb-20 flex flex-col lg:flex-row max-w-6xl mx-auto justify-between pl-8 pr-8 mb-10">
  <aside class="flex flex-col flex-1 lg:max-w-lg mb-20 lg:mb-0">
    <h3 class="header-section">
      <a id="donate" class="anchor" aria-hidden="true" href="#donate">
          <svg viewBox="0 0 17 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="UI" stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd">
        <path d="M15.0803746,12.7857105 C15.0803746,12.5245494 14.979928,12.2834777 14.7991242,12.1026739 L12.709836,10.0133857 C12.5290323,9.83258193 12.2779159,9.73213539 12.0267995,9.73213539 C11.7355045,9.73213539 11.5044775,9.84262659 11.3035844,10.0535643 C11.635058,10.3850379 12.0267995,10.6662883 12.0267995,11.1785657 C12.0267995,11.7109324 11.5948794,12.1428525 11.0625126,12.1428525 C10.5502352,12.1428525 10.2689849,11.751111 9.9375113,11.4196374 C9.72657355,11.6205305 9.60603769,11.8515576 9.60603769,12.1528972 C9.60603769,12.4040136 9.70648424,12.6551299 9.88728803,12.8359337 L11.9564869,14.9151773 C12.1372907,15.0959811 12.3884071,15.186383 12.6395235,15.186383 C12.8906398,15.186383 13.1417562,15.0959811 13.32256,14.9252219 L14.7991242,13.4587023 C14.979928,13.2778985 15.0803746,13.0368268 15.0803746,12.7857105 Z M8.01898223,5.7042288 C8.01898223,5.45311243 7.91853568,5.20199606 7.7377319,5.02119227 L5.668533,2.94194872 C5.48772921,2.76114493 5.23661284,2.66069838 4.98549647,2.66069838 C4.7343801,2.66069838 4.48326373,2.76114493 4.30245994,2.93190406 L2.82589568,4.39842367 C2.6450919,4.57922746 2.54464535,4.82029917 2.54464535,5.07141554 C2.54464535,5.33257657 2.6450919,5.57364829 2.82589568,5.75445207 L4.91518389,7.84374028 C5.09598767,8.02454406 5.34710405,8.11494596 5.59822042,8.11494596 C5.88951541,8.11494596 6.12054247,8.01449941 6.32143556,7.80356166 C5.98996195,7.47208805 5.59822042,7.19083771 5.59822042,6.67856032 C5.59822042,6.14619361 6.03014057,5.71427345 6.56250728,5.71427345 C7.07478468,5.71427345 7.35603501,6.10601499 7.68750862,6.4374886 C7.89844637,6.2365955 8.01898223,6.00556844 8.01898223,5.7042288 Z M17.0089483,12.7857105 C17.0089483,13.5491042 16.697564,14.2924087 16.1551526,14.8247754 L14.6785884,16.291295 C14.136177,16.8337064 13.4029172,17.1250013 12.6395235,17.1250013 C11.866085,17.1250013 11.1328252,16.8236617 10.5904139,16.2712057 L8.52121497,14.1919621 C7.97880361,13.6495508 7.68750862,12.916291 7.68750862,12.1528972 C7.68750862,11.3593695 8.00893758,10.6060204 8.57143825,10.0535643 L7.68750862,9.16963471 C7.13505261,9.73213539 6.39174815,10.0535643 5.59822042,10.0535643 C4.83482665,10.0535643 4.09152219,9.75222469 3.54911083,9.20981333 L1.45982263,7.12052513 C0.90736661,6.56806911 0.61607162,5.84485397 0.61607162,5.07141554 C0.61607162,4.30802178 0.92745592,3.56471732 1.46986728,3.03235061 L2.94643154,1.56583101 C3.4888429,1.02341965 4.2221027,0.732124656 4.98549647,0.732124656 C5.75893489,0.732124656 6.4921947,1.0334643 7.03460606,1.58592032 L9.10380495,3.66516387 C9.64621631,4.20757523 9.9375113,4.94083503 9.9375113,5.7042288 C9.9375113,6.49775653 9.61608235,7.25110564 9.05358168,7.80356166 L9.9375113,8.68749128 C10.4899673,8.12499061 11.2332718,7.80356166 12.0267995,7.80356166 C12.7901933,7.80356166 13.5334977,8.1049013 14.0759091,8.64731266 L16.1651973,10.7366009 C16.7176533,11.2890569 17.0089483,12.012272 17.0089483,12.7857105 Z"></path>
    </g>
</svg>

        </a>
      {{ _('Thunderbird is funded by users like you')}}
    </h3>
    <p class="font-lg tracking-wider leading-loose mb-8 mt-0 flex-1">
      {{ _('Thunderbird is both free and freedom respecting, but we’re also completely funded by donations! Help us sustain the project and continue to improve.') }}
    </p>
    <a href="?form=support&utm_content=home_page_body&utm_source=thunderbird.net&utm_medium=fru&utm_campaign=donation_2023" class="btn-donate self-start ml-0 mr-0 font-lg lg:font-base" data-donate-btn data-donate-content="home_page_body">
      <span class="inline-block w-3 mr-1"><svg viewBox="0 0 14 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="UI" stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd">
        <path d="M7.00000834,12.0000012 C6.8750082,12.0000012 6.75000805,11.9531261 6.65625793,11.859376 L1.78125212,7.15624542 C1.71875205,7.10155785 0,5.53124348 0,3.65624125 C0,1.36717602 1.39843917,-1.31130219e-05 3.73437945,-1.31130219e-05 C5.10156858,-1.31130219e-05 6.38282011,1.07811317 7.00000834,1.6874889 C7.61719658,1.07811317 8.89844811,-1.31130219e-05 10.2656372,-1.31130219e-05 C12.6015775,-1.31130219e-05 14.0000167,1.36717602 14.0000167,3.65624125 C14.0000167,5.53124348 12.2812646,7.10155785 12.2109521,7.17187044 L7.34375875,11.859376 C7.25000864,11.9531261 7.12500849,12.0000012 7.00000834,12.0000012 Z"></path>
    </g>
</svg>
</span> {{_('Donate')}}
    </a>
  </aside>

  <aside class="flex flex-col flex-1 lg:max-w-lg">
    <h3 class="header-section">
      <a id="mailing-list" class="anchor" aria-hidden="true" href="#mailing-list">
        <svg viewBox="0 0 17 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="UI" stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd">
        <path d="M15.0803746,12.7857105 C15.0803746,12.5245494 14.979928,12.2834777 14.7991242,12.1026739 L12.709836,10.0133857 C12.5290323,9.83258193 12.2779159,9.73213539 12.0267995,9.73213539 C11.7355045,9.73213539 11.5044775,9.84262659 11.3035844,10.0535643 C11.635058,10.3850379 12.0267995,10.6662883 12.0267995,11.1785657 C12.0267995,11.7109324 11.5948794,12.1428525 11.0625126,12.1428525 C10.5502352,12.1428525 10.2689849,11.751111 9.9375113,11.4196374 C9.72657355,11.6205305 9.60603769,11.8515576 9.60603769,12.1528972 C9.60603769,12.4040136 9.70648424,12.6551299 9.88728803,12.8359337 L11.9564869,14.9151773 C12.1372907,15.0959811 12.3884071,15.186383 12.6395235,15.186383 C12.8906398,15.186383 13.1417562,15.0959811 13.32256,14.9252219 L14.7991242,13.4587023 C14.979928,13.2778985 15.0803746,13.0368268 15.0803746,12.7857105 Z M8.01898223,5.7042288 C8.01898223,5.45311243 7.91853568,5.20199606 7.7377319,5.02119227 L5.668533,2.94194872 C5.48772921,2.76114493 5.23661284,2.66069838 4.98549647,2.66069838 C4.7343801,2.66069838 4.48326373,2.76114493 4.30245994,2.93190406 L2.82589568,4.39842367 C2.6450919,4.57922746 2.54464535,4.82029917 2.54464535,5.07141554 C2.54464535,5.33257657 2.6450919,5.57364829 2.82589568,5.75445207 L4.91518389,7.84374028 C5.09598767,8.02454406 5.34710405,8.11494596 5.59822042,8.11494596 C5.88951541,8.11494596 6.12054247,8.01449941 6.32143556,7.80356166 C5.98996195,7.47208805 5.59822042,7.19083771 5.59822042,6.67856032 C5.59822042,6.14619361 6.03014057,5.71427345 6.56250728,5.71427345 C7.07478468,5.71427345 7.35603501,6.10601499 7.68750862,6.4374886 C7.89844637,6.2365955 8.01898223,6.00556844 8.01898223,5.7042288 Z M17.0089483,12.7857105 C17.0089483,13.5491042 16.697564,14.2924087 16.1551526,14.8247754 L14.6785884,16.291295 C14.136177,16.8337064 13.4029172,17.1250013 12.6395235,17.1250013 C11.866085,17.1250013 11.1328252,16.8236617 10.5904139,16.2712057 L8.52121497,14.1919621 C7.97880361,13.6495508 7.68750862,12.916291 7.68750862,12.1528972 C7.68750862,11.3593695 8.00893758,10.6060204 8.57143825,10.0535643 L7.68750862,9.16963471 C7.13505261,9.73213539 6.39174815,10.0535643 5.59822042,10.0535643 C4.83482665,10.0535643 4.09152219,9.75222469 3.54911083,9.20981333 L1.45982263,7.12052513 C0.90736661,6.56806911 0.61607162,5.84485397 0.61607162,5.07141554 C0.61607162,4.30802178 0.92745592,3.56471732 1.46986728,3.03235061 L2.94643154,1.56583101 C3.4888429,1.02341965 4.2221027,0.732124656 4.98549647,0.732124656 C5.75893489,0.732124656 6.4921947,1.0334643 7.03460606,1.58592032 L9.10380495,3.66516387 C9.64621631,4.20757523 9.9375113,4.94083503 9.9375113,5.7042288 C9.9375113,6.49775653 9.61608235,7.25110564 9.05358168,7.80356166 L9.9375113,8.68749128 C10.4899673,8.12499061 11.2332718,7.80356166 12.0267995,7.80356166 C12.7901933,7.80356166 13.5334977,8.1049013 14.0759091,8.64731266 L16.1651973,10.7366009 C16.7176533,11.2890569 17.0089483,12.012272 17.0089483,12.7857105 Z"></path>
    </g>
</svg>

      </a>
      {{ _('Join our Newsletter')}}
    </h3>
    <p class="font-lg tracking-wider leading-loose mb-8 mt-0 flex-1">
      {{ _('Keep up with the latest and greatest updates on news, features, events, and previews, and get a sneak peek on the upcoming releases.') }}
    </p>
    <div class="max-w-lg">
      <!-- Begin Mailchimp Signup Form -->
<form action="https://thunderbird.us12.list-manage.com/subscribe?u=f8051cc8637cf3ff79661f382&id=56428f2efc"
class="newsletter-form"
method="post" id="mc-embedded-subscribe-form"
name="mc-embedded-subscribe-form">
<input id="mce-EMAIL" name="EMAIL" type="email" placeholder="{{ _('Your Email') }}" required>
<button type="submit" id="mc-embedded-subscribe" class="btn-newsletter">{{ _('Sign me up!') }}</button>
<!-- Prevent Bot signups -->
<div style="position: absolute; left: -5000px;" aria-hidden="true"><input type="text"
  name="b_f8051cc8637cf3ff79661f382_9badb0cec2" tabindex="-1" value=""></div>
</form>
<!--End mc_embed_signup-->
    </div>
  </aside>
</section>

  </section>


    </main>

    


    
    

    
        <footer id="colophon" class="bg-black-light text-white flex flex-col pt-10 pb-8 pl-4 pr-4">
  <section class="flex flex-col lg:flex-col justify-between items-center self-center w-full max-w-6xl">
    <aside class="flex flex-col items-center">
      <a href="/en-US/" class="block w-40" title="Thunderbird"
        alt="Thunderbird Vector Wordmark">
        <img src="/media/svg/logo-wordmark-white.svg" alt="Thunderbird Vector Wordmark"/>
      </a>


    </aside>
  </section>
    <section class="self-center text-center max-w-5xl mt-8">
    <ul class="list-none p-0 flex justify-center flex-wrap -mt-2">
      <li class="ml-2 mr-2 mt-2">
        <a href="{{ url('privacy') }}" class="small-link" data-link-type="footer" data-link-name="Privacy">{{ _('Privacy') }}</a>
      </li>
      <li class="ml-2 mr-2 mt-2">
        <a href="{{ url('privacy.notices.websites') }}" class="small-link" data-link-type="footer" data-link-name="Cookies">{{ _('Cookies') }}</a>
      </li>
      <li class="ml-2 mr-2 mt-2">
        <a href="{{ url('legal.index') }}" class="small-link" data-link-type="footer"
          data-link-name="Legal">{{ _('Legal') }}</a>
      </li>
      <li class="ml-2 mr-2 mt-2">
        <a href="{{ url('guidelines') }}" class="small-link" data-link-type="footer"
          data-link-name="guidelines">{{ _('Participation Guidelines') }}</a>
      </li>
      <li class="ml-2 mr-2 mt-2">
        <a href="{{ url('legal.infringement') }}" class="small-link" data-link-type="footer"
          data-link-name="infringement">{{ _('Report Trademark or Copyright Infringement') }}</a>
      </li>
      <li class="ml-2 mr-2 mt-2">
        <a href="{{ url('legal.fraud-report') }}" class="small-link" data-link-type="footer"
          data-link-name="Report Trademark Misuse">{{ _('Report Misuse of Thunderbird Trademark') }}</a>
      </li>
    </ul>

    <p class="mt-6 mb-6 leading-normal">
      {%- trans url=url('foundation.licensing.website-content'),mzla=url('mzla.blog-post') -%}
      Thunderbird is part of
      <a href="{{ mzla }}" class="inline-link">MZLA Technologies Corporation</a>,
      a wholly owned subsidiary of Mozilla Foundation.
      Portions of this content are ©1998–{{ current_year }} by individual
      contributors. Content available under
      a <a href="{{ url }}" class="inline-link">Creative Commons license</a>.
      {%- endtrans -%}
    </p>

    <a href="{{ url('contribute') }}" class="small-link font-semibold" data-link-type="footer"
      data-link-name="Contribute to this site">{{ _('Contribute to this site') }}</a>
    {% if LANG != 'en-US' %}
     &bull; <a href="https://pontoon.mozilla.org/{{ LANG }}/thunderbirdnet/LC_MESSAGES/messages.po/" class="small-link font-semibold"
      data-link-type="footer" data-link-name="Translate this site">{{ _('Translate this site') }}</a>
    {% endif %}
  </section>
</footer>
    

    
      <script type="text/javascript" src="/media/archived/js/site-bundle.js" charset="utf-8"></script>
      <script type="text/javascript" src="/media/archived/js/common-bundle.js" charset="utf-8"></script>
    
    
    
      <script type="text/javascript" src="/media/archived/js/matomo-config.js" charset="utf-8"></script>
      <script type="text/javascript" src="/media/archived/js/matomo.js" async defer></script>
      <noscript>
        <p><img src="//thunderbird.innocraft.cloud/piwik.php?idsite=1&rec=1" style="border:0;" alt="" /></p>
      </noscript>
    
    <!--[if IE 9]>
      <script src="/media/archived/js/libs/matchMedia.js"></script>
    <![endif]-->

    

    
  </body>
</html>