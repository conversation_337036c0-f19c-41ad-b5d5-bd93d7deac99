{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% set active_page = "beta-appeal-24" %}
{% set page_title_text = _('Experience the future of Thunderbird') %}
{% extends "includes/base/base.html" %}

{% block page_title %}{{ page_title_text }}{% endblock %}
{% block breadcrumbs %}{% endblock %}
{% block extra_meta %}
  <meta name="robots" content="noindex,nofollow"/>
{% endblock %}

{% block site_header %}
  <header id="masthead">
    {% block header_content %}
      <h1 class="tagline">
        {{ page_title_text }}
      </h1>
    {% endblock %}

    {% include 'includes/donation-includes.html' %}

    {% block site_header_share %}{% endblock %}

    {% block header_separator %}
      <div class="header-separator" aria-hidden="true">
        {{ svg('base/separators/chevron-convex') }}
      </div>
    {% endblock %}

  </header>
{% endblock %}

{% block content %}
  <section>
    <div class="container">
      <div class="two-columns">
        <div class="section-text">
          {% trans trimmed beta_version=128, current_version=115, knowledge_base_url='https://support.mozilla.org/kb/thunderbird-beta', topic_box_url='https://thunderbird.topicbox.com/groups/beta' %}
            <p>Join the Beta today, and enjoy an exciting preview of the new features and visual enhancements coming to Thunderbird {{ beta_version }}. You can still run Thunderbird {{ current_version }} after installing Beta on Windows with no special settings, unless you are using POP mail accounts. See details in the <a href="{{ knowledge_base_url }}">beta knowledge base</a>.</p>
            <p>Thunderbird Beta is early access software, so please report the rough edges using the Help menu. You can find assistance via “Get Help” and “Share Ideas and Feedback” as well as the <a href="{{ topic_box_url }}">Beta Topicbox</a> forum.</p>
          {% endtrans %}
        </div>
        <div class="image">
          {{ high_res_img('thunderbird/base/donate/rocket.png', {'alt': _('A rocket with the Thunderbird logo.')}, alt_formats=('avif', 'webp')) }}
        </div>
      </div>
      <div class="btn-container win64bit">
        <a class="btn btn-white-bg" href="{{ download_url(platform_os='win64', channel='beta') }}">{{ _('Install now') }}</a>
      </div>
      <div class="btn-container win32bit">
        <a class="btn btn-white-bg " href="{{ download_url(platform_os='win', channel='beta') }}">{{ _('Install now') }}</a>
      </div>
      <!-- Default button -->
      <div class="btn-container default">
        <a class="btn btn-white-bg default" href="{{ url('thunderbird.download-beta') }}">{{ _('Install now') }}</a>
      </div>
    </div>
  </section>
{% endblock %}

{% block site_footer %}
  <div class="appeal-footer-container">
    <div class="pre-footer-cover-container">
      {% include 'includes/components/page-separator-cover.html' %}
    </div>
    <div id="footer" class="container footer">
      {% include 'includes/appeal-footer.html' %}
    </div>
    {% include 'includes/donation-includes.html' %}
  </div>
{% endblock site_footer %}
