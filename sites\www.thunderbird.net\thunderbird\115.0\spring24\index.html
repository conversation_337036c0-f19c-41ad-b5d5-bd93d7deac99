{# This Source Code Form is subject to the terms of the Mozilla Public
  # License, v. 2.0. If a copy of the MPL was not distributed with this
  # file, You can obtain one at http://mozilla.org/MPL/2.0/. #}

 {% set active_page = "eoy" %}
 {% extends "includes/base/base.html" %}
 {% block facebook_id %}348169218537151{# facebook.com/Thunderbird #}{% endblock %}

 {% block page_image %}{{ static('img/thunderbird/thunderbird-256.png') }}{% endblock %}
 {% block page_favicon %}{{ static('img/thunderbird/favicon.ico') }}{% endblock %}
 {% block page_favicon_large %}{{ static('img/thunderbird/favicon-196.png') }}{% endblock %}
 {% block page_ios_icon %}{{ static('img/thunderbird/ios-icon-180.png') }}{% endblock %}

 {% block base_css %}<link href="/media/css/appeal/spring24.css" rel="stylesheet" type="text/css" />{% endblock %}

 {% block page_title %}{{ _('Thunderbird is Powered by You!') }}{% endblock %}

{% block extrahead %}
<script type="application/javascript">
  /**
   * Fix for using <strong> instead of <b> which causes anything in strong to be split out by Mac VoiceOver.
   * This adds the role="presentation" to each strong tag. Allowing the text to be read together without requiring additional input.
   * Can't fix it in the code because localization has already gone out. Ah well.
   */
  document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('strong').forEach(function (item) {
      item.role = 'presentation';
    });
  });
</script>
{% endblock %}

{% block site_header_unwrapped %}
<div class="clouds-bg">
  <div class="left"></div>
  <div class="right"></div>
</div>
{% endblock %}

{% block site_header %}
<header id="initAppeal" >
  <aside>
    <h1 id="appeal-heading" class="accent-text" aria-label="{{ _('Thunderbird is Powered by You!') }}">
      {{ _('Thunderbird <span>is Powered by You!</span>') }}
    </h1>
    <p>
      {{ _('<strong>Did you know Thunderbird is funded only by donations from users like you?</strong>')}}
    </p>
    <p>
      {{ _('We don’t show advertisements or sell your data. <strong>You are not the product.</strong>')}}
    </p>
    <p>
      {{ _('<strong>Without you, Thunderbird can’t exist.</strong> Giving a gift today means Thunderbird continues to remain available!')}}
    </p>
  </aside>
  <aside id="illustration" aria-hidden="true">
    <div id="ill-body">
      {{ svg('appeal/spring24/ill-body2') }}
    </div>
    <div id="ill-hearts">
      {{ svg('appeal/spring24/ill-hearts') }}
    </div>
  </aside>
</header>
{% endblock %}

{% block email_form %}
{% endblock email_form %}

{% block content %}
<section id="appeal-body" class="letter-container font-xl">
    <a href="{{ donate_url(content='cta', campaign='spring_appeal_2024', source='thunderbird-client') }}" class="donate-banner" data-donate-btn>
      <div id="donate-banner-left">
        {{ _('Click here to <strong class="block">Donate!</strong>') }}
      </div>
      <div id="decoration">
        <div id="pill-1" class="pill"></div>
        <div id="pill-2" class="pill"></div>
        <div id="pill-3" class="pill"></div>
        <div id="pill-4" class="pill"></div>
        <div id="pill-5" class="pill"></div>
        <div id="pill-6" class="pill"></div>
        <div id="pill-7" class="pill"></div>
        <div id="pill-8" class="pill"></div>
        <div id="pill-9" class="pill"></div>
        <div id="pill-10" class="pill"></div>
      </div>
      <div id="donate-banner-right" class="text-right accent-text self-end">
        {{ _('Help keep <strong class="block">Thunderbird Alive!</strong>') }}
      </div>
      <div id="hover-hearts">
        <div id="hover-heart-1">{{ svg('donate-heart') }}</div>
        <div id="hover-heart-2">{{ svg('donate-heart') }}</div>
        <div id="hover-heart-3">{{ svg('donate-heart') }}</div>
      </div>
    </a>
    <p>
      {{ _('Since 2003, Thunderbird\'s mission has been to offer a powerful and customizable communication experience that respects your time, your data, and your privacy. <strong>Free for everyone!</strong>')}}
    </p>
    <p>
      {{ _('Accomplishing that mission means keeping <strong>Thunderbird</strong> secure, maintaining complex server infrastructure, updating old code, fixing bugs, and developing new features. <strong>These activities are expensive</strong> - requiring talented software engineers and robust infrastructure.')}}
    </p>
    <p>
      {{ _('<strong>So today we’re asking you to help us out.</strong> If you get value from using Thunderbird, please consider giving a donation to support it!')}}
    </p>
    <div class="heart-container">
      <div class="left-lines">
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
      </div>
      <div aria-hidden="true" class="heart-svg">{{ svg('donate-heart') }}</div>
      <div class="right-lines">
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
      </div>
    </div>
    <p class="closing-text">
      {{ _('The Thunderbird Team') }}
    </p>
</section>
{% endblock %}
{% block site_footer%}
  {% include 'includes/donation-includes.html' %}
  <div id="footer" class="container footer">
    {% include 'includes/appeal-footer.html' %}
  </div>
{% endblock site_footer %}
