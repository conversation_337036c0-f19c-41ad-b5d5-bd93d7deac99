{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% set active_page = "whatsnew-128" %}
{% set page_title_text = _('Welcome to the evolution of Supernova!') %}
{% extends "includes/base/base.html" %}

{% block page_title %}{{ page_title_text }}{% endblock %}
{% block breadcrumbs %}{% endblock %}
{% block extra_meta %}
  <meta name="robots" content="noindex,nofollow"/>
{% endblock %}

{% block base_css %}
  <link href="{{ static('css/whatsnew-128.css') }}" rel="stylesheet" type="text/css"/>
{% endblock %}

{% macro svg_img(file_name, alt_text='') %}
<img src="{{ static('svg/{file_name}.svg'.format(file_name=file_name)) }}" alt="{{ alt_text }}"/>
{% endmacro %}

{% block content %}
  <div class="main-content">
    <div class="overlay"></div>
    <div class="sticky-nebula"></div>

    <section class="appeal-welcome">
      <div class="header-collection">
        <div class="space-things" aria-hidden="true">
          {{ svg_img('appeal/whatsnew-128/space-things') }}
        </div>
        <div class="screenshot-128">
          {{ high_res_img('thunderbird/appeal/whatsnew-128/128-screenshot.png', {'alt': ''}, alt_formats=('avif', 'webp')) }}
        </div>
      </div>
      <div class="nebula-branding" aria-label="{{ _('Thunderbird 128 Nebula') }}">
        <span aria-hidden="true">
        {{ svg_img('appeal/whatsnew-128/nebula-branding') }}
        </span>
      </div>
      <div class="welcome-message with-frame">
        <section class="copy">
          <h3>{{ _('Welcome to the evolution of Supernova!') }}</h3>
          <p>
            {% trans trimmed %}
              With revolutionary code improvements, Nebula sets the stage for
              faster delivery of new features, reinforced stability, stellar
              quality-of-life enhancements and visual refinements.
            {% endtrans %}
          </p>
        </section>
      </div>

      <div class="non-carousel-carousel">
        {{ high_res_img('thunderbird/appeal/whatsnew-128/non-carousel-carousel.png', {'alt': ''}, alt_formats=('avif', 'webp')) }}
      </div>
    </section>


    <div class="fast-and-fluid two-column">
      <section class="copy">
        <h2>{{ _('Fast and Fluid') }}</h2>
        <p>
          {% trans trimmed %}
            Feel the difference smarter code makes possible! Interactions with your
            message list are faster, the email space is more responsive, and
            Thunderbird is more stable and reliable.
          {% endtrans %}
        </p>
      </section>
      <aside class="picture rocket" aria-hidden="true"></aside>
    </div>

    <div class="cards-view two-column flipped">
      <section class="copy">
        <h3>
          {{ _('Cards View') }}
          <span aria-hidden="true">{{ svg_img('appeal/whatsnew-128/cards-view-icon') }}</span>
        </h3>
        <p>
          {% trans trimmed %}
            The updated design makes scanning your emails faster, with a more
            attractive layout and an improved threads experience. Plus, the height
            of email cards adjusts automatically based on your settings, so
            everything looks just right.
          {% endtrans %}
        </p>
      </section>
      <aside class="picture with-frame">
        {{ high_res_img('thunderbird/appeal/whatsnew-128/cards-view.png', {'alt': ''}, alt_formats=('avif', 'webp')) }}
      </aside>
    </div>

    <div class="accent-colors two-column">
      <section class="copy">
        <h3>
          {{ _('Accent Colors') }}
          <span aria-hidden="true">{{ svg_img('appeal/whatsnew-128/accent-colors-icon') }}</span>
        </h3>
        <p>
          {% trans trimmed %}
            Enhanced theme compatibility, especially for Linux users on Ubuntu
            and Mint. Your Thunderbird will better blend with your desktop
            environment, seamlessly matching the system’s accent colors.
          {% endtrans %}
        </p>
      </section>
      <aside class="picture with-frame">
        {{ high_res_img('thunderbird/appeal/whatsnew-128/accent-colors-items.png', {'alt': ''}, alt_formats=('avif', 'webp')) }}
      </aside>
    </div>

    <div class="folder-pane two-column flipped">
      <section class="copy">
        <h3>
          {{ _('Folder Pane') }}
          <span aria-hidden="true">{{ svg_img('appeal/whatsnew-128/folder-pane-icon') }}</span>
        </h3>
        <p>
          {% trans trimmed %}
            A bundle of improvements including faster rendering and searching
            of unified folders, multi-folder selection capabilities, and better
            recall of message thread states.
          {% endtrans %}
        </p>
      </section>
      <aside class="picture with-frame">
        {{ high_res_img('thunderbird/appeal/whatsnew-128/folder-pane.png', {'alt': ''}, alt_formats=('avif', 'webp')) }}
      </aside>
    </div>

    <section class="rust-revolution two-column">
      <section class="copy">
        <h2>{{ _('Rust Revolution') }}</h2>
        <p>
          {% trans trimmed %}
            We've overcome major roadblocks to bring Rust into Thunderbird.
            This paves the way for better code, shared features between desktop and
            mobile, and faster development time. It's a game-changer for our
            developers and for our users.
          {% endtrans %}
        </p>
      </section>
    </section>
    <aside class="right-wrap" aria-hidden="true">
        {{ high_res_img('thunderbird/appeal/whatsnew-128/rust-gears.png', {'alt': ''}, alt_formats=('avif', 'webp')) }}
    </aside>
  </div>
  <div class="pre-footer">
    <section class="streamlined-menu full-width">
      <p>
        {% trans trimmed %}
          Streamlined menu navigation with better visual cues and less
          cognitive burden to make your email experience more efficient
          and more enjoyable.
        {% endtrans %}
      </p>
    </section>

    <section class="additional-features three-column">
      <div class="column">
        <div class="feature" aria-hidden="true">{{ svg_img('appeal/whatsnew-128/native-windows-notifications-icon') }}</div>
        <div class="content">
          <h4>
            {{ _('Native Windows Notifications') }}
          </h4>
          <p>
            {% trans trimmed %}
              Fixed and fully functional! Clicking a notification dismisses it,
              brings Thunderbird to the foreground, and selects the message.
              Notifications now disappear when Thunderbird closes.
            {% endtrans %}
          </p>
        </div>
      </div>
      <div class="column">
        <div class="feature" aria-hidden="true">{{ svg_img('appeal/whatsnew-128/context-menu-improvements-icon') }}</div>
        <div class="content">
          <h4>
            {{ _('Context Menu Improvements') }}
          </h4>
          <p>
            {% trans trimmed %}
              Reorganized for a smoother experience, with primary actions
              now as icons for quick access.
            {% endtrans %}
          </p>
        </div>
      </div>
      <div class="column">
        <div class="feature" aria-hidden="true">{{ svg_img('appeal/whatsnew-128/account-color-customization-icon') }}</div>
        <div class="content">
          <h4>
            {{ _('Account Color Customization') }}
          </h4>
          <p>
            {% trans trimmed %}
              By popular demand, you can now customize the color of your account
              icons. These colors also show up in the “From” selection
              when composing emails.
            {% endtrans %}
          </p>
        </div>
      </div>
    </section>
    <section class="nebula-enables full-width">
      <p>
        {% trans trimmed %}
          “Nebula” enables an email experience that shines brighter than ever.
          Expect more updates and helpful new features to materialize over
          the upcoming months.
        {% endtrans %}
      </p>
    </section>
  </div>
{% endblock %}

{% block site_footer %}
  <footer class="appeal-footer-container">
    <div class="pre-footer-cover-container" aria-hidden="true">
      {% include 'includes/components/page-separator-cover.html' %}
    </div>
    <div id="footer" class="container footer">
      {% include 'includes/appeal-footer.html' %}
    </div>
    {% include 'includes/donation-includes.html' %}
  </footer>
{% endblock site_footer %}


