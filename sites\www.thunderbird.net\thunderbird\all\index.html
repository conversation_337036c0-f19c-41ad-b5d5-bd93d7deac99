{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% set active_page = "download" %}
{% extends "includes/base/page.html" %}
{% from "includes/_enonly/product-all-macros.html" import build_table with context %}
{% from 'includes/download/macros/download-mobile.html' import download_mobile_btns with context %}

{% block page_title %}{{ _('Download') }}{% endblock %}
{% block category %}{{ _('Resources') }}{% endblock %}

{% block content %}
<section>
  <div class="container" id="get-thunderbird">
    <div id="select-download" class="two-columns">
      <div class="column">
        <div class="section-text">
          <div class="section-title">
            <h2 class="split-text" aria-label="{{ _('Thunderbird Desktop.') }}">
              {{ _('Thunderbird <span class="txt-gradient">Desktop</span>') }}
            </h2>
          </div>
          <p>{{ _('These are officially maintained and distributed by Thunderbird. Download the latest Release version unless you wish to test the Beta version and report bugs.') }}</p>
        </div>
        <div class="section-text">
          <div class="alerts" role="alert">
            <p id="daily-warning" class="notice-panel warning hidden">
              {{ _('The Daily channel is where development and testing begin, and new features are developed. This is expected to be the least stable channel. Make sure you back up important data regularly.') }}
            </p>
            <p id="beta-warning" class="notice-panel warning hidden">
              {{ _('The Beta channel is where stabilization occurs. This is expected to be more stable than Daily, but not as stable as the Release or Extended Support Release channels. Make sure you back up important data regularly.') }}
            </p>
            <p id="esr-notice" class="notice-panel info hidden">
              {{ _('The official annual extended support release. This release is intended for users who want all available new features on an annual basis and stability/security fixes on a monthly basis.') }}
            </p>
            <p id="release-notice" class="notice-panel info hidden">
              {{ _('The official monthly release. This release is intended for users who want all available new features and bug fixes on a monthly basis.') }}
            </p>
          </div>

          <!-- 1. Locale -->
          <label class="pretend-to-be-h6" for="download-language-select" role="group">
            <strong>{{ _('Locale') }}</strong>
          </label>
          <select id="download-language-select" name="lang" dir="ltr" class="form-select">
            {% for code, label in translations|dictsort -%}
            {# We're just verifying release builds! #}
            {% if has_localized_download(code, None) %}
            <option lang="{{ code }}" value="{{ code }}" {{
            'selected' if code==LANG else '' }}>{{ label|safe }}</option>
            {% endif %}
            {% endfor %}
          </select>

          <!-- 2. Channel -->
          <label class="pretend-to-be-h6" for="download-release-select" role="group">
            <strong>{{ _('Release Channel') }}</strong>
          </label>
          <select id="download-release-select" name="channel" dir="ltr" class="form-select">
            {% for channel, channel_name in get_channels().items() %}
              {% if settings.DEFAULT_RELEASE_VERSION == channel %}
                {% set option_name = 'Thunderbird %(channel_name)s (Recommended)'|format(channel_name=channel_name) %}
              {% else %}
                {% set option_name = 'Thunderbird %(channel_name)s'|format(channel_name=channel_name) %}
              {% endif %}
            <option value="{{ channel }}">{{ _(option_name) }}</option>
            {% endfor %}
          </select>

          <!-- 3. OS -->
          <label class="pretend-to-be-h6" for="download-os-select" role="group">
            <strong>{{ _('Operating System') }}</strong>
          </label>
          <select id="download-os-select" name="os" dir="ltr" class="form-select">
            {% for os, platforms in get_platforms().items() %}
            <option data-is-mobile="{{ is_os_mobile(os) }}" value="{{ os }}">{{ os }}</option>
            {% endfor %}
          </select>

          <!-- 4. Advanced Platform (Optional) -->
          <details class="accordion">
            <summary>
              <span class="chevron-svg" aria-hidden="true">{{ svg('chevron-down') }}</span>
              {{ _('Need a specific installer?') }}
            </summary>
            <div class="answer">
              <label class="pretend-to-be-h6" for="download-advanced-platform-select" role="group">
                <strong>{{ _('Installer Type') }}</strong>
              </label>
              <select id="download-advanced-platform-select" name="installer" dir="ltr" class="form-select">
                {% for os, platforms in get_platforms().items() %}
                {% for platform_tuple in platforms %}
                {% set platform = platform_tuple[0] %}
                {% set platform_name = platform_tuple[1] %}
                <option data-for-os="{{ os }}" value="{{ platform }}">{{ platform_name }}</option>
                {% endfor %}
                {% endfor %}
              </select>
            </div>
          </details>
        <div class="download-button download-button-page download-spacing">
            <a
              id="download-btn"
              class="btn btn-download btn-white-bg btn-slim matomo-track-download"
              href="{{ download_url(platform_os=win64) }}"
              data-donate-redirect="download-{{ channel or 'esr' }}"
              data-donate-content="post_download"
              data-donate-link="{{ redirect_donate_url(content='post_download', download=True, download_channel=channel or 'esr') }}"
            >
              <span class="download-icon" aria-hidden="true">{{ svg('base/icons/download/generic-download-currentcolor') }}</span>
              {{ _('Download') }}
            </a>
          </div>
          <div class="platform-release">
            <div>
              <h4>{{ _('Other Downloads') }}</h4>
              <ul>
                <li>
                  <a class="small-link dotted" href="{{ url('download.desktop.msstore') }}">{{ _('Microsoft Apps Store') }}</a>
                </li>
                <li>
                  <a class="small-link dotted" href="{{ url('download.desktop.flathub') }}">{{ _('Flathub') }}</a>
                </li>
                <li>
                  <a class="small-link dotted" href="{{ url('download.desktop.snap') }}">{{ _('Snap Store') }}</a>
                </li>
              </ul>
            </div>
          </div>
          <div class="platform-release" data-for-app="desktop">
            <div>
              <h4>{{ _('More Information') }}</h4>
              <ul>
                <li>
                  <a class="small-link dotted" href="{{ url('thunderbird.sysreq', latest_thunderbird_version) }}">{{ _('System Requirements') }}</a>
                </li>
                <li>
                  <a class="small-link dotted" href="{{ thunderbird_url('releasenotes', default_channel) }}">{{ _('Release Notes') }}</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div class="column">
        <div class="section-text">
          <div class="section-title">
            <h2 class="split-text" aria-label="{{ _('Thunderbird Mobile.') }}">
              {{ _('Thunderbird <span class="txt-gradient">Mobile</span>') }}
              <span class="new-badge">{{ _('New') }}</span>
            </h2>
          </div>
          <p>{{ _('These are officially maintained and distributed by Thunderbird Mobile.') }}</p>
        </div>
        <div class="download-spacing">
        {{ download_mobile_btns() }}
        </div>
        <div class="platform-release">
            <div>
              <h4>{{ _('Other Downloads') }}</h4>
              <ul>
                <li>
                  <a class="small-link dotted"
                    href="{{ download_url(platform_os='apk', channel='mobile') }}"
                    data-donate-redirect="download-mobile"
                    data-donate-content="post_download"
                    data-donate-link="{{ redirect_donate_url(content='post_download', download=True, download_channel='mobile', form_id=settings.FRU_FORM_IDS['tfa']) }}"
                  >
                    {{ _('Binary (.apk)') }}
                  </a>
                </li>
                <li>
                  <a class="small-link dotted"
                     href="{{ download_url(platform_os='gplay', channel='mobile-beta') }}"
                     data-donate-redirect="download-mobile"
                     data-donate-content="post_download"
                     data-donate-link="{{ redirect_donate_url(content='post_download', download=True, download_channel='mobile-beta', form_id=settings.FRU_FORM_IDS['tfa']) }}"
                  >
                    {{ _('Google Play (Beta)') }}
                  </a>
                </li>
                <li>
                  <a class="small-link dotted"
                     href="{{ download_url(platform_os='fdroid', channel='mobile-beta') }}"
                     data-donate-redirect="download-mobile"
                     data-donate-content="post_download"
                     data-donate-link="{{ redirect_donate_url(content='post_download', download=True, download_channel='mobile-beta', form_id=settings.FRU_FORM_IDS['tfa']) }}"
                  >
                    {{ _('F-Droid (Beta)') }}
                  </a>
                </li>
              </ul>
            </div>
          </div>
        <div class="platform-release" data-for-app="android">
          <div>
            <h4>{{ _('More Information') }}</h4>
            <ul>
              <li>
                <a class="small-link dotted" href="{{ url('download.android.compatibility') }}">{{ _('System Requirements') }}</a>
              </li>
              <li>
                <a class="small-link dotted" href="{{ url('download.android.changelog') }}">{{ _('Release Notes') }}</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    {# No javascript / source of truth for esr download links #}
    <div id="all-downloads" data-thunderbird-version="{{ latest_thunderbird_version }}">
      <div class="section-text">
        <h2 class="split-text" aria-label="{{ _('Thunderbird Mobile.') }}" style="justify-content: center">
          {{ _('Thunderbird <span class="txt-gradient">Mobile</span>') }}
        </h2>
        <p>{{ _('These are officially maintained and distributed by Thunderbird Mobile.') }}</p>
        <div class="download-spacing">
        {{ download_mobile_btns() }}
        </div>
      </div>
      <div class="section-text">
        <h2 class="split-text" aria-label="{{ _('Thunderbird Desktop.') }}" style="justify-content: center">
          {{ _('Thunderbird <span class="txt-gradient">Desktop</span>') }}
        </h2>
        <p>{{ _('These are officially maintained and distributed by Thunderbird.') }}</p>
        <p>{{ _('Jump to the language you wish to download by using the legend below.') }}</p>
      </div>
      <div class="all-downloads-legend">
        <ul>
          {% set letter = namespace(first='') %}
          {% for build in full_builds -%}
          {% if letter.first != build.name_en[:1] %}
          {% set letter.first = build.name_en[:1] %}
          <li>
            <a href="#{{ letter.first }}" class="btn-link text-blue-dark">
              {{ letter.first }}
            </a>
          </li>
          {% endif %}
          {% endfor -%}
        </ul>
      </div>
      <div class="all-downloads-container">
        {{ build_table(platforms, full_builds) }}
      </div>
    </div>
  </div>
</section>
{% endblock %}
