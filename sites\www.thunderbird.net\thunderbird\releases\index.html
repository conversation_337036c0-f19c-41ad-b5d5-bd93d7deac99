{# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/. -#}

{% set active_page = "releases" %}
{% extends "includes/base/page.html" %}

{% block page_title %}{{ _('Releases') }}{% endblock %}
{% block category %}{{ _('Products') }} > {{ _('Thunderbird Desktop') }}{% endblock %}
{% block extrahead %}
  <link rel="alternate" type="application/atom+xml" title="{{ _('Thunderbird Release Notes Atom Feed') }}" href="{{ url('thunderbird.releases.atom') }}">
{% endblock %}
{% block content %}
<section>
  <div class="container no-gap">
    <div class="section-text two-columns">
      <div>
      <p>{{ _('Thunderbird release notes are specific to each version of the application. Select your version from the list below to see the release notes for it.') }}</p>
      </div>
      <div class="atom-feed">
        <a href="{{ url('thunderbird.releases.atom') }}" title="{{ _('Thunderbird Release Notes Atom Feed') }}">
          {{ svg('rss') }}
        </a>
      </div>
    </div>
    <div class="section-text release-list">
        {% for int_version, versions in releases -%}
          <div class="release-container">
            <h2 class="release-major txt-gradient">
              {{ get_link(int_version, versions.major) }}
            </h2>
            {% if versions.minor -%}
            <div class="release-minors">
              <ul aria-label="{{ _('Stability releases for version %(version)s'|format(version=versions.major)) }}">
              {% for version in versions.minor -%}
                <li>{{ get_link(int_version, version, true) }}</li>
              {% endfor -%}
              </ul>
            </div>
            {% endif -%}
          </div>
        {% endfor -%}

        <div class="release-container">
          <h2 class="release-major txt-gradient">
            <a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/releases/0.1.html">0.1</a>
          </h2>
          <div class="release-minors">
            <ul aria-label="{{ _('Stability releases for version %(version)s'|format(version=0.1)) }}">
            <li><a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/releases/0.2.html">0.2</a></li>
            <li><a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/releases/0.3.html">0.3</a></li>
            <li><a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/releases/0.4.html">0.4</a></li>
            <li><a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/releases/0.5.html">0.5</a></li>
            <li><a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/releases/0.6.html">0.6</a></li>
            <li><a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/releases/0.7.html">0.7</a></li>
            <li><a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/releases/0.8.html">0.8</a></li>
            <li><a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/releases/0.9.html">0.9</a></li>
            </ul>
          </div>
        </div>
      </div>

  </div>
</section>
{% endblock %}

{% macro accordion(icon, title) %}
{# Formatted accordion with inner contents being the html
:param icon: A variable containing pre-formatted html (e.g. the result of svg() or image())
:param title: The title of the accordion
:caller: The text body, shows up when you click on the title or accordion.
#}
<details class="accordion">
  <summary class="question">
    <span>{{ icon }}</span>
    {{ title }}
  </summary>
  <div class="answer">
    {{ caller() }}
  </div>
</details>
{% endmacro %}

{% macro get_link(int_version, version, point_release = false) %}
  {% set class = ' btn-body btn-secondary btn-release' if point_release else '' %}
  {%- if int_version < 2 -%}
    <a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/releases/{{ version }}.html" class="inline-link text-blue-dark font-bold no-underline{{ class }}">{{ version }}</a>
  {%- elif version == '2.0' -%}
    <a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/2.0.0.0/releasenotes/" class="inline-link text-blue-dark font-bold no-underline{{ class }}">{{ version }}</a>
  {%- elif version == '17.0.9' -%}
    <a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/17.0.9esr/releasenotes/" class="inline-link text-blue-dark font-bold no-underline{{ class }}">{{ version }}</a>
  {%- elif version == '17.0.10' -%}
    <a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/17.0.10esr/releasenotes/" class="inline-link text-blue-dark font-bold no-underline{{ class }}">{{ version }}</a>
  {%- elif int_version < 31 -%}
    <a href="http://website-archive.mozilla.org/www.mozilla.org/thunderbird_releasenotes/en-US/thunderbird/{{ version }}/releasenotes/" class="inline-link text-blue-dark font-bold no-underline{{ class }}">{{ version }}</a>
  {%- else -%}
    <a href="../../../en-US/thunderbird/{{ version }}/releasenotes/" class="inline-link text-blue-dark font-bold no-underline{{ class }}">{{ version }}</a>
  {%- endif -%}
{% endmacro %}
