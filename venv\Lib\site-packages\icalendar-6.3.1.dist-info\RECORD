../../Scripts/icalendar.exe,sha256=OFVQtAPXiXPmvh-Nt9xcGmcztTbfZkshbqXgpHSBEqA,108436
icalendar-6.3.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
icalendar-6.3.1.dist-info/METADATA,sha256=Lzo946BU2dqdAjHj-web1iZhAoPjRH4Cl8IkltqSSXA,9034
icalendar-6.3.1.dist-info/RECORD,,
icalendar-6.3.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
icalendar-6.3.1.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
icalendar-6.3.1.dist-info/entry_points.txt,sha256=B-H9JDTr-StnCaknEjA2a7tCIYsp1C_hP8gO-6owqz8,49
icalendar-6.3.1.dist-info/licenses/LICENSE.rst,sha256=xhB-8DVauOCBzobxbGqj-ZBoljxnZTDYOjOAnJPOtfQ,1326
icalendar/__init__.py,sha256=lkfDec8Oz01dLIrR1sZWYXRzZfiXNDNOSuKOnnUmIuM,2306
icalendar/__pycache__/__init__.cpython-311.pyc,,
icalendar/__pycache__/_version.cpython-311.pyc,,
icalendar/__pycache__/alarms.cpython-311.pyc,,
icalendar/__pycache__/attr.cpython-311.pyc,,
icalendar/__pycache__/cal.cpython-311.pyc,,
icalendar/__pycache__/caselessdict.cpython-311.pyc,,
icalendar/__pycache__/cli.cpython-311.pyc,,
icalendar/__pycache__/enums.cpython-311.pyc,,
icalendar/__pycache__/error.cpython-311.pyc,,
icalendar/__pycache__/param.cpython-311.pyc,,
icalendar/__pycache__/parser.cpython-311.pyc,,
icalendar/__pycache__/parser_tools.cpython-311.pyc,,
icalendar/__pycache__/prop.cpython-311.pyc,,
icalendar/__pycache__/tools.cpython-311.pyc,,
icalendar/__pycache__/version.cpython-311.pyc,,
icalendar/_version.py,sha256=agMWItxoVh1uO1_NTDgwgNT-UrUcksroeimEQFvRT2Q,511
icalendar/alarms.py,sha256=oO-dvp3rP-UOuc-y5tIbf_WGnNj6zO3fFCq_1bajWlE,13612
icalendar/attr.py,sha256=1PdmAlZ9F-ys8ytZNqzGMWmvaDRWmPVyH_3kgzyPxT0,26124
icalendar/cal.py,sha256=aneVWu6KrUpetxmqIm9nEQ3fLixaH_7hV52KmIctJ-Y,76175
icalendar/caselessdict.py,sha256=zu7ZcBNOXennD0RGcbEvHwt7mHZqRM8GvgOplai9tO4,3555
icalendar/cli.py,sha256=-6oOlgrNL96AKMOBejo8HtJqUxUffHw37d6l4YwZ41U,2980
icalendar/enums.py,sha256=_uPQVRLvBJGbHC1ADR8oi_pEa-ofMOOKNgGNwGmx090,2433
icalendar/error.py,sha256=LmT1UKMJg1R4_cBQ3gSRDah7VoSXR1eeg5L9v6baXw0,1767
icalendar/param.py,sha256=3L_Ua3mCLkk-SrYagKyDrkRxXI2zrBoSPdrFA44jg04,17076
icalendar/parser.py,sha256=QvDTGRN80S5690HYSgt_aw0lbG_8KZWS_VUJ4NeTboI,15024
icalendar/parser_tools.py,sha256=dC-fh2TQl7W1qpqiFbWAtDtcw8HOaf8SEMX59xEAHz4,1867
icalendar/prop.py,sha256=uFYZVf1kWSEk3wGc1a_olBO-Fj94bS07pdyy45SL0JM,62487
icalendar/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
icalendar/tests/__pycache__/__init__.cpython-311.pyc,,
icalendar/tests/__pycache__/conftest.cpython-311.pyc,,
icalendar/tests/__pycache__/test_bom_calendar.cpython-311.pyc,,
icalendar/tests/__pycache__/test_cli_tool.cpython-311.pyc,,
icalendar/tests/__pycache__/test_components_break_on_bad_ics.cpython-311.pyc,,
icalendar/tests/__pycache__/test_encoding.cpython-311.pyc,,
icalendar/tests/__pycache__/test_equality.cpython-311.pyc,,
icalendar/tests/__pycache__/test_examples.cpython-311.pyc,,
icalendar/tests/__pycache__/test_icalendar.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_116.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_165_missing_event.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_168_parsing_invalid_calendars_no_warning.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_218_parse_calendar.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_27_period.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_301_add_rrule_as_string.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_318_skip_default_parameters.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_322_single_strings_characters_split_into_multiple_categories.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_336_dateutil_timezone.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_348_exception_parsing_value.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_350.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_500_vboolean_for_parameter.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_557_encode_native_parameters.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_662_component_properties.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_716_alarm_time_computation.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_720_uid_property.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_722_generate_vtimezone.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_798_property_parameters.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_802.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_828.cpython-311.pyc,,
icalendar/tests/__pycache__/test_issue_836_do_not_quote_tzid.cpython-311.pyc,,
icalendar/tests/__pycache__/test_multiple.cpython-311.pyc,,
icalendar/tests/__pycache__/test_oss_fuzz_errors.cpython-311.pyc,,
icalendar/tests/__pycache__/test_parsing.cpython-311.pyc,,
icalendar/tests/__pycache__/test_period.cpython-311.pyc,,
icalendar/tests/__pycache__/test_property_params.cpython-311.pyc,,
icalendar/tests/__pycache__/test_pytz_zoneinfo_integration.cpython-311.pyc,,
icalendar/tests/__pycache__/test_recurrence.cpython-311.pyc,,
icalendar/tests/__pycache__/test_rfc_6868.cpython-311.pyc,,
icalendar/tests/__pycache__/test_rfc_7529.cpython-311.pyc,,
icalendar/tests/__pycache__/test_rfc_7986.cpython-311.pyc,,
icalendar/tests/__pycache__/test_rfc_7986_categories.cpython-311.pyc,,
icalendar/tests/__pycache__/test_rfc_9074.cpython-311.pyc,,
icalendar/tests/__pycache__/test_time.cpython-311.pyc,,
icalendar/tests/__pycache__/test_timezone_identification.cpython-311.pyc,,
icalendar/tests/__pycache__/test_timezoned.cpython-311.pyc,,
icalendar/tests/__pycache__/test_unit_cal.cpython-311.pyc,,
icalendar/tests/__pycache__/test_unit_caselessdict.cpython-311.pyc,,
icalendar/tests/__pycache__/test_unit_parser_tools.cpython-311.pyc,,
icalendar/tests/__pycache__/test_unit_tools.cpython-311.pyc,,
icalendar/tests/__pycache__/test_with_doctest.cpython-311.pyc,,
icalendar/tests/__pycache__/timezone_ids.cpython-311.pyc,,
icalendar/tests/alarms/rfc_5545_absolute_alarm_example.ics,sha256=OEtl5s3VH5i4bf2b_Fa1ZCXXmteGBw_5Rn3XnM1x210,170
icalendar/tests/alarms/rfc_5545_end.ics,sha256=E03TaFWnle3vCAQ9y2jQjgZH1hF_2JX9YghcqenPs7k,409
icalendar/tests/alarms/start_date.ics,sha256=2YUvmY0m232ukF2SG67UejQlikCf3n6pr7Ychlte4Cs,411
icalendar/tests/attr/__init__.py,sha256=Xd9NJyKNCyr67nf9acCWPdfSKCv76hbexvzcRxkrpKY,45
icalendar/tests/attr/__pycache__/__init__.cpython-311.pyc,,
icalendar/tests/attr/__pycache__/test_alarm.cpython-311.pyc,,
icalendar/tests/attr/__pycache__/test_component.cpython-311.pyc,,
icalendar/tests/attr/__pycache__/test_exdates.cpython-311.pyc,,
icalendar/tests/attr/__pycache__/test_rdate.cpython-311.pyc,,
icalendar/tests/attr/__pycache__/test_rrule.cpython-311.pyc,,
icalendar/tests/attr/test_alarm.py,sha256=P2WXeC0aiPTyZh5CxMn6E3jQg4bNK16ZL3qjQt_iTEk,1111
icalendar/tests/attr/test_component.py,sha256=88na2F19J55JrNcq3cKTvOGMPeGuvQUR1jr6esKrFEA,3077
icalendar/tests/attr/test_exdates.py,sha256=BBQlE5JlNFhR_-GfzpRNXobherwB4tW_suJ6X_Bi7E4,1734
icalendar/tests/attr/test_rdate.py,sha256=KGsXW383ApOfjQ7VmxDpcE1CpPi8mH8SpnZEYewGPfM,3261
icalendar/tests/attr/test_rrule.py,sha256=4q4G1lkZcUJas2ZwPoomofS8rmxSIoJmfRr0I2Rg0iA,1284
icalendar/tests/calendars/alarm_etar_future.ics,sha256=8cKaydJ5UAjuvGwYZFRkgvxEiX5HyA7F0N6OxvPP0eE,5178
icalendar/tests/calendars/alarm_etar_notification.ics,sha256=eBNu9cK4GjCGtUVOeWjXxu28LbkcESSFxFyDCn9UAdw,5178
icalendar/tests/calendars/alarm_etar_notification_clicked.ics,sha256=Eo26cFPcfnyu_knNtMmPuWXF1N7kD8NAtTC5DYTt1hQ,4993
icalendar/tests/calendars/alarm_google_acknowledged.ics,sha256=pDBop7zq9RKja0Usms82gRecW_rPp5dF72_k6XmOwMA,1326
icalendar/tests/calendars/alarm_google_future.ics,sha256=YUO9_kz_WIZptsWC3LZIE0dhjBaANNhBMqXc_HiZJ9A,1326
icalendar/tests/calendars/alarm_thunderbird_2_future.ics,sha256=JsAO2aUaXBOVGK3Ak8kwbuD0xnyea-MYiJa-9_4E6Hg,14188
icalendar/tests/calendars/alarm_thunderbird_2_notification_5_min_postponed.ics,sha256=kCZ97a3HiISUjB5AT4wuPFSIkf_kBb0ZaLjyarE7MqE,14256
icalendar/tests/calendars/alarm_thunderbird_2_notification_5_min_postponed_and_closed.ics,sha256=3tjy0qsXjgaeORsXgIueHnJ2lMEXU5TifykVPT9HjuQ,14220
icalendar/tests/calendars/alarm_thunderbird_2_notification_5_min_postponed_and_popped_up.ics,sha256=kCZ97a3HiISUjB5AT4wuPFSIkf_kBb0ZaLjyarE7MqE,14256
icalendar/tests/calendars/alarm_thunderbird_2_notification_popped_up.ics,sha256=JsAO2aUaXBOVGK3Ak8kwbuD0xnyea-MYiJa-9_4E6Hg,14188
icalendar/tests/calendars/alarm_thunderbird_closed.ics,sha256=IO_aPY2Gl-84swNLw50HJrJIGWrVFfHDsAE92snVwTs,14233
icalendar/tests/calendars/alarm_thunderbird_future.ics,sha256=WfEAUs7flHVOYkv5ooH3HzbO7c7R0JMTEeODH_JpsvI,14201
icalendar/tests/calendars/alarm_thunderbird_snoozed_until_1457.ics,sha256=ZtS77-V8BD4Lb2UBDmhLkoqOgsKeLYYNSfkfmPvKybQ,14269
icalendar/tests/calendars/america_new_york.ics,sha256=bmv-Plq4X0cq5SdEpgf8Ia8vayKwzyNQRlyCw4wl2zA,1372
icalendar/tests/calendars/america_new_york_forward_reference.ics,sha256=cgp8EZHxrZJw4fCZ93RyOyEzQSnJ6X8-y1AF6vI1BZk,1428
icalendar/tests/calendars/big_bad_calendar.ics,sha256=6PfpckE9rLs6mLfIvn9BvT7ULB6xLkGJYytcL2fwrZ8,496
icalendar/tests/calendars/bom_calendar.ics,sha256=zqiTmYM_OEQmNhE1QfQnO3brsLhQy9Tivgl12uvC_1g,35
icalendar/tests/calendars/broken_ical.ics,sha256=TXWDdsRdEMjokn6C9KPrPwI9RjTmOnSgzT8-FDEy2C8,144
icalendar/tests/calendars/calendar_with_unicode.ics,sha256=TQ6GyZV9CtMGqodlke4OxhMlnJ0Ku4dZW2PN820Pl_A,198
icalendar/tests/calendars/created_calendar_with_unicode_fields.ics,sha256=0SNDIvg0MtpR5wYkq3DwJgVplC6Kg4xYcclfRZlATM0,563
icalendar/tests/calendars/example.ics,sha256=w5DDci3CJyzph1te8B-V7vHVEO6HTMsYKyJuFktFSQY,839
icalendar/tests/calendars/issue_104_broken_calendar.ics,sha256=LihNyMsVnDtBWSo6m7mJduVOzE1lKyv8iZsaocEoW2k,235
icalendar/tests/calendars/issue_156_RDATE_with_PERIOD_TZID_khal.ics,sha256=v6lJ3mah714hnf8bOW6Ws0sFjYf9V9P-LdsYEhrJxWM,1211
icalendar/tests/calendars/issue_156_RDATE_with_PERIOD_TZID_khal_2.ics,sha256=nS_80qbyV3GUQY_z97lJ4HLCjsYhsfpHcFQ38dLNKIw,2126
icalendar/tests/calendars/issue_165_missing_event.ics,sha256=X2kkhgFsDM07MAAOJwUDx4tJoNYoJzY8bKm6kZiH-2U,788
icalendar/tests/calendars/issue_168_expected_output.ics,sha256=YCE6F_c2YmKglhi-zYQVBCWF0dUR1Yb5UV-177T9FgY,117
icalendar/tests/calendars/issue_168_input.ics,sha256=e0nYj-_NZRO8vTn66PnEnSIvHfa6Ys1cl2MSE9QENuo,151
icalendar/tests/calendars/issue_178_component_with_invalid_name_represented.ics,sha256=2mv6UPQFHXKADU_3NQ-vvkoat9pfKH7oM-OVmyZ-gQQ,26
icalendar/tests/calendars/issue_178_custom_component_contains_other.ics,sha256=61Ly68x8ht3WYIveh5q99rHvo_sGdCeiUJoULX9ceds,112
icalendar/tests/calendars/issue_178_custom_component_inside_other.ics,sha256=cJ-zxlSKsClJ4NrLl9gF2ySn5ot120pv6PmpgN3GCqM,70
icalendar/tests/calendars/issue_218_bad_tzid.ics,sha256=QCV2z0ytgId0eiMPU4BWupoupM4u0QcxA1qOSpgSOd8,446
icalendar/tests/calendars/issue_237_fail_to_parse_timezone_with_non_ascii_tzid.ics,sha256=P-WlWKenJ3bonDAOPxl0bQcj71yAyU2D5vAfc-L0HE0,565
icalendar/tests/calendars/issue_27_multiple_periods_in_freebusy_multiple_freebusies.ics,sha256=vHCx9MgVXSxTE7C0q4ea11dT49kM1r7mCOBgeLxj61E,740
icalendar/tests/calendars/issue_27_multiple_periods_in_freebusy_one_freebusy.ics,sha256=GndHfm7VbYcn3O0n8tnnq3B7Dd0JysiJU8CLvF8xTIg,593
icalendar/tests/calendars/issue_322_expected_calendar.ics,sha256=FhHi-xDErrR8uFs2Vqy5bKtKnKKjlAqMnl5pRi7szWo,137
icalendar/tests/calendars/issue_348_exception_parsing_value.ics,sha256=KHQ-hJEeDtOtOm6zle16Vl-NWwWibgW14jmnWSAFZbM,1442
icalendar/tests/calendars/issue_350.ics,sha256=CVQ1mWSo8l8kSxlqd_AJMFhbC45_KyX09YKzvl7mpfw,1350
icalendar/tests/calendars/issue_466_convert_tzid_with_slash.ics,sha256=v80zLk1HtVKmtiqtjuxvflOqp4WE_UaKManzMKKJKuU,325
icalendar/tests/calendars/issue_466_respect_unique_timezone.ics,sha256=EZ-wXiru9oEWTotrvF5TjXsQHQS2H_Db802g-DOHcMc,635
icalendar/tests/calendars/issue_526_calendar_with_different_events.ics,sha256=NvwZMuODENZyNzGx93HHXior0t8qBfwgtAA05C4_LmM,560
icalendar/tests/calendars/issue_526_calendar_with_event_subset.ics,sha256=jbnzcpLWKqjQ9cOE_sd_I8xiHxtsF1-N2GuzXhFRGEk,281
icalendar/tests/calendars/issue_526_calendar_with_events.ics,sha256=ZAMoku6MRa_ygxjTJWHFixLYxksnVIEyWnR_UuhQ7_o,500
icalendar/tests/calendars/issue_526_calendar_with_shuffeled_events.ics,sha256=Z9QWtL6fzYNbbjSKIlzqvd9RRCxsAylvPIK_T5AqdM8,500
icalendar/tests/calendars/issue_722_missing_VTIMEZONE_custom.ics,sha256=GY12TCaiYufIjgblrBQPr_1gLGoMKh8xkCZRj1GlngE,111
icalendar/tests/calendars/issue_722_missing_timezones.ics,sha256=cpkodPHNJgYEo9wlFfftss8m-MipN4_pnOQ9tf5CCWA,649
icalendar/tests/calendars/issue_722_timezone_transition_ambiguity.ics,sha256=1JqmhFRPeRL-TyxGI1X-9cO_UTSCcNk6QlQMB9Rs91M,1049
icalendar/tests/calendars/issue_798_freebusy.ics,sha256=4yTDpQrT427UIOELr5Uo2MvtvawHIF3Q3N8uygh3MUA,331
icalendar/tests/calendars/issue_798_related_to.ics,sha256=25PnhBEKf5KEw-rkgJavKnh48_1Q8-kIf9vHNdZvwKA,204
icalendar/tests/calendars/issue_836_do_not_quote_tzid.ics,sha256=-LZzbPhNI8R4V6RzOa4667S3WAdOHY57gNSvg4L_NaI,662
icalendar/tests/calendars/multiple_calendar_components.ics,sha256=nvZimBFaXEVyTfqUJNA6l5iqT3014e2dQNGW8Wcr754,948
icalendar/tests/calendars/pacific_fiji.ics,sha256=jeW74IQzVzg7PZ9QhgY3LOiMhRt9zmIbvcih5dU0nKA,1222
icalendar/tests/calendars/parsing_error.ics,sha256=SSUZZxW8aOD__J0wdcD5vFI_OV-31HszeflxBKNYM3w,490
icalendar/tests/calendars/parsing_error_in_UTC_offset.ics,sha256=c72-fHFVs6-fyJWQFfkHNl2glhGpKDbo8YcocMBh7rY,189
icalendar/tests/calendars/period_with_timezone.ics,sha256=8_utcIlfMhbTVW9TE2OB3MUspKGvlmmbV9X1UPdKA5U,744
icalendar/tests/calendars/pr_480_summary_with_colon.ics,sha256=53HNwW5fIhCq-1rC3XFppiB0jrSObUWi9gpaLyoex6I,165
icalendar/tests/calendars/property_params.ics,sha256=4lfRhUIIE7TaZ19K95yr8q0D0IpjmYVC3Yl1AN4kvFQ,660
icalendar/tests/calendars/rfc_5545_RDATE_example.ics,sha256=QPAM2WRTfIVjXyUBepNstvhDJcjr6GyZlL-I51hFb9g,756
icalendar/tests/calendars/rfc_6868.ics,sha256=nURkH2HnHZXnkSSTirypQLmyFr-IIeVlpsZpfA1G1So,169
icalendar/tests/calendars/rfc_7529.ics,sha256=VEPf9NFl2hJgJ7hvTTr0LAbz42B-MxK3g8yuOPUj5P4,635
icalendar/tests/calendars/small_bad_calendar.ics,sha256=FfwBkz9k5fOKf4QzgieFV2Hpk71Z1GkBK6-5HxVCUHE,40
icalendar/tests/calendars/time.ics,sha256=Rmdx4dNgyxkdOU-ZCndNZreq8uqz9WYaJmz_QIrQ0yI,59
icalendar/tests/calendars/timezone_rdate.ics,sha256=9wZ1zds7ha4vC8so4AzF71yz0Yw1YCKi5LTaBKsfkq8,1109
icalendar/tests/calendars/timezone_same_start.ics,sha256=ehLTQtA_mwgZk2NLXDOzr9_K8yYdYAGthc8Br-DsvUY,685
icalendar/tests/calendars/timezone_same_start_and_offset.ics,sha256=IrMUtrcECSl1iIb6cJ9AVy1MQw81U7qYTNTEJuaVsmA,475
icalendar/tests/calendars/timezoned.ics,sha256=AP5V2Qe15tWKjM8wddVDiFSQgJftVTNiclIEYGv4vko,812
icalendar/tests/calendars/x_location.ics,sha256=mEvrn2sny3FRmLKe81b2534C-d1eUqarENtuZvd9sTw,1402
icalendar/tests/conftest.py,sha256=AdpAuWQXuo-gGjhKLruuXXr-QU0Ki7MlA_Zb750sb6k,9355
icalendar/tests/events/event_with_escaped_character1.ics,sha256=zF8gT7zwQpd6M5nzhUgOKZ5o-BlQpqCkI7B7UULzdvw,60
icalendar/tests/events/event_with_escaped_character2.ics,sha256=XNIPhLa6q4ndJqxbR7ePeUcQGrUGXsAlDe9FK7iJl50,60
icalendar/tests/events/event_with_escaped_character3.ics,sha256=bw9rORvFz9QFqt0QjM_18-ltthQEcCugmKSGbzElUN8,60
icalendar/tests/events/event_with_escaped_character4.ics,sha256=dPnEcJG-dsq_Q_wxnpEJNqiQs2aWHOxGKNUyDyAxWBA,60
icalendar/tests/events/event_with_escaped_characters.ics,sha256=ckmvjK8xXcvm71THlyrW8uojsDE7z6TENIepjhxPbNY,104
icalendar/tests/events/event_with_recurrence.ics,sha256=xcz-snNFFX7m3B-M6MtrbRZglBbzIWxIrCwwnK-VoXc,200
icalendar/tests/events/event_with_recurrence_exdates_on_different_lines.ics,sha256=isbt8cZbZ9uHnxXk3-YsSAAlp1xPtDT2C_NFPwY00rE,456
icalendar/tests/events/event_with_rsvp.ics,sha256=e3k2PahxC7XNkHXXXvxU8Q1OKbGzEMd0_gjrV3J3upc,73
icalendar/tests/events/event_with_unicode_fields.ics,sha256=WH2XqEfW0iKrouy3jBawpyfClDN6R7zISjRNgJUq4kk,254
icalendar/tests/events/event_with_unicode_organizer.ics,sha256=3T_e94HikWETtTxcv-WINKxOrwl-ObZ5S342FoTAP1E,86
icalendar/tests/events/issue_100_transformed_doctests_into_unittests.ics,sha256=TByjhHIJJRBIP0vNUIgy2D3TFHFhglKjOgjSbU3sswI,53
icalendar/tests/events/issue_101_icalendar_chokes_on_umlauts_in_organizer.ics,sha256=yfrDws5GOzxFM0XLkBQFQ_n9H9vyMXWEQx-DQYnvQiY,359
icalendar/tests/events/issue_104_mark_events_broken.ics,sha256=FhIt8k7HQNf-UYAstJUq1WEjad0jaUCWTKYmkVCJogI,164
icalendar/tests/events/issue_112_missing_tzinfo_on_exdate.ics,sha256=IenYrPhj9BaX0Ojmu8seECuHpW74jEe8iROcalMrgf4,670
icalendar/tests/events/issue_156_RDATE_with_PERIOD.ics,sha256=pAk4wI1ivi65cBrGwt7HxvRyKQFgGvGsNFNglVQVsSA,170
icalendar/tests/events/issue_156_RDATE_with_PERIOD_list.ics,sha256=u8FPZwmJFWZC4dXNemgw85a4URfZC9PN2qpnOc9m-RM,198
icalendar/tests/events/issue_157_removes_trailing_semicolon.ics,sha256=6ZGaQltWRyUBc2ZCnrgSqU4NYj1kxij-BxAHVlkTsnk,92
icalendar/tests/events/issue_184_broken_representation_of_period.ics,sha256=xGHlViVfPzQPjKnsG8Ntr0dnWPkXu6pFtw6q3r96XmE,131
icalendar/tests/events/issue_464_invalid_rdate.ics,sha256=DQm7c4S2Wgfl69Gvaumj-zADZJzZ5tkzQvPNXMNQJ74,193
icalendar/tests/events/issue_53_description_parsed_properly.ics,sha256=HSjknOesLHfr9b0MP8f97-5k7qmoe9Mf5-7lrr1wC5w,739
icalendar/tests/events/issue_64_event_with_ascii_summary.ics,sha256=d1NMR5EImhYrzR7oUNmPvK5Jxa5LDd8roN9aB-ck0oc,42
icalendar/tests/events/issue_64_event_with_non_ascii_summary.ics,sha256=ir4PpRbsIGtXdssK-jru6ZgmY31XJQINaq6HvhMxvGU,42
icalendar/tests/events/issue_70_rrule_causes_attribute_error.ics,sha256=7qtWoq75oBuEM9dauNSSZnsgeebVvSg_GxuIE0USC4k,279
icalendar/tests/events/issue_82_expected_output.ics,sha256=N7aB6IoCpleUVcnOYSinEDOl9haMnc4ZxQJLcr4h9aU,91
icalendar/tests/events/rfc_9074_example_1.ics,sha256=hztVnUDZ3e6Kp6DSkwKwvt7fJ9KodBd78ta9YKPfZhQ,344
icalendar/tests/events/rfc_9074_example_2.ics,sha256=StG7r8cvBmniI6WKkaBH15DfQ-i8Dso3TtZdoffPmlQ,587
icalendar/tests/events/rfc_9074_example_3.ics,sha256=Jj1KlOo34uUkmkVwY4t5dl-ct1prpj9oGasg1gxbvx4,587
icalendar/tests/events/rfc_9074_example_4.ics,sha256=MRt9Wt7GjfMqV-B_0ezCCFMuuQzPMozvK_LRTocJG1M,616
icalendar/tests/events/rfc_9074_example_proximity.ics,sha256=rr29kD4Yd8bRuMPdDKRYXM2g7KZzZXavHmDicwKenQ4,292
icalendar/tests/fuzzed/__init__.py,sha256=O1_cgbjDwb5mzs-tYy8NtUznwF2vkZmAumss1owgJSo,721
icalendar/tests/fuzzed/__pycache__/__init__.cpython-311.pyc,,
icalendar/tests/fuzzed/__pycache__/test_fuzzed_calendars.cpython-311.pyc,,
icalendar/tests/fuzzed/generate_python_test_cases_from_downloaded_clusterfuzz_test_cases.sh,sha256=0Jc9Msyj_w4skwALytFuJ0vyzlybIjmxbf_m7jlbLPQ,1644
icalendar/tests/fuzzed/test_fuzzed_calendars.py,sha256=Q0qdUcKyC_vUmc-dA4pP21P4nlZjuiEHpT5NXy5NCVg,343
icalendar/tests/hypothesis/__pycache__/test_fuzzing.cpython-311.pyc,,
icalendar/tests/hypothesis/test_fuzzing.py,sha256=p5nKOC4LdPn07CwGxDvYNERFqqetANh262N3g9lFW9Y,1021
icalendar/tests/prop/__init__.py,sha256=H1sqpnGs6CFBQOUoLw7rDL15JS8VzAIZ_N3JUCn3pgs,42
icalendar/tests/prop/__pycache__/__init__.cpython-311.pyc,,
icalendar/tests/prop/__pycache__/test_constructors.cpython-311.pyc,,
icalendar/tests/prop/__pycache__/test_identity_and_equality.cpython-311.pyc,,
icalendar/tests/prop/__pycache__/test_property_values.cpython-311.pyc,,
icalendar/tests/prop/__pycache__/test_unit.cpython-311.pyc,,
icalendar/tests/prop/__pycache__/test_vBinary.cpython-311.pyc,,
icalendar/tests/prop/__pycache__/test_vBoolean.cpython-311.pyc,,
icalendar/tests/prop/__pycache__/test_vCalAddress.cpython-311.pyc,,
icalendar/tests/prop/__pycache__/test_vDDDTypes.cpython-311.pyc,,
icalendar/tests/prop/__pycache__/test_vDatetime.cpython-311.pyc,,
icalendar/tests/prop/__pycache__/test_vPeriod.cpython-311.pyc,,
icalendar/tests/prop/__pycache__/test_vWeekday.cpython-311.pyc,,
icalendar/tests/prop/__pycache__/test_windows_to_olson_mapping.cpython-311.pyc,,
icalendar/tests/prop/test_constructors.py,sha256=HFSfvxzHvf1ZHrkFbXgSG8B4XLk2QBh5rLqX1yLjtPM,3258
icalendar/tests/prop/test_identity_and_equality.py,sha256=-QlTNk9mlipYQ-cvQkABJMK450jdqeLP8quMSfCZKN4,1869
icalendar/tests/prop/test_property_values.py,sha256=ycucOYR-FQjnfPJ7wn4-pKzBacIBbAcHR--S929geEk,649
icalendar/tests/prop/test_unit.py,sha256=gAaxfaMk_4kGiL-694pknm7tyuoRo7ewKacTFLk3-q8,13900
icalendar/tests/prop/test_vBinary.py,sha256=5hiuexUU-li2cdcfB47spjvEROjvQesxkRTBmJqlOzM,1214
icalendar/tests/prop/test_vBoolean.py,sha256=eGXDzrosXvjPi97cLHqrpE5B_qXEZjpT0YaWWkyeHjo,439
icalendar/tests/prop/test_vCalAddress.py,sha256=pVXohea8hgAMx-u12AZkns5XTGZOUm4uV2mBYwaT4v4,1420
icalendar/tests/prop/test_vDDDTypes.py,sha256=Ai8T1HdlPV9oslmBvk7Tae573Tyxyj94I_qUXUSTpJg,989
icalendar/tests/prop/test_vDatetime.py,sha256=d_9qXDqxAjUy--wUQCp3dlBfdidyLdFNC2VpOWvJaKY,1417
icalendar/tests/prop/test_vPeriod.py,sha256=gG7F12q-bG67f9fAOiltuNTEmJhdFG1_9-EHQxE0Gak,2155
icalendar/tests/prop/test_vWeekday.py,sha256=OAHnACj-_ySU8iUMlA1hFI1awAbxwM08qklYTJ-0_7A,622
icalendar/tests/prop/test_windows_to_olson_mapping.py,sha256=xxEsJ02BZSTgcn1TMU6LTTBDge48iIqaDbyFSpx9CDc,722
icalendar/tests/test_bom_calendar.py,sha256=BnnM8ROzW-cqWhWwDSZvE7tMuKYeYsHd4EoNtuuLHPA,159
icalendar/tests/test_cli_tool.py,sha256=hdyPqMGmHlMuuIrYmCBZbdCxTTEJNQhn8lYxPbsMKmw,2728
icalendar/tests/test_components_break_on_bad_ics.py,sha256=RLuNP0bjgqtebwe4Au6ggTpED84oz7Bf5F7YNfVrdVg,1602
icalendar/tests/test_create_release.sh,sha256=x5BL5AsjUnCkfpTI8HwqBGQ49hhIk5EhgH52UEvXUU8,713
icalendar/tests/test_encoding.py,sha256=zK21I-d90fjamKBDFxvbk_MUPxjysTgOOuRBo73MW4Q,3605
icalendar/tests/test_equality.py,sha256=lkge7ERgQBXT4mIorc1olBhSJEfLj-auuTGa_JWRFoY,5644
icalendar/tests/test_examples.py,sha256=XC1QrEHOxCcKKq-mxcumZJ0t4XHW00nut8CgvV5xEFc,2582
icalendar/tests/test_icalendar.py,sha256=hcCa_5MtjZPXT2mYVaqWnEOEmx8nnIuvokLc0pZt8ZU,10974
icalendar/tests/test_issue_116.py,sha256=x7ysJmtrOer1eTGnBHA1m87iCykag1AU5Z97uQbvJBc,949
icalendar/tests/test_issue_165_missing_event.py,sha256=cwEsiKRt63nBwx8VrWMNBGTNCQBZW3b4WvG8fuxtHWg,331
icalendar/tests/test_issue_168_parsing_invalid_calendars_no_warning.py,sha256=fnapZJX2PDXSc_IbQSYXQw3Mwu5UsAn7ZtKZ4ABaCAA,561
icalendar/tests/test_issue_218_parse_calendar.py,sha256=83tl7V0Vas_fYQZkBITzfKR1I7_5FptsQbu-cK7Xfpw,983
icalendar/tests/test_issue_27_period.py,sha256=_jEjai72oRjlptTy587wfA9LtQ_jcn9j5e1LVw25G2M,648
icalendar/tests/test_issue_301_add_rrule_as_string.py,sha256=Gq-dDIojqgPR_7cefDRUVGADbYQBTP1VXMQtE5u6n_Y,410
icalendar/tests/test_issue_318_skip_default_parameters.py,sha256=rlh7e5-pbz2-tWUdaBNguL55g1Zh_HifkmsBhp0pgr4,674
icalendar/tests/test_issue_322_single_strings_characters_split_into_multiple_categories.py,sha256=2Dz4NsZ3iJM9tFlMiay99xjB2sjbG41Yrpzjl92BY0c,392
icalendar/tests/test_issue_336_dateutil_timezone.py,sha256=ZdTe_MDYGS9Q7sadv88zWxEuQCuaQuOe3El7NfSiaDo,861
icalendar/tests/test_issue_348_exception_parsing_value.py,sha256=2uKTqZwkdLepBtuFx6yzKWEBI_Ek0wN2fuzF2-bCsi0,829
icalendar/tests/test_issue_350.py,sha256=0F2iMhb_EVG8zLIqnYkaRhQRz7aTJgwlAfY2mpdpafM,286
icalendar/tests/test_issue_500_vboolean_for_parameter.py,sha256=fFRFMuVleqOM2R5ZTO2yLsMybVuNTvQwhHXU3CEGevo,395
icalendar/tests/test_issue_557_encode_native_parameters.py,sha256=IJl4AFkJ_HRkD5eYW_fTZTwE5KQnKPZgD-SUrOElwTE,5061
icalendar/tests/test_issue_662_component_properties.py,sha256=AEnCt60InY3b5DBTM2DGEO4CFWIBz0WP9NK1ieknHiA,21494
icalendar/tests/test_issue_716_alarm_time_computation.py,sha256=q5hwwxaqf_NJnE34KnSJv429HKHobpsP-Y9tQYMoSqo,15232
icalendar/tests/test_issue_720_uid_property.py,sha256=Xci4300VUPAFq0OBAnx4VdLCx5-bcsUGyGXLr593ppE,245
icalendar/tests/test_issue_722_generate_vtimezone.py,sha256=niIdyVJqRe5iFAY-hSFIj5rpeKw8ujK5K75Rocf-mKI,16520
icalendar/tests/test_issue_798_property_parameters.py,sha256=k4v5jVYYlTMh1_2f_l3_Ns3WaVaoyDvs4TwkKTWQ978,4722
icalendar/tests/test_issue_802.py,sha256=BrNoIF4Ymteu_WOTZ3Qooh7Oa9g7KEQcIHEnEAKRtXs,1425
icalendar/tests/test_issue_828.py,sha256=zuRCCFPsO1ZGUt8O_zWQSwLudzyNYiAZ2r2KB_LMYzc,4349
icalendar/tests/test_issue_836_do_not_quote_tzid.py,sha256=MR-SXMDB4HN52YdV2tau88yGB_JqZb8HkdJ8EnRJpVg,2381
icalendar/tests/test_multiple.py,sha256=kykaUsa97DjBdTs0nsRrs5GOGFME9hJFkeZYMgYlZJs,1001
icalendar/tests/test_oss_fuzz_errors.py,sha256=KIxpAryRmU69__IcobgsgHoPipwZbyA6_pajLmTQKeQ,583
icalendar/tests/test_parsing.py,sha256=GNAuEiF4f1p_IkCtuZhMJw7ONektdmjb07cwM2i0yrg,9158
icalendar/tests/test_period.py,sha256=KV5ehuPoQqXB-lwk3AmxzzG6ryoaBjtWzIEXMSU4xN8,4002
icalendar/tests/test_property_params.py,sha256=HJTXzMha3ZdrUK5upoQCNgdxqNHkYxwV2HpFvVyXTlQ,5182
icalendar/tests/test_pytz_zoneinfo_integration.py,sha256=2g4GQPI_PO4bRvjvJCH-nfhzCoeTwTfQo5guY4D2MCU,3261
icalendar/tests/test_recurrence.py,sha256=4UCIjK96r5_Lh783HhtVT4lIzSXea1lNzp8iuOsTFTA,5091
icalendar/tests/test_rfc_6868.py,sha256=c4ceCXLfgm7UPCDLweQN_3VlfFdYWKIsAS3H1zcKFQ8,3110
icalendar/tests/test_rfc_7529.py,sha256=T_hXtTVCkbSrp9E30YBQk0eeo4j6lEGSghXHg0u4_d8,2507
icalendar/tests/test_rfc_7986.py,sha256=FyNLIJUDAIK2GbFZaRzIobZdNZi6gm71LQA-qoLnfj8,5890
icalendar/tests/test_rfc_7986_categories.py,sha256=hGhDrb_3EcC4IX80DGUZdrIRltJhzN-DUKFZtaSnmEM,2245
icalendar/tests/test_rfc_9074.py,sha256=WQ6KSrbAKJTvNyACq8f1WhRxHrSOxFD2UH_ZxiiwyZ0,1477
icalendar/tests/test_time.py,sha256=zSkeKbTPZKTD0JRoBZt3jKBZ2Ug0kvqZb4IOARlO23w,883
icalendar/tests/test_timezone_identification.py,sha256=dDx4y84cGbP-2Q81hTKpItKTWpzEdD7HGDroyAmt5_g,1180
icalendar/tests/test_timezoned.py,sha256=9vOiXVCc7tlzn4jGs-2ntdykWy1NXfzt-s5Pu3g_AYo,18400
icalendar/tests/test_unit_cal.py,sha256=ctQe3UqMe8eUhhcfK51EvFmgAuuXmRr8INHiOHzofBw,16585
icalendar/tests/test_unit_caselessdict.py,sha256=pQmXjBf6GHeie6IDocw5qfutfCFHKWx2srgTYiT56QA,3608
icalendar/tests/test_unit_parser_tools.py,sha256=xsihLP91N_oOV5ms9CAKy04CkiIM_5lk7rNkmgvnjAc,1190
icalendar/tests/test_unit_tools.py,sha256=sLtZDJx7POpUiwVSUWRR2VzVPaHl5qT49ptfZ_MKfBc,2386
icalendar/tests/test_with_doctest.py,sha256=9jHCwwNdzk9CdgPXWUQRDjdluRXTYR6fHh-N0AMo4W4,3024
icalendar/tests/timezone_ids.py,sha256=GFQM-hHagXTdnJd4-Ik4HjfJb9I1V9mYN85UEzrFyBE,13308
icalendar/tests/timezones/issue_237_brazilia_standard.ics,sha256=67bg-GY1F6EwBrwwXPBJ-i5m1IgTWa4oWlMfrvagPgg,400
icalendar/tests/timezones/issue_53_tzid_parsed_properly.ics,sha256=S9eBMuhGxVgqzcjqcFXRnVDtQemXRUm-KwmZYfYOZwo,434
icalendar/tests/timezones/issue_55_parse_error_on_utc_offset_with_seconds.ics,sha256=OQzQAWv7uAqCxpLyMztp2TyBalYZKTlUYlEH-COoZag,188
icalendar/tests/timezones/pacific_fiji.ics,sha256=KmWDaMGEE6M6pkwnF-nqAV7pTGgaRqMoOuSMfW9VRhs,929
icalendar/timezone/__init__.py,sha256=TWse38Yp5FnR2rOmDn0K9Pbj24Pv8_kXPCbMjJFTMaU,549
icalendar/timezone/__pycache__/__init__.cpython-311.pyc,,
icalendar/timezone/__pycache__/equivalent_timezone_ids.cpython-311.pyc,,
icalendar/timezone/__pycache__/equivalent_timezone_ids_result.cpython-311.pyc,,
icalendar/timezone/__pycache__/provider.cpython-311.pyc,,
icalendar/timezone/__pycache__/pytz.cpython-311.pyc,,
icalendar/timezone/__pycache__/tzid.cpython-311.pyc,,
icalendar/timezone/__pycache__/tzp.cpython-311.pyc,,
icalendar/timezone/__pycache__/windows_to_olson.cpython-311.pyc,,
icalendar/timezone/__pycache__/zoneinfo.cpython-311.pyc,,
icalendar/timezone/equivalent_timezone_ids.py,sha256=2dkQg8AMyDBNqtF9MiirYY66SZoujRzJ2Yvgb7HWjxM,4665
icalendar/timezone/equivalent_timezone_ids_result.py,sha256=TZlo7AABJ2ePXhQakXIcXuwj_wE5G99vxeWsMO54kIA,219402
icalendar/timezone/provider.py,sha256=zk9q9YpFChFdYa--Q_wZkuAp7rcII5HLeW9gVAzo_fM,1598
icalendar/timezone/pytz.py,sha256=ut8Pizhs2EN_v8G4HtA43pcEK_iSZuXkI1rLpt9qdTI,2317
icalendar/timezone/tzid.py,sha256=YB9uJoy1gEICWaDp124UmrRP8kzaUn1BdkL4Hvm1MMM,4093
icalendar/timezone/tzp.py,sha256=e0AXHXbWrvSzLWX0okLBJDs2PcKPpqsmxdhhd5JTGek,4861
icalendar/timezone/windows_to_olson.py,sha256=n7pHvSiBR-C2K-SHp8lKMjF6Fdz8_4lZKUrkft0hEho,5632
icalendar/timezone/zoneinfo.py,sha256=k-xrYuUIxXGlfbviJ0YE5cEpyxlfLXgUpJZ7bYch5QQ,5033
icalendar/tools.py,sha256=zKUieyh5A_jCc5KW6bDCMGhEed__Q7dXK8qw3Ykm0AU,3059
icalendar/version.py,sha256=B7rIFvfKIhqRt7FiUM8UL60St7aOmvXZX21mpPVJHNU,385
