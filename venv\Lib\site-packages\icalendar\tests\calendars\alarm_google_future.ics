BEGIN:<PERSON><PERSON><PERSON><PERSON>R
PRODID:-//Google Inc//Google Calendar 70.9054//EN
VERSION:2.0
CALSCALE:GREGORIAN
METHOD:PUBLISH
X-WR-CALNAME:<PERSON><PERSON>
X-WR-TIMEZONE:Europe/London
BEGIN:VTIMEZONE
TZID:Europe/Berlin
X-LIC-LOCATION:Europe/Berlin
BEGIN:DAYLIGHT
TZOFFSETFROM:+0100
TZOFFSETTO:+0200
TZNAME:GMT+2
DTSTART:19700329T020000
RRULE:FREQ=YEARLY;BYMONTH=3;BYDAY=-1SU
END:DAYLIGHT
BEGIN:STANDARD
TZOFFSETFROM:+0200
TZOFFSETTO:+0100
TZNAME:GMT+1
DTSTART:19701025T030000
RRULE:FREQ=YEARLY;BYMONTH=10;BYDAY=-1SU
END:STANDARD
END:VTIMEZONE
BEGIN:VEVENT
DTSTART:20241004T181500Z
DTEND:20241004T190000Z
DTSTAMP:20241004T175945Z
UID:<EMAIL>
CREATED:20241004T175920Z
LAST-MODIFIED:20241004T175928Z
SEQUENCE:0
STATUS:CONFIRMED
SUMMARY:event with alarms
TRANSP:OPAQUE
BEGIN:VALARM
ACTION:DISPLAY
TRIGGER:-P0DT0H10M0S
DESCRIPTION:This is an event reminder
END:VALARM
BEGIN:VALARM
ACTION:DISPLAY
TRIGGER:-P0DT0H14M0S
DESCRIPTION:This is an event reminder
END:VALARM
BEGIN:VALARM
ACTION:EMAIL
ATTENDEE:mailto:<EMAIL>
TRIGGER:-P0DT0H15M0S
DESCRIPTION:This is an event reminder
SUMMARY:Alarm notification
END:VALARM
BEGIN:VALARM
ACTION:DISPLAY
TRIGGER:-P0DT0H15M0S
DESCRIPTION:This is an event reminder
END:VALARM
END:VEVENT
END:VCALENDAR
