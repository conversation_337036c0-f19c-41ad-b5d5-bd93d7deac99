BEGIN:<PERSON><PERSON><PERSON>DAR
BEGIN:VTIMEZONE
TZID:MyTimezone
BEGIN:STANDARD
COMMENT:The timezone starts at 2024-01-01 with +12h offset
TZOFFSETFROM:+1000
TZOFFSETTO:+1200
DTSTART:20240101T000000
TZNAME:winter
END:STANDARD
BEGIN:DAYLIGHT
COMMENT:The timezone goes from +12h to +10h at 8 am
TZOFFSETFROM:+1200
TZOFFSETTO:+1000
DTSTART:20240505T080000
TZNAME:summer
END:DAYLIGHT
END:VTIMEZONE
BEGIN:VEVENT
UID:0
SUMMARY:This event is clearly in winter
DTSTART;TZID=MyTimezone;VALUE=DATE-TIME:20240303T080000
X-TZNAME:winter
END:VEVENT
BEGIN:VEVENT
UID:1
SUMMARY:This event is clearly in summer
X-TZNAME:summer
DTSTART;TZID=MyTimezone;VALUE=DATE-TIME:20240803T080000
END:VEVENT
BEGIN:VEVENT
UID:2
SUMMARY:Transition is from 8am -> 6am, so 8:00:01 is summer
X-TZNAME:summer
DTSTART;TZID=MyTimezone;VALUE=DATE-TIME:20240505T080001
END:VEVENT
BEGIN:VEVENT
UID:3
SUMMARY:Transition is from 8am -> 6am, so 7:00:01 is winter
  RFC5545 does not allow us to be at the later TZ.
X-TZNAME:winter
DTSTART;TZID=MyTimezone;VALUE=DATE-TIME:20240505T070001
END:VEVENT
END:VCALENDAR