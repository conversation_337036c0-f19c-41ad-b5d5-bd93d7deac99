BEGIN:<PERSON><PERSON><PERSON>DAR
VERSION:2.0
BEGIN:VTIMEZONE
TZID:posix/Europe/Vaduz
BEGIN:STANDARD
TZNAME:CET
TZOFFSETFROM:+002946
TZOFFSETTO:+0100
DTSTART:19011213T211538
RDATE;VALUE=DATE-TIME:19011213T211538
END:STANDARD
BEGIN:DAYLIGHT
TZNAME:CEST
TZOFFSETFROM:+0100
TZOFFSETTO:+0200
DTSTART:19810329T020000
RRULE:FREQ=YEARLY;BYDAY=-1SU;BYMONTH=3
END:DAYLIGHT
BEGIN:DAYLIGHT
TZNAME:CEST
TZOFFSETFROM:+0100
TZOFFSETTO:+0200
DTSTART:19410505T010000
RDATE;VALUE=DATE-TIME:19410505T010000
RDATE;VALUE=DATE-TIME:19420504T010000
END:DAYLIGHT
BEGIN:STANDARD
TZNAME:CET
TZOFFSETFROM:+0200
TZOFFSETTO:+0100
DTSTART:19810927T030000
RRULE:FREQ=YEARLY;COUNT=15;BYDAY=-1SU;BYMONTH=9
END:STANDARD
BEGIN:STANDARD
TZNAME:CET
TZOFFSETFROM:+0200
TZOFFSETTO:+0100
DTSTART:19961027T030000
RRULE:FREQ=YEARLY;BYDAY=-1SU;BYMONTH=10
END:STANDARD
BEGIN:STANDARD
TZNAME:CET
TZOFFSETFROM:+0200
TZOFFSETTO:+0100
DTSTART:19411006T020000
RDATE;VALUE=DATE-TIME:19411006T020000
RDATE;VALUE=DATE-TIME:19421005T020000
END:STANDARD
END:VTIMEZONE
BEGIN:VEVENT
UID:123
DTSTART;TZID=posix/Europe/Vaduz:20120213T100000
SUMMARY=testevent
END:VEVENT
END:VCALENDAR
