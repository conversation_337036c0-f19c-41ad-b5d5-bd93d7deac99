"""This file is automatically generated by equivalent_timezone_ids.py"""

import datetime


lookup = (
    datetime.datetime(1970, 1, 2, 0, 0),
    {
        datetime.timedelta(days=-1, seconds=43200): (
            datetime.datetime(1979, 10, 2, 0, 0),
            {
                datetime.timedelta(days=-1, seconds=43200): (
                    datetime.datetime(1993, 8, 22, 21, 0),
                    {
                        datetime.timedelta(days=-1, seconds=43200): {"Etc/GMT+12"},
                        datetime.timedelta(seconds=43200): {
                            "Kwajalein",
                            "Pacific/Kwajalein",
                        },
                    },
                ),
                datetime.timedelta(days=-1, seconds=46800): {
                    "Pacific/Enderbury",
                    "Pacific/Kanton",
                },
            },
        ),
        datetime.timedelta(days=-1, seconds=46800): (
            datetime.datetime(1970, 4, 26, 15, 0),
            {
                datetime.timedelta(days=-1, seconds=46800): (
                    datetime.datetime(2010, 9, 26, 15, 0),
                    {
                        datetime.timedelta(days=-1, seconds=46800): (
                            datetime.datetime(2011, 12, 31, 21, 0),
                            {
                                datetime.timedelta(days=-1, seconds=46800): {
                                    "Etc/GMT+11",
                                    "Pacific/Midway",
                                    "Pacific/Niue",
                                    "Pacific/Pago_Pago",
                                    "Pacific/Samoa",
                                    "US/Samoa",
                                },
                                datetime.timedelta(seconds=46800): {"Pacific/Fakaofo"},
                            },
                        ),
                        datetime.timedelta(days=-1, seconds=50400): {"Pacific/Apia"},
                    },
                ),
                datetime.timedelta(days=-1, seconds=50400): (
                    datetime.datetime(1983, 10, 30, 21, 0),
                    {
                        datetime.timedelta(days=-1, seconds=50400): {
                            "America/Adak",
                            "America/Atka",
                            "US/Aleutian",
                        },
                        datetime.timedelta(days=-1, seconds=54000): {"America/Nome"},
                    },
                ),
            },
        ),
        datetime.timedelta(days=-1, seconds=48000): {"Pacific/Kiritimati"},
        datetime.timedelta(days=-1, seconds=48600): {"Pacific/Rarotonga"},
        datetime.timedelta(days=-1, seconds=50400): (
            datetime.datetime(1970, 4, 26, 15, 0),
            {
                datetime.timedelta(days=-1, seconds=50400): {
                    "Etc/GMT+10",
                    "HST",
                    "Pacific/Honolulu",
                    "Pacific/Johnston",
                    "Pacific/Tahiti",
                    "US/Hawaii",
                },
                datetime.timedelta(days=-1, seconds=54000): {
                    "America/Anchorage",
                    "US/Alaska",
                },
            },
        ),
        datetime.timedelta(days=-1, seconds=52200): {"Pacific/Marquesas"},
        datetime.timedelta(days=-1, seconds=54000): (
            datetime.datetime(1970, 4, 26, 15, 0),
            {
                datetime.timedelta(days=-1, seconds=54000): (
                    datetime.datetime(1973, 10, 28, 18, 0),
                    {
                        datetime.timedelta(days=-1, seconds=54000): {
                            "Etc/GMT+9",
                            "Pacific/Gambier",
                        },
                        datetime.timedelta(days=-1, seconds=57600): {"America/Dawson"},
                    },
                ),
                datetime.timedelta(days=-1, seconds=57600): {"America/Yakutat"},
            },
        ),
        datetime.timedelta(days=-1, seconds=55800): {"Pacific/Pitcairn"},
        datetime.timedelta(days=-1, seconds=57600): (
            datetime.datetime(1970, 4, 26, 15, 0),
            {
                datetime.timedelta(days=-1, seconds=57600): (
                    datetime.datetime(1972, 4, 30, 21, 0),
                    {
                        datetime.timedelta(days=-1, seconds=57600): (
                            datetime.datetime(1976, 4, 25, 15, 0),
                            {
                                datetime.timedelta(days=-1, seconds=57600): (
                                    datetime.datetime(1980, 4, 27, 15, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=57600): {
                                            "Etc/GMT+8"
                                        },
                                        datetime.timedelta(days=-1, seconds=61200): {
                                            "America/Whitehorse",
                                            "Canada/Yukon",
                                        },
                                    },
                                ),
                                datetime.timedelta(days=-1, seconds=61200): {
                                    "America/Ensenada",
                                    "America/Santa_Isabel",
                                    "America/Tijuana",
                                    "Mexico/BajaNorte",
                                },
                            },
                        ),
                        datetime.timedelta(days=-1, seconds=61200): {"America/Inuvik"},
                    },
                ),
                datetime.timedelta(days=-1, seconds=61200): (
                    datetime.datetime(1972, 10, 30, 0, 0),
                    {
                        datetime.timedelta(days=-1, seconds=57600): (
                            datetime.datetime(1974, 1, 6, 18, 0),
                            {
                                datetime.timedelta(days=-1, seconds=57600): (
                                    datetime.datetime(2015, 11, 1, 21, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=57600): {
                                            "America/Vancouver",
                                            "Canada/Pacific",
                                        },
                                        datetime.timedelta(days=-1, seconds=61200): {
                                            "America/Fort_Nelson"
                                        },
                                    },
                                ),
                                datetime.timedelta(days=-1, seconds=61200): (
                                    datetime.datetime(1980, 4, 28, 0, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=57600): {
                                            "America/Juneau"
                                        },
                                        datetime.timedelta(days=-1, seconds=61200): (
                                            datetime.datetime(1983, 10, 30, 21, 0),
                                            {
                                                datetime.timedelta(
                                                    days=-1, seconds=54000
                                                ): {"America/Sitka"},
                                                datetime.timedelta(
                                                    days=-1, seconds=57600
                                                ): (
                                                    datetime.datetime(
                                                        1984, 4, 29, 21, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            days=-1, seconds=57600
                                                        ): {"America/Metlakatla"},
                                                        datetime.timedelta(
                                                            days=-1, seconds=61200
                                                        ): {
                                                            "America/Los_Angeles",
                                                            "PST8PDT",
                                                            "US/Pacific",
                                                        },
                                                    },
                                                ),
                                            },
                                        ),
                                    },
                                ),
                            },
                        ),
                        datetime.timedelta(days=-1, seconds=61200): {
                            "America/Dawson_Creek"
                        },
                    },
                ),
            },
        ),
        datetime.timedelta(days=-1, seconds=61200): (
            datetime.datetime(1970, 4, 26, 15, 0),
            {
                datetime.timedelta(days=-1, seconds=61200): (
                    datetime.datetime(1972, 4, 30, 21, 0),
                    {
                        datetime.timedelta(days=-1, seconds=61200): (
                            datetime.datetime(1996, 4, 7, 15, 0),
                            {
                                datetime.timedelta(days=-1, seconds=61200): {
                                    "America/Creston",
                                    "America/Phoenix",
                                    "Etc/GMT+7",
                                    "MST",
                                    "US/Arizona",
                                },
                                datetime.timedelta(days=-1, seconds=64800): (
                                    datetime.datetime(1999, 4, 4, 15, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=61200): {
                                            "America/Hermosillo"
                                        },
                                        datetime.timedelta(days=-1, seconds=64800): (
                                            datetime.datetime(2010, 4, 5, 0, 0),
                                            {
                                                datetime.timedelta(
                                                    days=-1, seconds=64800
                                                ): {
                                                    "America/Mazatlan",
                                                    "Mexico/BajaSur",
                                                },
                                                datetime.timedelta(
                                                    days=-1, seconds=68400
                                                ): {"America/Bahia_Banderas"},
                                            },
                                        ),
                                    },
                                ),
                            },
                        ),
                        datetime.timedelta(days=-1, seconds=64800): (
                            datetime.datetime(1972, 10, 29, 21, 0),
                            {
                                datetime.timedelta(days=-1, seconds=61200): (
                                    datetime.datetime(1999, 11, 1, 0, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=61200): {
                                            "America/Edmonton",
                                            "America/Yellowknife",
                                            "Canada/Mountain",
                                        },
                                        datetime.timedelta(days=-1, seconds=64800): {
                                            "America/Cambridge_Bay"
                                        },
                                    },
                                ),
                                datetime.timedelta(days=-1, seconds=64800): {
                                    "America/Swift_Current"
                                },
                            },
                        ),
                    },
                ),
                datetime.timedelta(days=-1, seconds=64800): (
                    datetime.datetime(1974, 1, 6, 18, 0),
                    {
                        datetime.timedelta(days=-1, seconds=61200): {"America/Boise"},
                        datetime.timedelta(days=-1, seconds=64800): (
                            datetime.datetime(1992, 10, 25, 18, 0),
                            {
                                datetime.timedelta(days=-1, seconds=61200): (
                                    datetime.datetime(2003, 10, 26, 15, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=61200): (
                                            datetime.datetime(2010, 11, 7, 21, 0),
                                            {
                                                datetime.timedelta(
                                                    days=-1, seconds=61200
                                                ): {
                                                    "America/Denver",
                                                    "America/Shiprock",
                                                    "MST7MDT",
                                                    "Navajo",
                                                    "US/Mountain",
                                                },
                                                datetime.timedelta(
                                                    days=-1, seconds=64800
                                                ): {"America/North_Dakota/Beulah"},
                                            },
                                        ),
                                        datetime.timedelta(days=-1, seconds=64800): {
                                            "America/North_Dakota/New_Salem"
                                        },
                                    },
                                ),
                                datetime.timedelta(days=-1, seconds=64800): {
                                    "America/North_Dakota/Center"
                                },
                            },
                        ),
                    },
                ),
            },
        ),
        datetime.timedelta(days=-1, seconds=64800): (
            datetime.datetime(1970, 3, 29, 15, 0),
            {
                datetime.timedelta(days=-1, seconds=61200): {
                    "Chile/EasterIsland",
                    "Pacific/Easter",
                },
                datetime.timedelta(days=-1, seconds=64800): (
                    datetime.datetime(1970, 4, 26, 15, 0),
                    {
                        datetime.timedelta(days=-1, seconds=64800): (
                            datetime.datetime(1972, 4, 30, 21, 0),
                            {
                                datetime.timedelta(days=-1, seconds=64800): (
                                    datetime.datetime(1973, 5, 1, 15, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=64800): (
                                            datetime.datetime(1973, 11, 25, 18, 0),
                                            {
                                                datetime.timedelta(
                                                    days=-1, seconds=64800
                                                ): (
                                                    datetime.datetime(
                                                        1973, 12, 5, 21, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            days=-1, seconds=64800
                                                        ): (
                                                            datetime.datetime(
                                                                1979, 2, 26, 0, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=64800,
                                                                ): (
                                                                    datetime.datetime(
                                                                        1981,
                                                                        12,
                                                                        27,
                                                                        0,
                                                                        0,
                                                                    ),
                                                                    {
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=64800,
                                                                        ): (
                                                                            datetime.datetime(
                                                                                1987,
                                                                                5,
                                                                                4,
                                                                                0,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=64800,
                                                                                ): (
                                                                                    datetime.datetime(
                                                                                        1988,
                                                                                        4,
                                                                                        4,
                                                                                        0,
                                                                                        0,
                                                                                    ),
                                                                                    {
                                                                                        datetime.timedelta(
                                                                                            days=-1,
                                                                                            seconds=64800,
                                                                                        ): (
                                                                                            datetime.datetime(
                                                                                                1996,
                                                                                                4,
                                                                                                7,
                                                                                                15,
                                                                                                0,
                                                                                            ),
                                                                                            {
                                                                                                datetime.timedelta(
                                                                                                    days=-1,
                                                                                                    seconds=64800,
                                                                                                ): {
                                                                                                    "America/Regina",
                                                                                                    "Canada/Saskatchewan",
                                                                                                    "Etc/GMT+6",
                                                                                                },
                                                                                                datetime.timedelta(
                                                                                                    days=-1,
                                                                                                    seconds=68400,
                                                                                                ): (
                                                                                                    datetime.datetime(
                                                                                                        1998,
                                                                                                        4,
                                                                                                        5,
                                                                                                        15,
                                                                                                        0,
                                                                                                    ),
                                                                                                    {
                                                                                                        datetime.timedelta(
                                                                                                            days=-1,
                                                                                                            seconds=64800,
                                                                                                        ): (
                                                                                                            datetime.datetime(
                                                                                                                2010,
                                                                                                                3,
                                                                                                                14,
                                                                                                                21,
                                                                                                                0,
                                                                                                            ),
                                                                                                            {
                                                                                                                datetime.timedelta(
                                                                                                                    days=-1,
                                                                                                                    seconds=61200,
                                                                                                                ): {
                                                                                                                    "America/Chihuahua"
                                                                                                                },
                                                                                                                datetime.timedelta(
                                                                                                                    days=-1,
                                                                                                                    seconds=64800,
                                                                                                                ): {
                                                                                                                    "America/Ciudad_Juarez",
                                                                                                                    "America/Ojinaga",
                                                                                                                },
                                                                                                            },
                                                                                                        ),
                                                                                                        datetime.timedelta(
                                                                                                            days=-1,
                                                                                                            seconds=68400,
                                                                                                        ): {
                                                                                                            "America/Mexico_City",
                                                                                                            "Mexico/General",
                                                                                                        },
                                                                                                    },
                                                                                                ),
                                                                                            },
                                                                                        ),
                                                                                        datetime.timedelta(
                                                                                            days=-1,
                                                                                            seconds=68400,
                                                                                        ): (
                                                                                            datetime.datetime(
                                                                                                2010,
                                                                                                3,
                                                                                                14,
                                                                                                21,
                                                                                                0,
                                                                                            ),
                                                                                            {
                                                                                                datetime.timedelta(
                                                                                                    days=-1,
                                                                                                    seconds=64800,
                                                                                                ): {
                                                                                                    "America/Monterrey"
                                                                                                },
                                                                                                datetime.timedelta(
                                                                                                    days=-1,
                                                                                                    seconds=68400,
                                                                                                ): {
                                                                                                    "America/Matamoros"
                                                                                                },
                                                                                            },
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=68400,
                                                                                ): (
                                                                                    datetime.datetime(
                                                                                        2006,
                                                                                        5,
                                                                                        8,
                                                                                        0,
                                                                                        0,
                                                                                    ),
                                                                                    {
                                                                                        datetime.timedelta(
                                                                                            days=-1,
                                                                                            seconds=64800,
                                                                                        ): {
                                                                                            "America/El_Salvador"
                                                                                        },
                                                                                        datetime.timedelta(
                                                                                            days=-1,
                                                                                            seconds=68400,
                                                                                        ): {
                                                                                            "America/Tegucigalpa"
                                                                                        },
                                                                                    },
                                                                                ),
                                                                            },
                                                                        ),
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=68400,
                                                                        ): (
                                                                            datetime.datetime(
                                                                                1982,
                                                                                11,
                                                                                2,
                                                                                18,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=64800,
                                                                                ): {
                                                                                    "America/Merida"
                                                                                },
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=68400,
                                                                                ): {
                                                                                    "America/Cancun"
                                                                                },
                                                                            },
                                                                        ),
                                                                    },
                                                                ),
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=68400,
                                                                ): {
                                                                    "America/Costa_Rica"
                                                                },
                                                            },
                                                        ),
                                                        datetime.timedelta(
                                                            days=-1, seconds=68400
                                                        ): {"America/Belize"},
                                                    },
                                                ),
                                                datetime.timedelta(
                                                    days=-1, seconds=68400
                                                ): {"America/Guatemala"},
                                            },
                                        ),
                                        datetime.timedelta(days=-1, seconds=68400): {
                                            "America/Managua"
                                        },
                                    },
                                ),
                                datetime.timedelta(days=-1, seconds=68400): (
                                    datetime.datetime(2006, 10, 29, 21, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=64800): {
                                            "America/Rankin_Inlet"
                                        },
                                        datetime.timedelta(days=-1, seconds=68400): {
                                            "America/Resolute"
                                        },
                                    },
                                ),
                            },
                        ),
                        datetime.timedelta(days=-1, seconds=68400): (
                            datetime.datetime(1974, 1, 6, 18, 0),
                            {
                                datetime.timedelta(days=-1, seconds=64800): {
                                    "America/Rainy_River",
                                    "America/Winnipeg",
                                    "Canada/Central",
                                },
                                datetime.timedelta(days=-1, seconds=68400): (
                                    datetime.datetime(1977, 10, 31, 0, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=64800): (
                                            datetime.datetime(1991, 10, 27, 18, 0),
                                            {
                                                datetime.timedelta(
                                                    days=-1, seconds=64800
                                                ): (
                                                    datetime.datetime(
                                                        2000, 10, 29, 15, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            days=-1, seconds=64800
                                                        ): {
                                                            "America/Chicago",
                                                            "CST6CDT",
                                                            "US/Central",
                                                        },
                                                        datetime.timedelta(
                                                            days=-1, seconds=68400
                                                        ): {
                                                            "America/Kentucky/Monticello"
                                                        },
                                                    },
                                                ),
                                                datetime.timedelta(
                                                    days=-1, seconds=68400
                                                ): {
                                                    "America/Indiana/Knox",
                                                    "America/Knox_IN",
                                                    "US/Indiana-Starke",
                                                },
                                            },
                                        ),
                                        datetime.timedelta(days=-1, seconds=68400): {
                                            "America/Indiana/Petersburg"
                                        },
                                    },
                                ),
                            },
                        ),
                    },
                ),
            },
        ),
        datetime.timedelta(days=-1, seconds=68400): (
            datetime.datetime(1970, 4, 26, 15, 0),
            {
                datetime.timedelta(days=-1, seconds=68400): (
                    datetime.datetime(1972, 4, 30, 21, 0),
                    {
                        datetime.timedelta(days=-1, seconds=68400): (
                            datetime.datetime(1973, 4, 29, 21, 0),
                            {
                                datetime.timedelta(days=-1, seconds=68400): (
                                    datetime.datetime(1973, 10, 28, 21, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=64800): {
                                            "America/Menominee"
                                        },
                                        datetime.timedelta(days=-1, seconds=68400): (
                                            datetime.datetime(1974, 1, 6, 15, 0),
                                            {
                                                datetime.timedelta(
                                                    days=-1, seconds=68400
                                                ): (
                                                    datetime.datetime(
                                                        1979, 4, 29, 21, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            days=-1, seconds=68400
                                                        ): (
                                                            datetime.datetime(
                                                                1983, 5, 8, 18, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=68400,
                                                                ): (
                                                                    datetime.datetime(
                                                                        1985,
                                                                        11,
                                                                        2,
                                                                        15,
                                                                        0,
                                                                    ),
                                                                    {
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=68400,
                                                                        ): (
                                                                            datetime.datetime(
                                                                                1986,
                                                                                1,
                                                                                1,
                                                                                18,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=64800,
                                                                                ): {
                                                                                    "Pacific/Galapagos"
                                                                                },
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=68400,
                                                                                ): (
                                                                                    datetime.datetime(
                                                                                        1992,
                                                                                        5,
                                                                                        4,
                                                                                        0,
                                                                                        0,
                                                                                    ),
                                                                                    {
                                                                                        datetime.timedelta(
                                                                                            days=-1,
                                                                                            seconds=68400,
                                                                                        ): (
                                                                                            datetime.datetime(
                                                                                                1992,
                                                                                                11,
                                                                                                28,
                                                                                                15,
                                                                                                0,
                                                                                            ),
                                                                                            {
                                                                                                datetime.timedelta(
                                                                                                    days=-1,
                                                                                                    seconds=68400,
                                                                                                ): {
                                                                                                    "America/Atikokan",
                                                                                                    "America/Cayman",
                                                                                                    "America/Coral_Harbour",
                                                                                                    "America/Panama",
                                                                                                    "EST",
                                                                                                    "Etc/GMT+5",
                                                                                                },
                                                                                                datetime.timedelta(
                                                                                                    days=-1,
                                                                                                    seconds=72000,
                                                                                                ): {
                                                                                                    "America/Guayaquil"
                                                                                                },
                                                                                            },
                                                                                        ),
                                                                                        datetime.timedelta(
                                                                                            days=-1,
                                                                                            seconds=72000,
                                                                                        ): {
                                                                                            "America/Bogota"
                                                                                        },
                                                                                    },
                                                                                ),
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=72000,
                                                                                ): {
                                                                                    "America/Lima"
                                                                                },
                                                                            },
                                                                        ),
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=72000,
                                                                        ): (
                                                                            datetime.datetime(
                                                                                1993,
                                                                                10,
                                                                                17,
                                                                                15,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=68400,
                                                                                ): {
                                                                                    "America/Porto_Acre",
                                                                                    "America/Rio_Branco",
                                                                                    "Brazil/Acre",
                                                                                },
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=72000,
                                                                                ): {
                                                                                    "America/Eirunepe"
                                                                                },
                                                                            },
                                                                        ),
                                                                    },
                                                                ),
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=72000,
                                                                ): {
                                                                    "America/Port-au-Prince"
                                                                },
                                                            },
                                                        ),
                                                        datetime.timedelta(
                                                            days=-1, seconds=72000
                                                        ): {"America/Grand_Turk"},
                                                    },
                                                ),
                                                datetime.timedelta(
                                                    days=-1, seconds=72000
                                                ): {"America/Jamaica", "Jamaica"},
                                            },
                                        ),
                                    },
                                ),
                                datetime.timedelta(days=-1, seconds=72000): {
                                    "America/Detroit",
                                    "US/Michigan",
                                },
                            },
                        ),
                        datetime.timedelta(days=-1, seconds=72000): {
                            "America/Iqaluit",
                            "America/Pangnirtung",
                        },
                    },
                ),
                datetime.timedelta(days=-1, seconds=72000): (
                    datetime.datetime(1971, 4, 25, 15, 0),
                    {
                        datetime.timedelta(days=-1, seconds=68400): (
                            datetime.datetime(2006, 4, 2, 15, 0),
                            {
                                datetime.timedelta(days=-1, seconds=68400): (
                                    datetime.datetime(2007, 3, 12, 3, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=68400): (
                                            datetime.datetime(2007, 11, 4, 15, 0),
                                            {
                                                datetime.timedelta(
                                                    days=-1, seconds=64800
                                                ): {"America/Indiana/Tell_City"},
                                                datetime.timedelta(
                                                    days=-1, seconds=68400
                                                ): {"America/Indiana/Vincennes"},
                                            },
                                        ),
                                        datetime.timedelta(days=-1, seconds=72000): {
                                            "America/Indiana/Winamac"
                                        },
                                    },
                                ),
                                datetime.timedelta(days=-1, seconds=72000): {
                                    "America/Fort_Wayne",
                                    "America/Indiana/Indianapolis",
                                    "America/Indianapolis",
                                    "US/East-Indiana",
                                },
                            },
                        ),
                        datetime.timedelta(days=-1, seconds=72000): (
                            datetime.datetime(1972, 10, 8, 12, 0),
                            {
                                datetime.timedelta(days=-1, seconds=68400): {
                                    "America/Havana",
                                    "Cuba",
                                },
                                datetime.timedelta(days=-1, seconds=72000): (
                                    datetime.datetime(1973, 4, 29, 15, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=68400): {
                                            "America/Indiana/Vevay"
                                        },
                                        datetime.timedelta(days=-1, seconds=72000): (
                                            datetime.datetime(1974, 1, 6, 18, 0),
                                            {
                                                datetime.timedelta(
                                                    days=-1, seconds=68400
                                                ): (
                                                    datetime.datetime(
                                                        1974, 4, 28, 15, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            days=-1, seconds=68400
                                                        ): (
                                                            datetime.datetime(
                                                                1976, 4, 26, 0, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=68400,
                                                                ): {
                                                                    "America/Indiana/Marengo"
                                                                },
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=72000,
                                                                ): {
                                                                    "America/Kentucky/Louisville",
                                                                    "America/Louisville",
                                                                },
                                                            },
                                                        ),
                                                        datetime.timedelta(
                                                            days=-1, seconds=72000
                                                        ): {
                                                            "America/Montreal",
                                                            "America/Nassau",
                                                            "America/Nipigon",
                                                            "America/Thunder_Bay",
                                                            "America/Toronto",
                                                            "Canada/Eastern",
                                                        },
                                                    },
                                                ),
                                                datetime.timedelta(
                                                    days=-1, seconds=72000
                                                ): {
                                                    "America/New_York",
                                                    "EST5EDT",
                                                    "US/Eastern",
                                                },
                                            },
                                        ),
                                    },
                                ),
                            },
                        ),
                    },
                ),
            },
        ),
        datetime.timedelta(days=-1, seconds=70200): {"America/Santo_Domingo"},
        datetime.timedelta(days=-1, seconds=72000): (
            datetime.datetime(1970, 4, 26, 15, 0),
            {
                datetime.timedelta(days=-1, seconds=72000): (
                    datetime.datetime(1972, 4, 30, 21, 0),
                    {
                        datetime.timedelta(days=-1, seconds=72000): (
                            datetime.datetime(1972, 10, 2, 0, 0),
                            {
                                datetime.timedelta(days=-1, seconds=72000): (
                                    datetime.datetime(1974, 4, 28, 21, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=72000): (
                                            datetime.datetime(1977, 6, 12, 18, 0),
                                            {
                                                datetime.timedelta(
                                                    days=-1, seconds=72000
                                                ): (
                                                    datetime.datetime(
                                                        1980, 4, 6, 18, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            days=-1, seconds=72000
                                                        ): (
                                                            datetime.datetime(
                                                                1980, 5, 2, 0, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=72000,
                                                                ): (
                                                                    datetime.datetime(
                                                                        1983, 5, 2, 0, 0
                                                                    ),
                                                                    {
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=72000,
                                                                        ): (
                                                                            datetime.datetime(
                                                                                1985,
                                                                                11,
                                                                                2,
                                                                                18,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=72000,
                                                                                ): (
                                                                                    datetime.datetime(
                                                                                        1991,
                                                                                        3,
                                                                                        31,
                                                                                        18,
                                                                                        0,
                                                                                    ),
                                                                                    {
                                                                                        datetime.timedelta(
                                                                                            days=-1,
                                                                                            seconds=72000,
                                                                                        ): (
                                                                                            datetime.datetime(
                                                                                                2007,
                                                                                                12,
                                                                                                10,
                                                                                                0,
                                                                                                0,
                                                                                            ),
                                                                                            {
                                                                                                datetime.timedelta(
                                                                                                    days=-1,
                                                                                                    seconds=70200,
                                                                                                ): {
                                                                                                    "America/Caracas"
                                                                                                },
                                                                                                datetime.timedelta(
                                                                                                    days=-1,
                                                                                                    seconds=72000,
                                                                                                ): {
                                                                                                    "America/Anguilla",
                                                                                                    "America/Antigua",
                                                                                                    "America/Aruba",
                                                                                                    "America/Blanc-Sablon",
                                                                                                    "America/Curacao",
                                                                                                    "America/Dominica",
                                                                                                    "America/Grenada",
                                                                                                    "America/Guadeloupe",
                                                                                                    "America/Kralendijk",
                                                                                                    "America/La_Paz",
                                                                                                    "America/Lower_Princes",
                                                                                                    "America/Marigot",
                                                                                                    "America/Montserrat",
                                                                                                    "America/Port_of_Spain",
                                                                                                    "America/Puerto_Rico",
                                                                                                    "America/St_Barthelemy",
                                                                                                    "America/St_Kitts",
                                                                                                    "America/St_Lucia",
                                                                                                    "America/St_Thomas",
                                                                                                    "America/St_Vincent",
                                                                                                    "America/Tortola",
                                                                                                    "America/Virgin",
                                                                                                    "Etc/GMT+4",
                                                                                                },
                                                                                            },
                                                                                        ),
                                                                                        datetime.timedelta(
                                                                                            days=-1,
                                                                                            seconds=75600,
                                                                                        ): {
                                                                                            "America/Thule"
                                                                                        },
                                                                                    },
                                                                                ),
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=75600,
                                                                                ): (
                                                                                    datetime.datetime(
                                                                                        1988,
                                                                                        10,
                                                                                        16,
                                                                                        15,
                                                                                        0,
                                                                                    ),
                                                                                    {
                                                                                        datetime.timedelta(
                                                                                            days=-1,
                                                                                            seconds=72000,
                                                                                        ): (
                                                                                            datetime.datetime(
                                                                                                1993,
                                                                                                10,
                                                                                                17,
                                                                                                15,
                                                                                                0,
                                                                                            ),
                                                                                            {
                                                                                                datetime.timedelta(
                                                                                                    days=-1,
                                                                                                    seconds=72000,
                                                                                                ): (
                                                                                                    datetime.datetime(
                                                                                                        1999,
                                                                                                        10,
                                                                                                        3,
                                                                                                        18,
                                                                                                        0,
                                                                                                    ),
                                                                                                    {
                                                                                                        datetime.timedelta(
                                                                                                            days=-1,
                                                                                                            seconds=72000,
                                                                                                        ): (
                                                                                                            datetime.datetime(
                                                                                                                2008,
                                                                                                                6,
                                                                                                                24,
                                                                                                                15,
                                                                                                                0,
                                                                                                            ),
                                                                                                            {
                                                                                                                datetime.timedelta(
                                                                                                                    days=-1,
                                                                                                                    seconds=72000,
                                                                                                                ): {
                                                                                                                    "America/Porto_Velho"
                                                                                                                },
                                                                                                                datetime.timedelta(
                                                                                                                    days=-1,
                                                                                                                    seconds=75600,
                                                                                                                ): {
                                                                                                                    "America/Santarem"
                                                                                                                },
                                                                                                            },
                                                                                                        ),
                                                                                                        datetime.timedelta(
                                                                                                            days=-1,
                                                                                                            seconds=75600,
                                                                                                        ): {
                                                                                                            "America/Boa_Vista"
                                                                                                        },
                                                                                                    },
                                                                                                ),
                                                                                                datetime.timedelta(
                                                                                                    days=-1,
                                                                                                    seconds=75600,
                                                                                                ): {
                                                                                                    "America/Manaus",
                                                                                                    "Brazil/West",
                                                                                                },
                                                                                            },
                                                                                        ),
                                                                                        datetime.timedelta(
                                                                                            days=-1,
                                                                                            seconds=75600,
                                                                                        ): (
                                                                                            datetime.datetime(
                                                                                                2003,
                                                                                                10,
                                                                                                19,
                                                                                                21,
                                                                                                0,
                                                                                            ),
                                                                                            {
                                                                                                datetime.timedelta(
                                                                                                    days=-1,
                                                                                                    seconds=72000,
                                                                                                ): {
                                                                                                    "America/Cuiaba"
                                                                                                },
                                                                                                datetime.timedelta(
                                                                                                    days=-1,
                                                                                                    seconds=75600,
                                                                                                ): {
                                                                                                    "America/Campo_Grande"
                                                                                                },
                                                                                            },
                                                                                        ),
                                                                                    },
                                                                                ),
                                                                            },
                                                                        ),
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=75600,
                                                                        ): {
                                                                            "Atlantic/Stanley"
                                                                        },
                                                                    },
                                                                ),
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=75600,
                                                                ): {"America/Miquelon"},
                                                            },
                                                        ),
                                                        datetime.timedelta(
                                                            days=-1, seconds=75600
                                                        ): {"America/Martinique"},
                                                    },
                                                ),
                                                datetime.timedelta(
                                                    days=-1, seconds=75600
                                                ): {"America/Barbados"},
                                            },
                                        ),
                                        datetime.timedelta(days=-1, seconds=75600): {
                                            "Atlantic/Bermuda"
                                        },
                                    },
                                ),
                                datetime.timedelta(days=-1, seconds=75600): {
                                    "America/Asuncion"
                                },
                            },
                        ),
                        datetime.timedelta(days=-1, seconds=75600): {
                            "America/Glace_Bay"
                        },
                    },
                ),
                datetime.timedelta(days=-1, seconds=75600): (
                    datetime.datetime(1973, 4, 30, 0, 0),
                    {
                        datetime.timedelta(days=-1, seconds=72000): {"America/Moncton"},
                        datetime.timedelta(days=-1, seconds=75600): (
                            datetime.datetime(1988, 4, 3, 15, 0),
                            {
                                datetime.timedelta(days=-1, seconds=75600): {
                                    "America/Halifax",
                                    "Canada/Atlantic",
                                },
                                datetime.timedelta(days=-1, seconds=79200): {
                                    "America/Goose_Bay"
                                },
                            },
                        ),
                    },
                ),
            },
        ),
        datetime.timedelta(days=-1, seconds=72900): {"America/Guyana"},
        datetime.timedelta(days=-1, seconds=73800): (
            datetime.datetime(1970, 4, 26, 15, 0),
            {
                datetime.timedelta(days=-1, seconds=73800): {"America/Paramaribo"},
                datetime.timedelta(days=-1, seconds=77400): {
                    "America/St_Johns",
                    "Canada/Newfoundland",
                },
            },
        ),
        datetime.timedelta(days=-1, seconds=75600): (
            datetime.datetime(1970, 3, 29, 15, 0),
            {
                datetime.timedelta(days=-1, seconds=72000): (
                    datetime.datetime(2017, 5, 14, 18, 0),
                    {
                        datetime.timedelta(days=-1, seconds=72000): {
                            "America/Santiago",
                            "Chile/Continental",
                        },
                        datetime.timedelta(days=-1, seconds=75600): {
                            "America/Punta_Arenas"
                        },
                    },
                ),
                datetime.timedelta(days=-1, seconds=75600): (
                    datetime.datetime(1970, 4, 25, 18, 0),
                    {
                        datetime.timedelta(days=-1, seconds=75600): (
                            datetime.datetime(1974, 1, 23, 21, 0),
                            {
                                datetime.timedelta(days=-1, seconds=75600): (
                                    datetime.datetime(1980, 4, 7, 0, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=75600): (
                                            datetime.datetime(1985, 11, 3, 0, 0),
                                            {
                                                datetime.timedelta(
                                                    days=-1, seconds=75600
                                                ): {"America/Cayenne", "Etc/GMT+3"},
                                                datetime.timedelta(
                                                    days=-1, seconds=79200
                                                ): (
                                                    datetime.datetime(
                                                        1988, 10, 16, 15, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            days=-1, seconds=75600
                                                        ): {"America/Belem"},
                                                        datetime.timedelta(
                                                            days=-1, seconds=79200
                                                        ): (
                                                            datetime.datetime(
                                                                1990, 10, 22, 0, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=75600,
                                                                ): (
                                                                    datetime.datetime(
                                                                        1995,
                                                                        10,
                                                                        15,
                                                                        15,
                                                                        0,
                                                                    ),
                                                                    {
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=75600,
                                                                        ): (
                                                                            datetime.datetime(
                                                                                2000,
                                                                                10,
                                                                                15,
                                                                                12,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=75600,
                                                                                ): {
                                                                                    "America/Recife"
                                                                                },
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=79200,
                                                                                ): {
                                                                                    "America/Fortaleza"
                                                                                },
                                                                            },
                                                                        ),
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=79200,
                                                                        ): (
                                                                            datetime.datetime(
                                                                                1996,
                                                                                10,
                                                                                6,
                                                                                18,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=75600,
                                                                                ): {
                                                                                    "America/Maceio"
                                                                                },
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=79200,
                                                                                ): {
                                                                                    "America/Araguaina"
                                                                                },
                                                                            },
                                                                        ),
                                                                    },
                                                                ),
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=79200,
                                                                ): (
                                                                    datetime.datetime(
                                                                        2003,
                                                                        10,
                                                                        19,
                                                                        21,
                                                                        0,
                                                                    ),
                                                                    {
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=75600,
                                                                        ): {
                                                                            "America/Bahia"
                                                                        },
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=79200,
                                                                        ): {
                                                                            "America/Sao_Paulo",
                                                                            "Brazil/East",
                                                                        },
                                                                    },
                                                                ),
                                                            },
                                                        ),
                                                    },
                                                ),
                                            },
                                        ),
                                        datetime.timedelta(days=-1, seconds=79200): (
                                            datetime.datetime(1996, 1, 2, 0, 0),
                                            {
                                                datetime.timedelta(
                                                    days=-1, seconds=75600
                                                ): {"America/Godthab", "America/Nuuk"},
                                                datetime.timedelta(0): {
                                                    "America/Danmarkshavn"
                                                },
                                            },
                                        ),
                                    },
                                ),
                                datetime.timedelta(days=-1, seconds=79200): (
                                    datetime.datetime(1982, 5, 1, 21, 0),
                                    {
                                        datetime.timedelta(days=-1, seconds=72000): {
                                            "Antarctica/Palmer"
                                        },
                                        datetime.timedelta(days=-1, seconds=75600): (
                                            datetime.datetime(1990, 3, 4, 21, 0),
                                            {
                                                datetime.timedelta(
                                                    days=-1, seconds=72000
                                                ): (
                                                    datetime.datetime(
                                                        1990, 10, 16, 0, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            days=-1, seconds=72000
                                                        ): {
                                                            "America/Argentina/Jujuy",
                                                            "America/Jujuy",
                                                        },
                                                        datetime.timedelta(
                                                            days=-1, seconds=75600
                                                        ): {
                                                            "America/Argentina/Mendoza",
                                                            "America/Mendoza",
                                                        },
                                                    },
                                                ),
                                                datetime.timedelta(
                                                    days=-1, seconds=75600
                                                ): (
                                                    datetime.datetime(
                                                        1991, 3, 1, 15, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            days=-1, seconds=72000
                                                        ): (
                                                            datetime.datetime(
                                                                2004, 6, 20, 18, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=72000,
                                                                ): {
                                                                    "America/Argentina/San_Juan"
                                                                },
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=75600,
                                                                ): {
                                                                    "America/Argentina/La_Rioja"
                                                                },
                                                            },
                                                        ),
                                                        datetime.timedelta(
                                                            days=-1, seconds=79200
                                                        ): (
                                                            datetime.datetime(
                                                                1991, 3, 4, 0, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=72000,
                                                                ): (
                                                                    datetime.datetime(
                                                                        2004,
                                                                        6,
                                                                        1,
                                                                        12,
                                                                        0,
                                                                    ),
                                                                    {
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=72000,
                                                                        ): (
                                                                            datetime.datetime(
                                                                                2004,
                                                                                6,
                                                                                14,
                                                                                0,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=72000,
                                                                                ): {
                                                                                    "America/Argentina/Catamarca",
                                                                                    "America/Argentina/ComodRivadavia",
                                                                                    "America/Catamarca",
                                                                                },
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=75600,
                                                                                ): {
                                                                                    "America/Argentina/Tucuman"
                                                                                },
                                                                            },
                                                                        ),
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=75600,
                                                                        ): (
                                                                            datetime.datetime(
                                                                                2008,
                                                                                10,
                                                                                19,
                                                                                15,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=75600,
                                                                                ): {
                                                                                    "America/Argentina/Salta"
                                                                                },
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=79200,
                                                                                ): {
                                                                                    "America/Argentina/Cordoba",
                                                                                    "America/Cordoba",
                                                                                    "America/Rosario",
                                                                                },
                                                                            },
                                                                        ),
                                                                    },
                                                                ),
                                                                datetime.timedelta(
                                                                    days=-1,
                                                                    seconds=75600,
                                                                ): (
                                                                    datetime.datetime(
                                                                        2004,
                                                                        5,
                                                                        30,
                                                                        15,
                                                                        0,
                                                                    ),
                                                                    {
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=72000,
                                                                        ): {
                                                                            "America/Argentina/Ushuaia"
                                                                        },
                                                                        datetime.timedelta(
                                                                            days=-1,
                                                                            seconds=75600,
                                                                        ): (
                                                                            datetime.datetime(
                                                                                2004,
                                                                                6,
                                                                                1,
                                                                                12,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=72000,
                                                                                ): {
                                                                                    "America/Argentina/Rio_Gallegos"
                                                                                },
                                                                                datetime.timedelta(
                                                                                    days=-1,
                                                                                    seconds=75600,
                                                                                ): {
                                                                                    "America/Argentina/Buenos_Aires",
                                                                                    "America/Buenos_Aires",
                                                                                },
                                                                            },
                                                                        ),
                                                                    },
                                                                ),
                                                            },
                                                        ),
                                                    },
                                                ),
                                                datetime.timedelta(
                                                    days=-1, seconds=79200
                                                ): {"America/Argentina/San_Luis"},
                                            },
                                        ),
                                    },
                                ),
                            },
                        ),
                        datetime.timedelta(days=-1, seconds=79200): {
                            "America/Montevideo"
                        },
                    },
                ),
            },
        ),
        datetime.timedelta(days=-1, seconds=79200): (
            datetime.datetime(1975, 11, 25, 18, 0),
            {
                datetime.timedelta(days=-1, seconds=79200): (
                    datetime.datetime(1980, 4, 6, 21, 0),
                    {
                        datetime.timedelta(days=-1, seconds=79200): (
                            datetime.datetime(1985, 11, 2, 21, 0),
                            {
                                datetime.timedelta(days=-1, seconds=79200): {
                                    "Atlantic/South_Georgia",
                                    "Etc/GMT+2",
                                },
                                datetime.timedelta(days=-1, seconds=82800): {
                                    "America/Noronha",
                                    "Brazil/DeNoronha",
                                },
                            },
                        ),
                        datetime.timedelta(days=-1, seconds=82800): {
                            "America/Scoresbysund"
                        },
                    },
                ),
                datetime.timedelta(days=-1, seconds=82800): {"Atlantic/Cape_Verde"},
            },
        ),
        datetime.timedelta(days=-1, seconds=82800): (
            datetime.datetime(1975, 1, 1, 15, 0),
            {
                datetime.timedelta(days=-1, seconds=82800): (
                    datetime.datetime(1976, 4, 15, 0, 0),
                    {
                        datetime.timedelta(days=-1, seconds=82800): (
                            datetime.datetime(1982, 3, 29, 0, 0),
                            {
                                datetime.timedelta(days=-1, seconds=82800): {
                                    "Etc/GMT+1"
                                },
                                datetime.timedelta(0): {"Atlantic/Azores"},
                            },
                        ),
                        datetime.timedelta(0): {"Africa/El_Aaiun"},
                    },
                ),
                datetime.timedelta(0): {"Africa/Bissau"},
            },
        ),
        datetime.timedelta(days=-1, seconds=83730): {"Africa/Monrovia"},
        datetime.timedelta(days=-1, seconds=83760): {"Africa/Monrovia"},
        datetime.timedelta(0): (
            datetime.datetime(1971, 4, 26, 12, 0),
            {
                datetime.timedelta(0): (
                    datetime.datetime(1974, 6, 25, 0, 0),
                    {
                        datetime.timedelta(0): (
                            datetime.datetime(1976, 12, 1, 15, 0),
                            {
                                datetime.timedelta(days=-1, seconds=75600): {
                                    "Antarctica/Rothera"
                                },
                                datetime.timedelta(0): (
                                    datetime.datetime(1977, 4, 4, 0, 0),
                                    {
                                        datetime.timedelta(0): (
                                            datetime.datetime(1980, 4, 6, 15, 0),
                                            {
                                                datetime.timedelta(0): (
                                                    datetime.datetime(
                                                        1981, 3, 29, 18, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(0): (
                                                            datetime.datetime(
                                                                1982, 4, 4, 15, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(0): (
                                                                    datetime.datetime(
                                                                        2005,
                                                                        3,
                                                                        27,
                                                                        15,
                                                                        0,
                                                                    ),
                                                                    {
                                                                        datetime.timedelta(
                                                                            0
                                                                        ): (
                                                                            datetime.datetime(
                                                                                2018,
                                                                                1,
                                                                                1,
                                                                                18,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    0
                                                                                ): {
                                                                                    "Africa/Abidjan",
                                                                                    "Africa/Accra",
                                                                                    "Africa/Bamako",
                                                                                    "Africa/Banjul",
                                                                                    "Africa/Conakry",
                                                                                    "Africa/Dakar",
                                                                                    "Africa/Freetown",
                                                                                    "Africa/Lome",
                                                                                    "Africa/Nouakchott",
                                                                                    "Africa/Ouagadougou",
                                                                                    "Africa/Timbuktu",
                                                                                    "Atlantic/Reykjavik",
                                                                                    "Atlantic/St_Helena",
                                                                                    "Etc/GMT",
                                                                                    "Etc/GMT+0",
                                                                                    "Etc/GMT-0",
                                                                                    "Etc/GMT0",
                                                                                    "Etc/Greenwich",
                                                                                    "Etc/UCT",
                                                                                    "Etc/UTC",
                                                                                    "Etc/Universal",
                                                                                    "Etc/Zulu",
                                                                                    "GMT",
                                                                                    "GMT+0",
                                                                                    "GMT-0",
                                                                                    "GMT0",
                                                                                    "Greenwich",
                                                                                    "Iceland",
                                                                                    "UCT",
                                                                                    "UTC",
                                                                                    "Universal",
                                                                                    "Zulu",
                                                                                },
                                                                                datetime.timedelta(
                                                                                    seconds=3600
                                                                                ): {
                                                                                    "Africa/Sao_Tome"
                                                                                },
                                                                            },
                                                                        ),
                                                                        datetime.timedelta(
                                                                            seconds=7200
                                                                        ): {
                                                                            "Antarctica/Troll"
                                                                        },
                                                                    },
                                                                ),
                                                                datetime.timedelta(
                                                                    seconds=3600
                                                                ): {"Atlantic/Madeira"},
                                                            },
                                                        ),
                                                        datetime.timedelta(
                                                            seconds=3600
                                                        ): {
                                                            "Atlantic/Faeroe",
                                                            "Atlantic/Faroe",
                                                        },
                                                    },
                                                ),
                                                datetime.timedelta(seconds=3600): {
                                                    "Atlantic/Canary"
                                                },
                                            },
                                        ),
                                        datetime.timedelta(seconds=3600): {"WET"},
                                    },
                                ),
                            },
                        ),
                        datetime.timedelta(seconds=3600): (
                            datetime.datetime(1986, 1, 1, 12, 0),
                            {
                                datetime.timedelta(0): {"Africa/Casablanca"},
                                datetime.timedelta(seconds=3600): {"Africa/Ceuta"},
                            },
                        ),
                    },
                ),
                datetime.timedelta(seconds=3600): {"Africa/Algiers"},
            },
        ),
        datetime.timedelta(seconds=3600): (
            datetime.datetime(1970, 6, 1, 0, 0),
            {
                datetime.timedelta(seconds=3600): (
                    datetime.datetime(1971, 11, 1, 0, 0),
                    {
                        datetime.timedelta(0): {
                            "Eire",
                            "Europe/Belfast",
                            "Europe/Dublin",
                            "Europe/Guernsey",
                            "Europe/Isle_of_Man",
                            "Europe/Jersey",
                            "Europe/London",
                            "GB",
                            "GB-Eire",
                        },
                        datetime.timedelta(seconds=3600): (
                            datetime.datetime(1974, 4, 14, 15, 0),
                            {
                                datetime.timedelta(seconds=3600): (
                                    datetime.datetime(1974, 5, 4, 21, 0),
                                    {
                                        datetime.timedelta(seconds=3600): (
                                            datetime.datetime(1976, 3, 28, 21, 0),
                                            {
                                                datetime.timedelta(seconds=3600): (
                                                    datetime.datetime(
                                                        1976, 9, 26, 21, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(0): (
                                                            datetime.datetime(
                                                                1980, 3, 30, 18, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(0): {
                                                                    "Europe/Lisbon",
                                                                    "Portugal",
                                                                    "WET",
                                                                },
                                                                datetime.timedelta(
                                                                    seconds=3600
                                                                ): {"Portugal"},
                                                            },
                                                        ),
                                                        datetime.timedelta(
                                                            seconds=3600
                                                        ): (
                                                            datetime.datetime(
                                                                1977, 4, 3, 18, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(
                                                                    seconds=3600
                                                                ): (
                                                                    datetime.datetime(
                                                                        1977,
                                                                        4,
                                                                        30,
                                                                        21,
                                                                        0,
                                                                    ),
                                                                    {
                                                                        datetime.timedelta(
                                                                            seconds=3600
                                                                        ): (
                                                                            datetime.datetime(
                                                                                1979,
                                                                                4,
                                                                                1,
                                                                                18,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    seconds=3600
                                                                                ): (
                                                                                    datetime.datetime(
                                                                                        1979,
                                                                                        10,
                                                                                        15,
                                                                                        0,
                                                                                        0,
                                                                                    ),
                                                                                    {
                                                                                        datetime.timedelta(
                                                                                            seconds=3600
                                                                                        ): (
                                                                                            datetime.datetime(
                                                                                                1980,
                                                                                                4,
                                                                                                6,
                                                                                                15,
                                                                                                0,
                                                                                            ),
                                                                                            {
                                                                                                datetime.timedelta(
                                                                                                    seconds=3600
                                                                                                ): (
                                                                                                    datetime.datetime(
                                                                                                        1981,
                                                                                                        3,
                                                                                                        29,
                                                                                                        18,
                                                                                                        0,
                                                                                                    ),
                                                                                                    {
                                                                                                        datetime.timedelta(
                                                                                                            seconds=3600
                                                                                                        ): (
                                                                                                            datetime.datetime(
                                                                                                                1982,
                                                                                                                3,
                                                                                                                28,
                                                                                                                18,
                                                                                                                0,
                                                                                                            ),
                                                                                                            {
                                                                                                                datetime.timedelta(
                                                                                                                    seconds=3600
                                                                                                                ): (
                                                                                                                    datetime.datetime(
                                                                                                                        1983,
                                                                                                                        3,
                                                                                                                        27,
                                                                                                                        18,
                                                                                                                        0,
                                                                                                                    ),
                                                                                                                    {
                                                                                                                        datetime.timedelta(
                                                                                                                            seconds=3600
                                                                                                                        ): (
                                                                                                                            datetime.datetime(
                                                                                                                                1985,
                                                                                                                                4,
                                                                                                                                1,
                                                                                                                                0,
                                                                                                                                0,
                                                                                                                            ),
                                                                                                                            {
                                                                                                                                datetime.timedelta(
                                                                                                                                    seconds=3600
                                                                                                                                ): {
                                                                                                                                    "Africa/Bangui",
                                                                                                                                    "Africa/Brazzaville",
                                                                                                                                    "Africa/Douala",
                                                                                                                                    "Africa/Kinshasa",
                                                                                                                                    "Africa/Lagos",
                                                                                                                                    "Africa/Libreville",
                                                                                                                                    "Africa/Luanda",
                                                                                                                                    "Africa/Malabo",
                                                                                                                                    "Africa/Niamey",
                                                                                                                                    "Africa/Porto-Novo",
                                                                                                                                    "Etc/GMT-1",
                                                                                                                                },
                                                                                                                                datetime.timedelta(
                                                                                                                                    seconds=7200
                                                                                                                                ): {
                                                                                                                                    "Europe/Andorra"
                                                                                                                                },
                                                                                                                            },
                                                                                                                        ),
                                                                                                                        datetime.timedelta(
                                                                                                                            seconds=7200
                                                                                                                        ): {
                                                                                                                            "Europe/Belgrade",
                                                                                                                            "Europe/Ljubljana",
                                                                                                                            "Europe/Podgorica",
                                                                                                                            "Europe/Sarajevo",
                                                                                                                            "Europe/Skopje",
                                                                                                                            "Europe/Zagreb",
                                                                                                                        },
                                                                                                                    },
                                                                                                                ),
                                                                                                                datetime.timedelta(
                                                                                                                    seconds=7200
                                                                                                                ): {
                                                                                                                    "Europe/Gibraltar"
                                                                                                                },
                                                                                                            },
                                                                                                        ),
                                                                                                        datetime.timedelta(
                                                                                                            seconds=7200
                                                                                                        ): {
                                                                                                            "Europe/Busingen",
                                                                                                            "Europe/Vaduz",
                                                                                                            "Europe/Zurich",
                                                                                                        },
                                                                                                    },
                                                                                                ),
                                                                                                datetime.timedelta(
                                                                                                    seconds=7200
                                                                                                ): {
                                                                                                    "Arctic/Longyearbyen",
                                                                                                    "Atlantic/Jan_Mayen",
                                                                                                    "Europe/Berlin",
                                                                                                    "Europe/Budapest",
                                                                                                    "Europe/Copenhagen",
                                                                                                    "Europe/Oslo",
                                                                                                    "Europe/Stockholm",
                                                                                                    "Europe/Vienna",
                                                                                                },
                                                                                            },
                                                                                        ),
                                                                                        datetime.timedelta(
                                                                                            seconds=7200
                                                                                        ): {
                                                                                            "Africa/Ndjamena"
                                                                                        },
                                                                                    },
                                                                                ),
                                                                                datetime.timedelta(
                                                                                    seconds=7200
                                                                                ): {
                                                                                    "Europe/Bratislava",
                                                                                    "Europe/Prague",
                                                                                },
                                                                            },
                                                                        ),
                                                                        datetime.timedelta(
                                                                            seconds=7200
                                                                        ): {
                                                                            "Africa/Tunis"
                                                                        },
                                                                    },
                                                                ),
                                                                datetime.timedelta(
                                                                    seconds=7200
                                                                ): {
                                                                    "CET",
                                                                    "Europe/Amsterdam",
                                                                    "Europe/Brussels",
                                                                    "Europe/Luxembourg",
                                                                    "Europe/Warsaw",
                                                                    "MET",
                                                                    "Poland",
                                                                },
                                                            },
                                                        ),
                                                    },
                                                ),
                                                datetime.timedelta(seconds=7200): {
                                                    "Europe/Monaco",
                                                    "Europe/Paris",
                                                },
                                            },
                                        ),
                                        datetime.timedelta(seconds=7200): {
                                            "Europe/Tirane"
                                        },
                                    },
                                ),
                                datetime.timedelta(seconds=7200): {"Europe/Madrid"},
                            },
                        ),
                    },
                ),
                datetime.timedelta(seconds=7200): (
                    datetime.datetime(1973, 3, 31, 18, 0),
                    {
                        datetime.timedelta(seconds=3600): {
                            "Europe/Rome",
                            "Europe/San_Marino",
                            "Europe/Vatican",
                        },
                        datetime.timedelta(seconds=7200): {"Europe/Malta"},
                    },
                ),
            },
        ),
        datetime.timedelta(seconds=7200): (
            datetime.datetime(1970, 5, 1, 15, 0),
            {
                datetime.timedelta(seconds=7200): (
                    datetime.datetime(1972, 6, 23, 0, 0),
                    {
                        datetime.timedelta(seconds=7200): (
                            datetime.datetime(1973, 6, 3, 15, 0),
                            {
                                datetime.timedelta(seconds=7200): (
                                    datetime.datetime(1973, 6, 6, 21, 0),
                                    {
                                        datetime.timedelta(seconds=7200): (
                                            datetime.datetime(1974, 7, 8, 0, 0),
                                            {
                                                datetime.timedelta(seconds=7200): (
                                                    datetime.datetime(
                                                        1975, 4, 13, 18, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            seconds=7200
                                                        ): (
                                                            datetime.datetime(
                                                                1977, 4, 3, 18, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(
                                                                    seconds=7200
                                                                ): (
                                                                    datetime.datetime(
                                                                        1979,
                                                                        4,
                                                                        1,
                                                                        15,
                                                                        0,
                                                                    ),
                                                                    {
                                                                        datetime.timedelta(
                                                                            seconds=7200
                                                                        ): (
                                                                            datetime.datetime(
                                                                                1979,
                                                                                5,
                                                                                27,
                                                                                15,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    seconds=7200
                                                                                ): (
                                                                                    datetime.datetime(
                                                                                        1981,
                                                                                        3,
                                                                                        29,
                                                                                        15,
                                                                                        0,
                                                                                    ),
                                                                                    {
                                                                                        datetime.timedelta(
                                                                                            seconds=7200
                                                                                        ): (
                                                                                            datetime.datetime(
                                                                                                1982,
                                                                                                1,
                                                                                                1,
                                                                                                21,
                                                                                                0,
                                                                                            ),
                                                                                            {
                                                                                                datetime.timedelta(
                                                                                                    seconds=3600
                                                                                                ): {
                                                                                                    "Africa/Tripoli",
                                                                                                    "Libya",
                                                                                                },
                                                                                                datetime.timedelta(
                                                                                                    seconds=7200
                                                                                                ): (
                                                                                                    datetime.datetime(
                                                                                                        1994,
                                                                                                        3,
                                                                                                        21,
                                                                                                        18,
                                                                                                        0,
                                                                                                    ),
                                                                                                    {
                                                                                                        datetime.timedelta(
                                                                                                            seconds=3600
                                                                                                        ): {
                                                                                                            "Africa/Windhoek"
                                                                                                        },
                                                                                                        datetime.timedelta(
                                                                                                            seconds=7200
                                                                                                        ): {
                                                                                                            "Africa/Blantyre",
                                                                                                            "Africa/Bujumbura",
                                                                                                            "Africa/Gaborone",
                                                                                                            "Africa/Harare",
                                                                                                            "Africa/Johannesburg",
                                                                                                            "Africa/Kigali",
                                                                                                            "Africa/Lubumbashi",
                                                                                                            "Africa/Lusaka",
                                                                                                            "Africa/Maputo",
                                                                                                            "Africa/Maseru",
                                                                                                            "Africa/Mbabane",
                                                                                                            "Etc/GMT-2",
                                                                                                        },
                                                                                                    },
                                                                                                ),
                                                                                            },
                                                                                        ),
                                                                                        datetime.timedelta(
                                                                                            seconds=10800
                                                                                        ): {
                                                                                            "Europe/Helsinki",
                                                                                            "Europe/Mariehamn",
                                                                                        },
                                                                                    },
                                                                                ),
                                                                                datetime.timedelta(
                                                                                    seconds=10800
                                                                                ): {
                                                                                    "Europe/Bucharest"
                                                                                },
                                                                            },
                                                                        ),
                                                                        datetime.timedelta(
                                                                            seconds=10800
                                                                        ): {
                                                                            "Europe/Sofia"
                                                                        },
                                                                    },
                                                                ),
                                                                datetime.timedelta(
                                                                    seconds=10800
                                                                ): {"EET"},
                                                            },
                                                        ),
                                                        datetime.timedelta(
                                                            seconds=10800
                                                        ): (
                                                            datetime.datetime(
                                                                1975, 10, 12, 18, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(
                                                                    seconds=7200
                                                                ): (
                                                                    datetime.datetime(
                                                                        2016,
                                                                        10,
                                                                        31,
                                                                        3,
                                                                        0,
                                                                    ),
                                                                    {
                                                                        datetime.timedelta(
                                                                            seconds=7200
                                                                        ): {
                                                                            "Asia/Nicosia",
                                                                            "Europe/Nicosia",
                                                                        },
                                                                        datetime.timedelta(
                                                                            seconds=10800
                                                                        ): {
                                                                            "Asia/Famagusta"
                                                                        },
                                                                    },
                                                                ),
                                                                datetime.timedelta(
                                                                    seconds=10800
                                                                ): {
                                                                    "EET",
                                                                    "Europe/Athens",
                                                                },
                                                            },
                                                        ),
                                                    },
                                                ),
                                                datetime.timedelta(seconds=10800): (
                                                    datetime.datetime(
                                                        1996, 3, 15, 21, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            seconds=7200
                                                        ): (
                                                            datetime.datetime(
                                                                2008, 8, 29, 18, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(
                                                                    seconds=7200
                                                                ): {"Asia/Gaza"},
                                                                datetime.timedelta(
                                                                    seconds=10800
                                                                ): {"Asia/Hebron"},
                                                            },
                                                        ),
                                                        datetime.timedelta(
                                                            seconds=10800
                                                        ): {
                                                            "Asia/Jerusalem",
                                                            "Asia/Tel_Aviv",
                                                            "Israel",
                                                        },
                                                    },
                                                ),
                                            },
                                        ),
                                        datetime.timedelta(seconds=10800): {
                                            "Asia/Amman"
                                        },
                                    },
                                ),
                                datetime.timedelta(seconds=10800): {
                                    "Asia/Istanbul",
                                    "Europe/Istanbul",
                                    "Turkey",
                                },
                            },
                        ),
                        datetime.timedelta(seconds=10800): {"Asia/Beirut"},
                    },
                ),
                datetime.timedelta(seconds=10800): (
                    datetime.datetime(1970, 10, 1, 18, 0),
                    {
                        datetime.timedelta(seconds=7200): (
                            datetime.datetime(1977, 9, 2, 0, 0),
                            {
                                datetime.timedelta(seconds=7200): {"Asia/Damascus"},
                                datetime.timedelta(seconds=10800): {
                                    "Africa/Cairo",
                                    "Egypt",
                                },
                            },
                        ),
                        datetime.timedelta(seconds=10800): (
                            datetime.datetime(2017, 11, 1, 18, 0),
                            {
                                datetime.timedelta(seconds=7200): {"Africa/Khartoum"},
                                datetime.timedelta(seconds=10800): {"Africa/Juba"},
                            },
                        ),
                    },
                ),
            },
        ),
        datetime.timedelta(seconds=10800): (
            datetime.datetime(1981, 4, 1, 15, 0),
            {
                datetime.timedelta(seconds=10800): (
                    datetime.datetime(1982, 5, 1, 18, 0),
                    {
                        datetime.timedelta(seconds=10800): {
                            "Africa/Addis_Ababa",
                            "Africa/Asmara",
                            "Africa/Asmera",
                            "Africa/Dar_es_Salaam",
                            "Africa/Djibouti",
                            "Africa/Kampala",
                            "Africa/Mogadishu",
                            "Africa/Nairobi",
                            "Antarctica/Syowa",
                            "Asia/Aden",
                            "Asia/Kuwait",
                            "Asia/Riyadh",
                            "Etc/GMT-3",
                            "Indian/Antananarivo",
                            "Indian/Comoro",
                            "Indian/Mayotte",
                        },
                        datetime.timedelta(seconds=14400): {"Asia/Baghdad"},
                    },
                ),
                datetime.timedelta(seconds=14400): (
                    datetime.datetime(1989, 3, 26, 21, 0),
                    {
                        datetime.timedelta(seconds=10800): (
                            datetime.datetime(1996, 9, 29, 15, 0),
                            {
                                datetime.timedelta(seconds=7200): {"Europe/Riga"},
                                datetime.timedelta(seconds=10800): (
                                    datetime.datetime(1998, 3, 29, 15, 0),
                                    {
                                        datetime.timedelta(seconds=7200): {
                                            "Europe/Vilnius"
                                        },
                                        datetime.timedelta(seconds=10800): (
                                            datetime.datetime(2000, 3, 26, 21, 0),
                                            {
                                                datetime.timedelta(seconds=7200): {
                                                    "Europe/Tallinn"
                                                },
                                                datetime.timedelta(seconds=10800): {
                                                    "Europe/Kaliningrad"
                                                },
                                            },
                                        ),
                                    },
                                ),
                            },
                        ),
                        datetime.timedelta(seconds=14400): (
                            datetime.datetime(1990, 3, 25, 21, 0),
                            {
                                datetime.timedelta(seconds=10800): (
                                    datetime.datetime(1990, 7, 2, 0, 0),
                                    {
                                        datetime.timedelta(seconds=7200): {
                                            "Europe/Simferopol"
                                        },
                                        datetime.timedelta(seconds=10800): {
                                            "Europe/Minsk"
                                        },
                                    },
                                ),
                                datetime.timedelta(seconds=14400): (
                                    datetime.datetime(1990, 5, 6, 15, 0),
                                    {
                                        datetime.timedelta(seconds=10800): {
                                            "Europe/Chisinau",
                                            "Europe/Tiraspol",
                                        },
                                        datetime.timedelta(seconds=14400): (
                                            datetime.datetime(1990, 7, 1, 15, 0),
                                            {
                                                datetime.timedelta(seconds=10800): {
                                                    "Europe/Kiev",
                                                    "Europe/Kyiv",
                                                    "Europe/Uzhgorod",
                                                    "Europe/Zaporozhye",
                                                },
                                                datetime.timedelta(seconds=14400): {
                                                    "Europe/Moscow",
                                                    "W-SU",
                                                },
                                            },
                                        ),
                                    },
                                ),
                            },
                        ),
                    },
                ),
            },
        ),
        datetime.timedelta(seconds=12600): {"Iran", "Asia/Tehran"},
        datetime.timedelta(seconds=14400): (
            datetime.datetime(1972, 6, 1, 21, 0),
            {
                datetime.timedelta(seconds=10800): {"Asia/Bahrain", "Asia/Qatar"},
                datetime.timedelta(seconds=14400): (
                    datetime.datetime(1981, 4, 1, 15, 0),
                    {
                        datetime.timedelta(seconds=14400): (
                            datetime.datetime(1982, 10, 11, 0, 0),
                            {
                                datetime.timedelta(seconds=14400): {
                                    "Asia/Dubai",
                                    "Asia/Muscat",
                                    "Etc/GMT-4",
                                    "Indian/Mahe",
                                    "Indian/Reunion",
                                },
                                datetime.timedelta(seconds=18000): {"Indian/Mauritius"},
                            },
                        ),
                        datetime.timedelta(seconds=18000): (
                            datetime.datetime(1988, 3, 27, 21, 0),
                            {
                                datetime.timedelta(seconds=14400): (
                                    datetime.datetime(2016, 12, 4, 18, 0),
                                    {
                                        datetime.timedelta(seconds=10800): {
                                            "Europe/Volgograd"
                                        },
                                        datetime.timedelta(seconds=14400): {
                                            "Europe/Saratov"
                                        },
                                    },
                                ),
                                datetime.timedelta(seconds=18000): (
                                    datetime.datetime(1989, 3, 26, 21, 0),
                                    {
                                        datetime.timedelta(seconds=14400): (
                                            datetime.datetime(1991, 3, 31, 18, 0),
                                            {
                                                datetime.timedelta(seconds=10800): (
                                                    datetime.datetime(
                                                        1991, 9, 29, 18, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            seconds=7200
                                                        ): {"Europe/Ulyanovsk"},
                                                        datetime.timedelta(
                                                            seconds=10800
                                                        ): {"Europe/Samara"},
                                                    },
                                                ),
                                                datetime.timedelta(seconds=14400): (
                                                    datetime.datetime(
                                                        2016, 3, 27, 18, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            seconds=10800
                                                        ): {"Europe/Kirov"},
                                                        datetime.timedelta(
                                                            seconds=14400
                                                        ): {"Europe/Astrakhan"},
                                                    },
                                                ),
                                            },
                                        ),
                                        datetime.timedelta(seconds=18000): (
                                            datetime.datetime(1992, 9, 27, 18, 0),
                                            {
                                                datetime.timedelta(seconds=10800): (
                                                    datetime.datetime(
                                                        1994, 9, 25, 18, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            seconds=10800
                                                        ): {"Asia/Yerevan"},
                                                        datetime.timedelta(
                                                            seconds=14400
                                                        ): {"Asia/Tbilisi"},
                                                    },
                                                ),
                                                datetime.timedelta(seconds=14400): {
                                                    "Asia/Baku"
                                                },
                                            },
                                        ),
                                    },
                                ),
                            },
                        ),
                    },
                ),
            },
        ),
        datetime.timedelta(seconds=16200): {"Asia/Kabul"},
        datetime.timedelta(seconds=18000): (
            datetime.datetime(1981, 4, 1, 15, 0),
            {
                datetime.timedelta(seconds=18000): (
                    datetime.datetime(1981, 10, 2, 0, 0),
                    {
                        datetime.timedelta(seconds=18000): (
                            datetime.datetime(1996, 1, 1, 21, 0),
                            {
                                datetime.timedelta(seconds=18000): (
                                    datetime.datetime(2002, 4, 8, 0, 0),
                                    {
                                        datetime.timedelta(seconds=18000): {
                                            "Etc/GMT-5",
                                            "Indian/Kerguelen",
                                            "Indian/Maldives",
                                        },
                                        datetime.timedelta(seconds=21600): {
                                            "Asia/Karachi"
                                        },
                                    },
                                ),
                                datetime.timedelta(seconds=21600): {"Indian/Chagos"},
                            },
                        ),
                        datetime.timedelta(seconds=21600): (
                            datetime.datetime(1994, 9, 26, 3, 0),
                            {
                                datetime.timedelta(seconds=14400): {"Asia/Aqtau"},
                                datetime.timedelta(seconds=18000): {"Asia/Atyrau"},
                            },
                        ),
                    },
                ),
                datetime.timedelta(seconds=21600): (
                    datetime.datetime(1981, 10, 1, 12, 0),
                    {
                        datetime.timedelta(seconds=18000): (
                            datetime.datetime(1992, 3, 29, 18, 0),
                            {
                                datetime.timedelta(seconds=18000): {
                                    "Asia/Ashgabat",
                                    "Asia/Ashkhabad",
                                },
                                datetime.timedelta(seconds=21600): {
                                    "Asia/Yekaterinburg"
                                },
                            },
                        ),
                        datetime.timedelta(seconds=21600): (
                            datetime.datetime(1989, 3, 26, 15, 0),
                            {
                                datetime.timedelta(seconds=18000): {"Asia/Oral"},
                                datetime.timedelta(seconds=21600): (
                                    datetime.datetime(1991, 4, 1, 0, 0),
                                    {
                                        datetime.timedelta(seconds=18000): (
                                            datetime.datetime(1991, 9, 30, 0, 0),
                                            {
                                                datetime.timedelta(seconds=14400): (
                                                    datetime.datetime(
                                                        2004, 10, 31, 18, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            seconds=18000
                                                        ): {"Asia/Aqtobe"},
                                                        datetime.timedelta(
                                                            seconds=21600
                                                        ): {"Asia/Qostanay"},
                                                    },
                                                ),
                                                datetime.timedelta(seconds=18000): {
                                                    "Asia/Qyzylorda"
                                                },
                                            },
                                        ),
                                        datetime.timedelta(seconds=21600): {
                                            "Asia/Samarkand"
                                        },
                                    },
                                ),
                            },
                        ),
                    },
                ),
            },
        ),
        datetime.timedelta(seconds=19800): (
            datetime.datetime(1986, 1, 1, 18, 0),
            {
                datetime.timedelta(seconds=19800): (
                    datetime.datetime(1987, 10, 1, 18, 0),
                    {
                        datetime.timedelta(seconds=19800): (
                            datetime.datetime(1996, 5, 25, 15, 0),
                            {
                                datetime.timedelta(seconds=19800): {
                                    "Asia/Calcutta",
                                    "Asia/Kolkata",
                                },
                                datetime.timedelta(seconds=23400): {"Asia/Colombo"},
                            },
                        ),
                        datetime.timedelta(seconds=21600): {
                            "Asia/Thimbu",
                            "Asia/Thimphu",
                        },
                    },
                ),
                datetime.timedelta(seconds=20700): {"Asia/Kathmandu", "Asia/Katmandu"},
            },
        ),
        datetime.timedelta(seconds=21600): (
            datetime.datetime(1978, 1, 2, 0, 0),
            {
                datetime.timedelta(seconds=21600): (
                    datetime.datetime(1981, 4, 1, 18, 0),
                    {
                        datetime.timedelta(seconds=21600): (
                            datetime.datetime(2009, 6, 20, 18, 0),
                            {
                                datetime.timedelta(seconds=21600): (
                                    datetime.datetime(2009, 10, 18, 21, 0),
                                    {
                                        datetime.timedelta(seconds=18000): {
                                            "Antarctica/Mawson"
                                        },
                                        datetime.timedelta(seconds=21600): {
                                            "Asia/Kashgar",
                                            "Asia/Urumqi",
                                            "Etc/GMT-6",
                                        },
                                    },
                                ),
                                datetime.timedelta(seconds=25200): {
                                    "Asia/Dacca",
                                    "Asia/Dhaka",
                                },
                            },
                        ),
                        datetime.timedelta(seconds=25200): (
                            datetime.datetime(1991, 8, 31, 21, 0),
                            {
                                datetime.timedelta(seconds=18000): {"Asia/Bishkek"},
                                datetime.timedelta(seconds=21600): (
                                    datetime.datetime(1991, 9, 9, 15, 0),
                                    {
                                        datetime.timedelta(seconds=18000): {
                                            "Asia/Dushanbe"
                                        },
                                        datetime.timedelta(seconds=21600): (
                                            datetime.datetime(1992, 1, 19, 18, 0),
                                            {
                                                datetime.timedelta(seconds=18000): {
                                                    "Asia/Tashkent"
                                                },
                                                datetime.timedelta(seconds=21600): (
                                                    datetime.datetime(
                                                        2005, 3, 27, 15, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            seconds=21600
                                                        ): {"Asia/Almaty"},
                                                        datetime.timedelta(
                                                            seconds=25200
                                                        ): {"Asia/Omsk"},
                                                    },
                                                ),
                                            },
                                        ),
                                    },
                                ),
                            },
                        ),
                    },
                ),
                datetime.timedelta(seconds=25200): {"Asia/Hovd"},
            },
        ),
        datetime.timedelta(seconds=23400): {
            "Asia/Rangoon",
            "Asia/Yangon",
            "Indian/Cocos",
        },
        datetime.timedelta(seconds=25200): (
            datetime.datetime(1978, 1, 2, 0, 0),
            {
                datetime.timedelta(seconds=25200): (
                    datetime.datetime(1981, 4, 1, 18, 0),
                    {
                        datetime.timedelta(seconds=25200): (
                            datetime.datetime(1994, 2, 1, 12, 0),
                            {
                                datetime.timedelta(0): {"Antarctica/Vostok"},
                                datetime.timedelta(seconds=25200): (
                                    datetime.datetime(2009, 10, 18, 21, 0),
                                    {
                                        datetime.timedelta(seconds=18000): {
                                            "Antarctica/Davis"
                                        },
                                        datetime.timedelta(seconds=25200): {
                                            "Asia/Bangkok",
                                            "Asia/Jakarta",
                                            "Asia/Phnom_Penh",
                                            "Asia/Vientiane",
                                            "Etc/GMT-7",
                                            "Indian/Christmas",
                                        },
                                    },
                                ),
                            },
                        ),
                        datetime.timedelta(seconds=28800): (
                            datetime.datetime(1993, 5, 23, 18, 0),
                            {
                                datetime.timedelta(seconds=25200): {"Asia/Novosibirsk"},
                                datetime.timedelta(seconds=28800): (
                                    datetime.datetime(1995, 5, 28, 18, 0),
                                    {
                                        datetime.timedelta(seconds=25200): {
                                            "Asia/Barnaul"
                                        },
                                        datetime.timedelta(seconds=28800): (
                                            datetime.datetime(2002, 5, 2, 0, 0),
                                            {
                                                datetime.timedelta(seconds=25200): {
                                                    "Asia/Tomsk"
                                                },
                                                datetime.timedelta(seconds=28800): (
                                                    datetime.datetime(
                                                        2010, 3, 28, 15, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            seconds=25200
                                                        ): {"Asia/Novokuznetsk"},
                                                        datetime.timedelta(
                                                            seconds=28800
                                                        ): {"Asia/Krasnoyarsk"},
                                                    },
                                                ),
                                            },
                                        ),
                                    },
                                ),
                            },
                        ),
                    },
                ),
                datetime.timedelta(seconds=28800): {
                    "Asia/Choibalsan",
                    "Asia/Ulaanbaatar",
                    "Asia/Ulan_Bator",
                },
            },
        ),
        datetime.timedelta(seconds=27000): {
            "Asia/Kuala_Lumpur",
            "Asia/Singapore",
            "Singapore",
        },
        datetime.timedelta(seconds=28800): (
            datetime.datetime(1970, 4, 19, 18, 0),
            {
                datetime.timedelta(seconds=28800): (
                    datetime.datetime(1974, 4, 1, 15, 0),
                    {
                        datetime.timedelta(seconds=28800): (
                            datetime.datetime(1974, 10, 27, 15, 0),
                            {
                                datetime.timedelta(seconds=28800): (
                                    datetime.datetime(1975, 6, 13, 21, 0),
                                    {
                                        datetime.timedelta(seconds=25200): {
                                            "Asia/Ho_Chi_Minh",
                                            "Asia/Saigon",
                                        },
                                        datetime.timedelta(seconds=28800): (
                                            datetime.datetime(1978, 3, 22, 21, 0),
                                            {
                                                datetime.timedelta(seconds=28800): (
                                                    datetime.datetime(
                                                        1981, 4, 1, 21, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            seconds=28800
                                                        ): (
                                                            datetime.datetime(
                                                                1986, 5, 4, 21, 0
                                                            ),
                                                            {
                                                                datetime.timedelta(
                                                                    seconds=28800
                                                                ): (
                                                                    datetime.datetime(
                                                                        1988,
                                                                        1,
                                                                        1,
                                                                        18,
                                                                        0,
                                                                    ),
                                                                    {
                                                                        datetime.timedelta(
                                                                            seconds=25200
                                                                        ): {
                                                                            "Asia/Pontianak"
                                                                        },
                                                                        datetime.timedelta(
                                                                            seconds=28800
                                                                        ): (
                                                                            datetime.datetime(
                                                                                2009,
                                                                                10,
                                                                                19,
                                                                                3,
                                                                                0,
                                                                            ),
                                                                            {
                                                                                datetime.timedelta(
                                                                                    seconds=28800
                                                                                ): {
                                                                                    "Asia/Brunei",
                                                                                    "Asia/Kuching",
                                                                                    "Asia/Makassar",
                                                                                    "Asia/Ujung_Pandang",
                                                                                    "Etc/GMT-8",
                                                                                },
                                                                                datetime.timedelta(
                                                                                    seconds=39600
                                                                                ): {
                                                                                    "Antarctica/Casey"
                                                                                },
                                                                            },
                                                                        ),
                                                                    },
                                                                ),
                                                                datetime.timedelta(
                                                                    seconds=32400
                                                                ): {
                                                                    "Asia/Chongqing",
                                                                    "Asia/Chungking",
                                                                    "Asia/Harbin",
                                                                    "Asia/Shanghai",
                                                                    "PRC",
                                                                },
                                                            },
                                                        ),
                                                        datetime.timedelta(
                                                            seconds=32400
                                                        ): {"Asia/Irkutsk"},
                                                    },
                                                ),
                                                datetime.timedelta(seconds=32400): {
                                                    "Asia/Manila"
                                                },
                                            },
                                        ),
                                    },
                                ),
                                datetime.timedelta(seconds=32400): {
                                    "Australia/Perth",
                                    "Australia/West",
                                },
                            },
                        ),
                        datetime.timedelta(seconds=32400): {"Asia/Taipei", "ROC"},
                    },
                ),
                datetime.timedelta(seconds=32400): {
                    "Asia/Hong_Kong",
                    "Asia/Macao",
                    "Asia/Macau",
                    "Hongkong",
                },
            },
        ),
        datetime.timedelta(seconds=31500): {"Australia/Eucla"},
        datetime.timedelta(seconds=32400): (
            datetime.datetime(1976, 5, 3, 18, 0),
            {
                datetime.timedelta(seconds=28800): {"Asia/Dili"},
                datetime.timedelta(seconds=32400): (
                    datetime.datetime(1981, 4, 1, 18, 0),
                    {
                        datetime.timedelta(seconds=32400): (
                            datetime.datetime(1987, 5, 10, 15, 0),
                            {
                                datetime.timedelta(seconds=32400): (
                                    datetime.datetime(2015, 8, 15, 15, 0),
                                    {
                                        datetime.timedelta(seconds=30600): {
                                            "Asia/Pyongyang"
                                        },
                                        datetime.timedelta(seconds=32400): {
                                            "Asia/Jayapura",
                                            "Asia/Tokyo",
                                            "Etc/GMT-9",
                                            "Japan",
                                            "Pacific/Palau",
                                        },
                                    },
                                ),
                                datetime.timedelta(seconds=36000): {
                                    "Asia/Seoul",
                                    "ROK",
                                },
                            },
                        ),
                        datetime.timedelta(seconds=36000): (
                            datetime.datetime(2004, 1, 1, 21, 0),
                            {
                                datetime.timedelta(seconds=32400): (
                                    datetime.datetime(2014, 10, 27, 0, 0),
                                    {
                                        datetime.timedelta(seconds=28800): {
                                            "Asia/Chita"
                                        },
                                        datetime.timedelta(seconds=32400): {
                                            "Asia/Yakutsk"
                                        },
                                    },
                                ),
                                datetime.timedelta(seconds=36000): {"Asia/Khandyga"},
                            },
                        ),
                        datetime.timedelta(seconds=43200): {"Asia/Ust-Nera"},
                    },
                ),
            },
        ),
        datetime.timedelta(seconds=34200): (
            datetime.datetime(1971, 10, 31, 21, 0),
            {
                datetime.timedelta(seconds=34200): {
                    "Australia/Darwin",
                    "Australia/North",
                },
                datetime.timedelta(seconds=37800): (
                    datetime.datetime(1982, 3, 7, 18, 0),
                    {
                        datetime.timedelta(seconds=34200): {
                            "Australia/Adelaide",
                            "Australia/South",
                        },
                        datetime.timedelta(seconds=37800): {
                            "Australia/Broken_Hill",
                            "Australia/Yancowinna",
                        },
                    },
                ),
            },
        ),
        datetime.timedelta(seconds=36000): (
            datetime.datetime(1970, 4, 26, 15, 0),
            {
                datetime.timedelta(seconds=36000): (
                    datetime.datetime(1971, 10, 31, 21, 0),
                    {
                        datetime.timedelta(seconds=36000): (
                            datetime.datetime(1981, 3, 2, 0, 0),
                            {
                                datetime.timedelta(seconds=36000): (
                                    datetime.datetime(1981, 4, 1, 18, 0),
                                    {
                                        datetime.timedelta(seconds=36000): (
                                            datetime.datetime(2014, 12, 28, 21, 0),
                                            {
                                                datetime.timedelta(seconds=36000): {
                                                    "Antarctica/DumontDUrville",
                                                    "Etc/GMT-10",
                                                    "Pacific/Chuuk",
                                                    "Pacific/Port_Moresby",
                                                    "Pacific/Truk",
                                                    "Pacific/Yap",
                                                },
                                                datetime.timedelta(seconds=39600): {
                                                    "Pacific/Bougainville"
                                                },
                                            },
                                        ),
                                        datetime.timedelta(seconds=39600): {
                                            "Asia/Vladivostok"
                                        },
                                    },
                                ),
                                datetime.timedelta(seconds=37800): {
                                    "Australia/LHI",
                                    "Australia/Lord_Howe",
                                },
                            },
                        ),
                        datetime.timedelta(seconds=39600): (
                            datetime.datetime(1972, 10, 29, 21, 0),
                            {
                                datetime.timedelta(seconds=36000): (
                                    datetime.datetime(1992, 10, 26, 0, 0),
                                    {
                                        datetime.timedelta(seconds=36000): {
                                            "Australia/Brisbane",
                                            "Australia/Queensland",
                                        },
                                        datetime.timedelta(seconds=39600): {
                                            "Australia/Lindeman"
                                        },
                                    },
                                ),
                                datetime.timedelta(seconds=39600): (
                                    datetime.datetime(1982, 3, 7, 18, 0),
                                    {
                                        datetime.timedelta(seconds=36000): {
                                            "Australia/Melbourne",
                                            "Australia/Victoria",
                                        },
                                        datetime.timedelta(seconds=39600): {
                                            "Australia/ACT",
                                            "Australia/Canberra",
                                            "Australia/NSW",
                                            "Australia/Sydney",
                                        },
                                    },
                                ),
                            },
                        ),
                    },
                ),
                datetime.timedelta(seconds=39600): {"Pacific/Guam", "Pacific/Saipan"},
            },
        ),
        datetime.timedelta(seconds=39600): (
            datetime.datetime(1970, 3, 8, 15, 0),
            {
                datetime.timedelta(seconds=36000): (
                    datetime.datetime(2010, 4, 4, 21, 0),
                    {
                        datetime.timedelta(seconds=36000): {
                            "Australia/Currie",
                            "Australia/Hobart",
                            "Australia/Tasmania",
                        },
                        datetime.timedelta(seconds=39600): {"Antarctica/Macquarie"},
                    },
                ),
                datetime.timedelta(seconds=39600): (
                    datetime.datetime(1973, 12, 23, 15, 0),
                    {
                        datetime.timedelta(seconds=39600): (
                            datetime.datetime(1977, 12, 4, 15, 0),
                            {
                                datetime.timedelta(seconds=39600): (
                                    datetime.datetime(1981, 4, 1, 18, 0),
                                    {
                                        datetime.timedelta(seconds=39600): {
                                            "Etc/GMT-11",
                                            "Pacific/Guadalcanal",
                                            "Pacific/Pohnpei",
                                            "Pacific/Ponape",
                                        },
                                        datetime.timedelta(seconds=43200): (
                                            datetime.datetime(1997, 3, 30, 15, 0),
                                            {
                                                datetime.timedelta(seconds=39600): {
                                                    "Asia/Sakhalin"
                                                },
                                                datetime.timedelta(seconds=43200): (
                                                    datetime.datetime(
                                                        2014, 10, 26, 18, 0
                                                    ),
                                                    {
                                                        datetime.timedelta(
                                                            seconds=36000
                                                        ): {"Asia/Magadan"},
                                                        datetime.timedelta(
                                                            seconds=39600
                                                        ): {"Asia/Srednekolymsk"},
                                                    },
                                                ),
                                            },
                                        ),
                                    },
                                ),
                                datetime.timedelta(seconds=43200): {"Pacific/Noumea"},
                            },
                        ),
                        datetime.timedelta(seconds=43200): {"Pacific/Efate"},
                    },
                ),
            },
        ),
        datetime.timedelta(seconds=41400): (
            datetime.datetime(1974, 10, 27, 15, 0),
            {
                datetime.timedelta(seconds=41400): {"Pacific/Nauru"},
                datetime.timedelta(seconds=45000): {"Pacific/Norfolk"},
            },
        ),
        datetime.timedelta(seconds=43200): (
            datetime.datetime(1974, 11, 3, 21, 0),
            {
                datetime.timedelta(seconds=43200): (
                    datetime.datetime(1981, 4, 1, 15, 0),
                    {
                        datetime.timedelta(seconds=43200): (
                            datetime.datetime(1998, 11, 1, 18, 0),
                            {
                                datetime.timedelta(seconds=43200): (
                                    datetime.datetime(1999, 1, 1, 18, 0),
                                    {
                                        datetime.timedelta(seconds=39600): {
                                            "Pacific/Kosrae"
                                        },
                                        datetime.timedelta(seconds=43200): {
                                            "Etc/GMT-12",
                                            "Pacific/Funafuti",
                                            "Pacific/Majuro",
                                            "Pacific/Tarawa",
                                            "Pacific/Wake",
                                            "Pacific/Wallis",
                                        },
                                    },
                                ),
                                datetime.timedelta(seconds=46800): {"Pacific/Fiji"},
                            },
                        ),
                        datetime.timedelta(seconds=46800): {"Asia/Kamchatka"},
                    },
                ),
                datetime.timedelta(seconds=46800): {
                    "Antarctica/McMurdo",
                    "Antarctica/South_Pole",
                    "NZ",
                    "Pacific/Auckland",
                },
            },
        ),
        datetime.timedelta(seconds=45900): {"NZ-CHAT", "Pacific/Chatham"},
        datetime.timedelta(seconds=46800): (
            datetime.datetime(1981, 4, 1, 15, 0),
            {
                datetime.timedelta(seconds=46800): (
                    datetime.datetime(1999, 10, 7, 21, 0),
                    {
                        datetime.timedelta(seconds=46800): {"Etc/GMT-13"},
                        datetime.timedelta(seconds=50400): {"Pacific/Tongatapu"},
                    },
                ),
                datetime.timedelta(seconds=50400): {"Asia/Anadyr"},
            },
        ),
        datetime.timedelta(seconds=50400): {"Etc/GMT-14"},
    },
)


__all__ = ["lookup"]
